version: '3.8'

services:
  mongodb:
    image: mongo:7.0
    container_name: quizflow-mongodb
    restart: unless-stopped
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: root
      MONGO_INITDB_ROOT_PASSWORD: password
      MONGO_INITDB_DATABASE: quizflow
    volumes:
      - mongodb_data:/data/db
      - ./mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - quizflow-network

volumes:
  mongodb_data:

networks:
  quizflow-network:
    driver: bridge
