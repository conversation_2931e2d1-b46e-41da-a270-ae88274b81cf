import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";

interface RouteParams {
  params: {
    id: string;
  };
}

// Get all questions for a quiz
export async function GET(req: Request, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions);
    const quizId = params.id;

    // Verify access
    const quiz = await db.quiz.findUnique({
      where: {
        id: quizId,
      },
    });

    if (!quiz) {
      return NextResponse.json(
        { message: "Quiz not found" },
        { status: 404 }
      );
    }

    // If quiz is not published, only the creator can view it
    if (!quiz.isPublished && (!session || quiz.creatorId !== session.user.id)) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const questions = await db.question.findMany({
      where: {
        quizId: quizId,
      },
      orderBy: {
        createdAt: "asc",
      },
    });

    return NextResponse.json(questions);
  } catch (error) {
    console.error("Error fetching questions:", error);
    return NextResponse.json(
      { message: "An error occurred while fetching questions" },
      { status: 500 }
    );
  }
}

// Add a new question to a quiz
export async function POST(req: Request, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const quizId = params.id;
    const body = await req.json();

    // Verify ownership
    const quiz = await db.quiz.findUnique({
      where: {
        id: quizId,
        creatorId: session.user.id,
      },
    });

    if (!quiz) {
      return NextResponse.json(
        { message: "Quiz not found or you don't have permission to edit it" },
        { status: 404 }
      );
    }

    // Process the question data
    let questionData = { ...body };
    
    // Convert text to JSON if it's not already a string
    if (typeof questionData.text !== "string") {
      questionData.text = JSON.stringify(questionData.text);
    }
    
    // Convert other JSON fields
    for (const field of ["options", "correctAnswer", "correctAnswers", "stems", "correctPairs", "textTemplate", "blanks"]) {
      if (questionData[field] && typeof questionData[field] !== "string") {
        questionData[field] = JSON.stringify(questionData[field]);
      }
    }

    // Add the question
    const question = await db.question.create({
      data: {
        ...questionData,
        quizId: quizId,
      },
    });

    return NextResponse.json(question, { status: 201 });
  } catch (error) {
    console.error("Error adding question:", error);
    return NextResponse.json(
      { message: "An error occurred while adding the question" },
      { status: 500 }
    );
  }
}
