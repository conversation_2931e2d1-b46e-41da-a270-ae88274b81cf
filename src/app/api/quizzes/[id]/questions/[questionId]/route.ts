import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";

interface RouteParams {
  params: {
    id: string;
    questionId: string;
  };
}

// Get a specific question
export async function GET(req: Request, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions);
    const { id: quizId, questionId } = await params;

    const question = await db.question.findUnique({
      where: {
        id: questionId,
        quizId: quizId,
      },
    });

    if (!question) {
      return NextResponse.json(
        { message: "Question not found" },
        { status: 404 }
      );
    }

    // Verify access
    const quiz = await db.quiz.findUnique({
      where: {
        id: quizId,
      },
    });

    if (!quiz) {
      return NextResponse.json(
        { message: "Quiz not found" },
        { status: 404 }
      );
    }

    // If quiz is not published, only the creator can view it
    if (!quiz.isPublished && (!session || quiz.creatorId !== session.user.id)) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    return NextResponse.json(question);
  } catch (error) {
    console.error("Error fetching question:", error);
    return NextResponse.json(
      { message: "An error occurred while fetching the question" },
      { status: 500 }
    );
  }
}

// Update a question
export async function PATCH(req: Request, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const { id: quizId, questionId } = await params;
    const body = await req.json();

    // Verify ownership
    const quiz = await db.quiz.findUnique({
      where: {
        id: quizId,
        creatorId: session.user.id,
      },
    });

    if (!quiz) {
      return NextResponse.json(
        { message: "Quiz not found or you don't have permission to edit it" },
        { status: 404 }
      );
    }

    // Verify question exists and belongs to the quiz
    const question = await db.question.findUnique({
      where: {
        id: questionId,
        quizId: quizId,
      },
    });

    if (!question) {
      return NextResponse.json(
        { message: "Question not found" },
        { status: 404 }
      );
    }

    // Process the question data
    let questionData = { ...body };
    
    // Convert text to JSON if it's not already a string
    if (questionData.text && typeof questionData.text !== "string") {
      questionData.text = JSON.stringify(questionData.text);
    }
    
    // Convert other JSON fields
    for (const field of ["options", "correctAnswer", "correctAnswers", "stems", "correctPairs", "textTemplate", "blanks"]) {
      if (questionData[field] && typeof questionData[field] !== "string") {
        questionData[field] = JSON.stringify(questionData[field]);
      }
    }

    // Update the question
    const updatedQuestion = await db.question.update({
      where: {
        id: questionId,
      },
      data: questionData,
    });

    return NextResponse.json(updatedQuestion);
  } catch (error) {
    console.error("Error updating question:", error);
    return NextResponse.json(
      { message: "An error occurred while updating the question" },
      { status: 500 }
    );
  }
}

// Delete a question
export async function DELETE(req: Request, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const { id: quizId, questionId } = await params;

    // Verify ownership
    const quiz = await db.quiz.findUnique({
      where: {
        id: quizId,
        creatorId: session.user.id,
      },
    });

    if (!quiz) {
      return NextResponse.json(
        { message: "Quiz not found or you don't have permission to edit it" },
        { status: 404 }
      );
    }

    // Verify question exists and belongs to the quiz
    const question = await db.question.findUnique({
      where: {
        id: questionId,
        quizId: quizId,
      },
    });

    if (!question) {
      return NextResponse.json(
        { message: "Question not found" },
        { status: 404 }
      );
    }

    // Delete the question
    await db.question.delete({
      where: {
        id: questionId,
      },
    });

    return NextResponse.json(
      { message: "Question deleted successfully" },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error deleting question:", error);
    return NextResponse.json(
      { message: "An error occurred while deleting the question" },
      { status: 500 }
    );
  }
}
