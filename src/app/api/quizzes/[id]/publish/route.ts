import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";

interface RouteParams {
  params: {
    id: string;
  };
}

export async function POST(req: Request, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const quizId = await params.id;
    const { isPublished } = await req.json();

    // Verify ownership
    const quiz = await db.quiz.findUnique({
      where: {
        id: quizId,
        creatorId: session.user.id,
      },
    });

    if (!quiz) {
      return NextResponse.json(
        { message: "Quiz not found or you don't have permission to edit it" },
        { status: 404 }
      );
    }

    // Update the publish status
    const updatedQuiz = await db.quiz.update({
      where: {
        id: quizId,
      },
      data: {
        isPublished,
      },
    });

    return NextResponse.json(updatedQuiz);
  } catch (error) {
    console.error("Error updating publish status:", error);
    return NextResponse.json(
      { message: "An error occurred while updating the publish status" },
      { status: 500 }
    );
  }
}
