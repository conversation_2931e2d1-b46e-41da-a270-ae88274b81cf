import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";

// Get a specific quiz
export async function GET(
  req: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    const { id: quizId } = await params;

    const quiz = await db.quiz.findUnique({
      where: {
        id: quizId,
      },
      include: {
        questions: true,
        questionPools: {
          include: {
            questions: true,
          },
        },
        selectionRules: true,
      },
    });

    if (!quiz) {
      return NextResponse.json(
        { message: "Quiz not found" },
        { status: 404 }
      );
    }

    // If quiz is not published, only the creator can view it
    if (!quiz.isPublished && (!session || quiz.creatorId !== session.user.id)) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    return NextResponse.json(quiz);
  } catch (error) {
    console.error("Error fetching quiz:", error);
    return NextResponse.json(
      { message: "An error occurred while fetching the quiz" },
      { status: 500 }
    );
  }
}

// Update a quiz
export async function PATCH(
  req: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const { id: quizId } = await params;
    const body = await req.json();

    // Verify ownership
    const quiz = await db.quiz.findUnique({
      where: {
        id: quizId,
        creatorId: session.user.id,
      },
    });

    if (!quiz) {
      return NextResponse.json(
        { message: "Quiz not found or you don't have permission to edit it" },
        { status: 404 }
      );
    }

    // Update the quiz
    const updatedQuiz = await db.quiz.update({
      where: {
        id: quizId,
      },
      data: body,
    });

    return NextResponse.json(updatedQuiz);
  } catch (error) {
    console.error("Error updating quiz:", error);
    return NextResponse.json(
      { message: "An error occurred while updating the quiz" },
      { status: 500 }
    );
  }
}

// Delete a quiz
export async function DELETE(
  req: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const { id: quizId } = await params;

    // Verify ownership
    const quiz = await db.quiz.findUnique({
      where: {
        id: quizId,
        creatorId: session.user.id,
      },
    });

    if (!quiz) {
      return NextResponse.json(
        { message: "Quiz not found or you don't have permission to delete it" },
        { status: 404 }
      );
    }

    // Delete the quiz
    await db.quiz.delete({
      where: {
        id: quizId,
      },
    });

    return NextResponse.json(
      { message: "Quiz deleted successfully" },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error deleting quiz:", error);
    return NextResponse.json(
      { message: "An error occurred while deleting the quiz" },
      { status: 500 }
    );
  }
}
