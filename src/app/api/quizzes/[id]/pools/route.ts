import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";

interface RouteParams {
  params: {
    id: string;
  };
}

// Get all pools for a quiz
export async function GET(req: Request, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions);
    const quizId = params.id;

    // Verify access
    const quiz = await db.quiz.findUnique({
      where: {
        id: quizId,
      },
    });

    if (!quiz) {
      return NextResponse.json(
        { message: "Quiz not found" },
        { status: 404 }
      );
    }

    // If quiz is not published, only the creator can view it
    if (!quiz.isPublished && (!session || quiz.creatorId !== session.user.id)) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const pools = await db.questionPool.findMany({
      where: {
        quizId: quizId,
      },
      include: {
        questions: true,
      },
      orderBy: {
        createdAt: "asc",
      },
    });

    return NextResponse.json(pools);
  } catch (error) {
    console.error("Error fetching pools:", error);
    return NextResponse.json(
      { message: "An error occurred while fetching pools" },
      { status: 500 }
    );
  }
}

// Add a new pool to a quiz
export async function POST(req: Request, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const quizId = params.id;
    const body = await req.json();

    // Verify ownership
    const quiz = await db.quiz.findUnique({
      where: {
        id: quizId,
        creatorId: session.user.id,
      },
    });

    if (!quiz) {
      return NextResponse.json(
        { message: "Quiz not found or you don't have permission to edit it" },
        { status: 404 }
      );
    }

    // Create the pool
    const pool = await db.questionPool.create({
      data: {
        ...body,
        quizId: quizId,
      },
    });

    return NextResponse.json(pool, { status: 201 });
  } catch (error) {
    console.error("Error creating pool:", error);
    return NextResponse.json(
      { message: "An error occurred while creating the pool" },
      { status: 500 }
    );
  }
}
