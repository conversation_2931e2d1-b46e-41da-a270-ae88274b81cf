import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";



// Get all selection rules for a quiz
export async function GET(
  req: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    const { id: quizId } = await params;

    // Verify access
    const quiz = await db.quiz.findUnique({
      where: {
        id: quizId,
      },
    });

    if (!quiz) {
      return NextResponse.json(
        { message: "Quiz not found" },
        { status: 404 }
      );
    }

    // If quiz is not published, only the creator can view it
    if (!quiz.isPublished && (!session || quiz.creatorId !== session.user.id)) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const rules = await db.selectionRule.findMany({
      where: {
        quizId: quizId,
      },
      orderBy: {
        createdAt: "asc",
      },
    });

    return NextResponse.json(rules);
  } catch (error) {
    console.error("Error fetching selection rules:", error);
    return NextResponse.json(
      { message: "An error occurred while fetching selection rules" },
      { status: 500 }
    );
  }
}

// Add a new selection rule to a quiz
export async function POST(
  req: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const { id: quizId } = await params;
    const body = await req.json();

    // Verify ownership
    const quiz = await db.quiz.findUnique({
      where: {
        id: quizId,
        creatorId: session.user.id,
      },
    });

    if (!quiz) {
      return NextResponse.json(
        { message: "Quiz not found or you don't have permission to edit it" },
        { status: 404 }
      );
    }

    // Verify the pool exists
    const pool = await db.questionPool.findUnique({
      where: {
        id: body.poolId,
        quizId: quizId,
      },
    });

    if (!pool) {
      return NextResponse.json(
        { message: "Pool not found" },
        { status: 404 }
      );
    }

    // Create the selection rule
    const rule = await db.selectionRule.create({
      data: {
        ...body,
        quizId: quizId,
        poolId: pool.poolId, // Use the poolId from the pool, not the pool's id
      },
    });

    return NextResponse.json(rule, { status: 201 });
  } catch (error) {
    console.error("Error creating selection rule:", error);
    return NextResponse.json(
      { message: "An error occurred while creating the selection rule" },
      { status: 500 }
    );
  }
}
