import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";

interface RouteParams {
  params: {
    id: string;
    ruleId: string;
  };
}

// Get a specific selection rule
export async function GET(req: Request, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions);
    const { id: quizId, ruleId } = params;

    const rule = await db.selectionRule.findUnique({
      where: {
        id: ruleId,
        quizId: quizId,
      },
    });

    if (!rule) {
      return NextResponse.json(
        { message: "Selection rule not found" },
        { status: 404 }
      );
    }

    // Verify access
    const quiz = await db.quiz.findUnique({
      where: {
        id: quizId,
      },
    });

    if (!quiz) {
      return NextResponse.json(
        { message: "Quiz not found" },
        { status: 404 }
      );
    }

    // If quiz is not published, only the creator can view it
    if (!quiz.isPublished && (!session || quiz.creatorId !== session.user.id)) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    return NextResponse.json(rule);
  } catch (error) {
    console.error("Error fetching selection rule:", error);
    return NextResponse.json(
      { message: "An error occurred while fetching the selection rule" },
      { status: 500 }
    );
  }
}

// Update a selection rule
export async function PATCH(req: Request, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const { id: quizId, ruleId } = params;
    const body = await req.json();

    // Verify ownership
    const quiz = await db.quiz.findUnique({
      where: {
        id: quizId,
        creatorId: session.user.id,
      },
    });

    if (!quiz) {
      return NextResponse.json(
        { message: "Quiz not found or you don't have permission to edit it" },
        { status: 404 }
      );
    }

    // Verify rule exists and belongs to the quiz
    const rule = await db.selectionRule.findUnique({
      where: {
        id: ruleId,
        quizId: quizId,
      },
    });

    if (!rule) {
      return NextResponse.json(
        { message: "Selection rule not found" },
        { status: 404 }
      );
    }

    // If the pool is being changed, verify the new pool exists
    if (body.poolId) {
      const pool = await db.questionPool.findUnique({
        where: {
          id: body.poolId,
          quizId: quizId,
        },
      });

      if (!pool) {
        return NextResponse.json(
          { message: "Pool not found" },
          { status: 404 }
        );
      }

      // Use the poolId from the pool, not the pool's id
      body.poolId = pool.poolId;
    }

    // Update the selection rule
    const updatedRule = await db.selectionRule.update({
      where: {
        id: ruleId,
      },
      data: body,
    });

    return NextResponse.json(updatedRule);
  } catch (error) {
    console.error("Error updating selection rule:", error);
    return NextResponse.json(
      { message: "An error occurred while updating the selection rule" },
      { status: 500 }
    );
  }
}

// Delete a selection rule
export async function DELETE(req: Request, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const { id: quizId, ruleId } = params;

    // Verify ownership
    const quiz = await db.quiz.findUnique({
      where: {
        id: quizId,
        creatorId: session.user.id,
      },
    });

    if (!quiz) {
      return NextResponse.json(
        { message: "Quiz not found or you don't have permission to edit it" },
        { status: 404 }
      );
    }

    // Verify rule exists and belongs to the quiz
    const rule = await db.selectionRule.findUnique({
      where: {
        id: ruleId,
        quizId: quizId,
      },
    });

    if (!rule) {
      return NextResponse.json(
        { message: "Selection rule not found" },
        { status: 404 }
      );
    }

    // Delete the selection rule
    await db.selectionRule.delete({
      where: {
        id: ruleId,
      },
    });

    return NextResponse.json(
      { message: "Selection rule deleted successfully" },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error deleting selection rule:", error);
    return NextResponse.json(
      { message: "An error occurred while deleting the selection rule" },
      { status: 500 }
    );
  }
}
