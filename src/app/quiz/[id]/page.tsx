import { notFound } from "next/navigation";
import Link from "next/link";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { QuizFlowJSON } from "@/types/qfjson";
import QuizClient from "@/components/quiz/QuizClient";

interface QuizPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default async function QuizPage({ params }: QuizPageProps) {
  const session = await getServerSession(authOptions);

  const { id } = await params;

  // Get the quiz
  const quiz = await db.quiz.findUnique({
    where: {
      id,
    },
    include: {
      creator: {
        select: {
          name: true,
        },
      },
      questions: true,
      questionPools: {
        include: {
          questions: true,
        },
      },
      selectionRules: true,
    },
  });

  if (!quiz || (!quiz.isPublished && quiz.creatorId !== session?.user.id)) {
    notFound();
  }

  // Convert the database quiz to QFJSON format
  const qfjson: QuizFlowJSON = {
    quiz: {
      $schema: "https://quizflow.org/schemas/quizflow_schema_v1.1.json",
      metadata: {
        format_version: quiz.formatVersion,
        quiz_id: quiz.quizId,
        title: quiz.title,
        description: quiz.description || undefined,
        author: quiz.creator?.name || quiz.author || undefined,
        creation_date: quiz.creationDate.toISOString(),
        tags: quiz.tags,
        passing_score_percentage: quiz.passingScore || undefined,
        time_limit_minutes: quiz.timeLimit || undefined,
        markup_format: quiz.markupFormat,
        locale: quiz.locale,
      },
      questions: quiz.questions.map((q) => ({
        ...q,
        id: undefined,
        quizId: undefined,
        createdAt: undefined,
        updatedAt: undefined,
        questionPoolId: undefined,
      })) as any,
      question_pools: quiz.questionPools.length > 0
        ? quiz.questionPools.map((pool) => ({
            pool_id: pool.poolId,
            title: pool.title || undefined,
            description: pool.description || undefined,
            questions: pool.questions.map((q) => ({
              ...q,
              id: undefined,
              quizId: undefined,
              createdAt: undefined,
              updatedAt: undefined,
              questionPoolId: undefined,
            })),
          }))
        : undefined,
      selection_rules: quiz.selectionRules.length > 0
        ? quiz.selectionRules.map((rule) => ({
            pool_id: rule.poolId,
            select_count: rule.selectCount,
            randomize: rule.randomize,
            shuffle_order: rule.shuffleOrder,
          }))
        : undefined,
    },
  };

  return (
    <div className="flex flex-col min-h-screen">
      {/* Navigation */}
      <header className="border-b">
        <div className="container mx-auto px-4 flex h-16 items-center justify-between">
          <div className="flex items-center gap-6">
            <Link href="/" className="text-xl font-bold">
              QuizFlow
            </Link>
          </div>
          <div className="flex items-center gap-4">
            {session ? (
              <Button asChild>
                <Link href="/dashboard">Dashboard</Link>
              </Button>
            ) : (
              <div className="flex items-center gap-4">
                <Button variant="outline" asChild>
                  <Link href="/auth/login">Sign In</Link>
                </Button>
                <Button asChild>
                  <Link href="/auth/register">Sign Up</Link>
                </Button>
              </div>
            )}
          </div>
        </div>
      </header>

      <main className="flex-1 py-8">
        <div className="container mx-auto px-4">
          <div className="mb-8">
            <Link href="/explore" className="text-primary hover:underline flex items-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-4 w-4 mr-1"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M10 19l-7-7m0 0l7-7m-7 7h18"
                />
              </svg>
              Back to Explore
            </Link>
          </div>

          <QuizClient quiz={qfjson} />
        </div>
      </main>

      {/* Footer */}
      <footer className="py-8 border-t">
        <div className="container mx-auto px-4 text-center">
          <p className="text-muted-foreground">
            &copy; {new Date().getFullYear()} QuizFlow. All rights reserved.
          </p>
        </div>
      </footer>
    </div>
  );
}
