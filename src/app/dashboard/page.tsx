import { redirect } from "next/navigation";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import Link from "next/link";
import { Button } from "@/components/ui/button";

export default async function DashboardPage() {
  const session = await getServerSession(authOptions);

  if (!session) {
    redirect("/auth/login");
  }

  // Get user's quizzes
  const userQuizzes = await db.quiz.findMany({
    where: {
      creatorId: session.user.id,
    },
    orderBy: {
      updatedAt: "desc",
    },
    take: 5,
  });

  // Get user's recent responses
  const userResponses = await db.userResponse.findMany({
    where: {
      userId: session.user.id,
    },
    include: {
      quiz: true,
    },
    orderBy: {
      completedAt: "desc",
    },
    take: 5,
  });

  return (
    <div className="container mx-auto py-8 px-4">
      <h1 className="text-3xl font-bold mb-8">Dashboard</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <Card>
          <CardHeader>
            <CardTitle>Welcome, {session.user.name || "User"}!</CardTitle>
            <CardDescription>
              Manage your quizzes and view your progress
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex space-x-4">
              <Button asChild>
                <Link href="/dashboard/quizzes/create">Create New Quiz</Link>
              </Button>
              <Button variant="outline" asChild>
                <Link href="/dashboard/quizzes">View All Quizzes</Link>
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Your Stats</CardTitle>
            <CardDescription>
              Your quiz activity and performance
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <div className="bg-muted p-4 rounded-md">
                <p className="text-sm text-muted-foreground">Created Quizzes</p>
                <p className="text-2xl font-bold">{userQuizzes.length}</p>
              </div>
              <div className="bg-muted p-4 rounded-md">
                <p className="text-sm text-muted-foreground">Completed Quizzes</p>
                <p className="text-2xl font-bold">{userResponses.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Your Quizzes</CardTitle>
            <CardDescription>
              Recently created and updated quizzes
            </CardDescription>
          </CardHeader>
          <CardContent>
            {userQuizzes.length > 0 ? (
              <ul className="space-y-2">
                {userQuizzes.map((quiz: any) => (
                  <li key={quiz.id} className="p-3 border rounded-md">
                    <div className="flex justify-between items-center">
                      <div>
                        <p className="font-medium">{quiz.title}</p>
                        <p className="text-sm text-muted-foreground">
                          {quiz.isPublished ? "Published" : "Draft"}
                        </p>
                      </div>
                      <Button variant="outline" size="sm" asChild>
                        <Link href={`/dashboard/quizzes/${quiz.id}`}>
                          Edit
                        </Link>
                      </Button>
                    </div>
                  </li>
                ))}
              </ul>
            ) : (
              <p className="text-muted-foreground">
                You haven&apos;t created any quizzes yet.
              </p>
            )}
            {userQuizzes.length > 0 && (
              <div className="mt-4">
                <Button variant="link" asChild className="px-0">
                  <Link href="/dashboard/quizzes">View all quizzes</Link>
                </Button>
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
            <CardDescription>
              Your recent quiz attempts
            </CardDescription>
          </CardHeader>
          <CardContent>
            {userResponses.length > 0 ? (
              <ul className="space-y-2">
                {userResponses.map((response: any) => (
                  <li key={response.id} className="p-3 border rounded-md">
                    <div>
                      <p className="font-medium">{response.quiz.title}</p>
                      <div className="flex justify-between items-center mt-1">
                        <p className="text-sm text-muted-foreground">
                          Score: {response.score.toFixed(0)}%
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {new Date(response.completedAt || response.startedAt).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
            ) : (
              <p className="text-muted-foreground">
                You haven&apos;t taken any quizzes yet.
              </p>
            )}
            {userResponses.length > 0 && (
              <div className="mt-4">
                <Button variant="link" asChild className="px-0">
                  <Link href="/dashboard/activity">View all activity</Link>
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
