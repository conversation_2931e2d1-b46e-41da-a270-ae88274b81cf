import { redirect } from "next/navigation";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { calculatePercentage } from "@/lib/utils";
import AnalyticsOverview from "@/components/analytics/AnalyticsOverview";
import QuizPerformanceChart from "@/components/analytics/QuizPerformanceChart";
import TopQuizzesChart from "@/components/analytics/TopQuizzesChart";
import RecentActivityTable from "@/components/analytics/RecentActivityTable";

export default async function AnalyticsPage() {
  const session = await getServerSession(authOptions);

  if (!session) {
    redirect("/auth/login");
  }

  // Get user's quizzes
  const userQuizzes = await db.quiz.findMany({
    where: {
      creatorId: session.user.id,
    },
    include: {
      _count: {
        select: {
          responses: true,
        },
      },
    },
  });

  // Get user's responses
  const userResponses = await db.userResponse.findMany({
    where: {
      userId: session.user.id,
    },
    include: {
      quiz: true,
    },
    orderBy: {
      completedAt: "desc",
    },
  });

  // Get total responses for user's quizzes
  const quizResponses = await db.userResponse.findMany({
    where: {
      quiz: {
        creatorId: session.user.id,
      },
    },
    include: {
      quiz: true,
      user: {
        select: {
          id: true,
          name: true,
          email: true,
        },
      },
    },
    orderBy: {
      completedAt: "desc",
    },
    take: 10,
  });

  // Calculate analytics data
  const totalQuizzes = userQuizzes.length;
  const totalQuizzesTaken = userResponses.length;
  const totalResponsesReceived = quizResponses.length;

  const averageScore = userResponses.length > 0
    ? userResponses.reduce((sum: number, response: any) => sum + response.score, 0) / userResponses.length
    : 0;

  const completionRate = userResponses.length > 0
    ? (userResponses.filter((r: any) => r.completedAt).length / userResponses.length) * 100
    : 0;

  // Get quiz performance data for chart
  const quizPerformanceData = userQuizzes.map((quiz: any) => {
    const responses = quizResponses.filter((r: any) => r.quizId === quiz.id);
    const avgScore = responses.length > 0
      ? responses.reduce((sum: number, r: any) => sum + r.score, 0) / responses.length
      : 0;

    return {
      quizTitle: quiz.title,
      responseCount: responses.length,
      averageScore: avgScore,
    };
  }).filter((q: any) => q.responseCount > 0).sort((a: any, b: any) => b.responseCount - a.responseCount).slice(0, 5);

  // Get top quizzes by response count
  const topQuizzes = userQuizzes
    .sort((a: any, b: any) => (b._count?.responses || 0) - (a._count?.responses || 0))
    .slice(0, 5)
    .map((quiz: any) => ({
      title: quiz.title,
      responseCount: quiz._count?.responses || 0,
    }));

  return (
    <div className="container mx-auto py-8 px-4">
      <h1 className="text-3xl font-bold mb-8">Analytics Dashboard</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <AnalyticsOverview
          totalQuizzes={totalQuizzes}
          totalQuizzesTaken={totalQuizzesTaken}
          totalResponsesReceived={totalResponsesReceived}
          averageScore={averageScore}
          completionRate={completionRate}
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <Card>
          <CardHeader>
            <CardTitle>Quiz Performance</CardTitle>
            <CardDescription>
              Average scores and response counts for your top quizzes
            </CardDescription>
          </CardHeader>
          <CardContent>
            <QuizPerformanceChart data={quizPerformanceData} />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Top Quizzes</CardTitle>
            <CardDescription>
              Your most popular quizzes by number of responses
            </CardDescription>
          </CardHeader>
          <CardContent>
            <TopQuizzesChart data={topQuizzes} />
          </CardContent>
        </Card>
      </div>

      <div className="mb-8">
        <Card>
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
            <CardDescription>
              Recent responses to your quizzes
            </CardDescription>
          </CardHeader>
          <CardContent>
            <RecentActivityTable responses={quizResponses} />
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
