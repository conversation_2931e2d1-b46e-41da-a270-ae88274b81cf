import { notFound } from "next/navigation";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import QuizEditor from "@/components/quiz/editor/QuizEditor";

interface QuizEditPageProps {
  params: {
    id: string;
  };
}

export default async function QuizEditPage({ params }: QuizEditPageProps) {
  const session = await getServerSession(authOptions);

  if (!session) {
    notFound();
  }

  const quiz = await db.quiz.findUnique({
    where: {
      id: params.id,
      creatorId: session.user.id, // Ensure the user owns this quiz
    },
    include: {
      questions: true,
      questionPools: {
        include: {
          questions: true,
        },
      },
      selectionRules: true,
    },
  });

  if (!quiz) {
    notFound();
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <QuizEditor quiz={quiz} />
    </div>
  );
}
