import { redirect } from "next/navigation";
import Link from "next/link";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { calculatePercentage } from "@/lib/utils";
import QuizAnalyticsOverview from "@/components/analytics/QuizAnalyticsOverview";
import QuestionPerformanceTable from "@/components/analytics/QuestionPerformanceTable";
import UserResponsesTable from "@/components/analytics/UserResponsesTable";

interface QuizAnalyticsPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default async function QuizAnalyticsPage({ params }: QuizAnalyticsPageProps) {
  const session = await getServerSession(authOptions);

  if (!session) {
    redirect("/auth/login");
  }

  const { id: quizId } = await params;

  // Get the quiz
  const quiz = await db.quiz.findUnique({
    where: {
      id: quizId,
      creatorId: session.user.id,
    },
    include: {
      questions: true,
    },
  });

  if (!quiz) {
    redirect("/dashboard/quizzes");
  }

  // Get responses for this quiz
  const responses = await db.userResponse.findMany({
    where: {
      quizId: quizId,
    },
    include: {
      user: {
        select: {
          id: true,
          name: true,
          email: true,
        },
      },
    },
    orderBy: {
      completedAt: "desc",
    },
  });

  // Calculate analytics data
  const totalResponses = responses.length;
  const completedResponses = responses.filter((r: any) => r.completedAt).length;
  const averageScore = responses.length > 0
    ? responses.reduce((sum: number, r: any) => sum + r.score, 0) / responses.length
    : 0;

  const averageTimeSpent = responses.filter((r: any) => r.timeSpent).length > 0
    ? responses.filter((r: any) => r.timeSpent).reduce((sum: number, r: any) => sum + (r.timeSpent || 0), 0) / responses.filter((r: any) => r.timeSpent).length
    : 0;

  const passingResponses = quiz.passingScore
    ? responses.filter((r: any) => r.score >= (quiz.passingScore || 0)).length
    : 0;

  const passingRate = totalResponses > 0 && quiz.passingScore
    ? (passingResponses / totalResponses) * 100
    : 0;

  // Calculate question performance
  const questionPerformance = quiz.questions.map((question: any) => {
    // In a real implementation, you would analyze the answers field in responses
    // to determine how many users got each question correct
    // This is a simplified placeholder
    const correctCount = Math.floor(Math.random() * totalResponses);

    return {
      id: question.id,
      text: typeof question.text === 'string'
        ? question.text
        : JSON.parse(question.text as string).default || 'Question',
      type: question.type,
      correctCount,
      totalAttempts: totalResponses,
      correctPercentage: totalResponses > 0 ? (correctCount / totalResponses) * 100 : 0,
    };
  }).sort((a: any, b: any) => a.correctPercentage - b.correctPercentage);

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold">{quiz.title} - Analytics</h1>
          <p className="text-muted-foreground">
            Detailed performance metrics and user responses
          </p>
        </div>
        <div className="flex space-x-4">
          <Button variant="outline" asChild>
            <Link href={`/dashboard/quizzes/${quizId}`}>
              Back to Quiz
            </Link>
          </Button>
          <Button variant="outline" asChild>
            <Link href="/dashboard/analytics">
              Overall Analytics
            </Link>
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <QuizAnalyticsOverview
          totalResponses={totalResponses}
          completedResponses={completedResponses}
          averageScore={averageScore}
          averageTimeSpent={averageTimeSpent}
          passingRate={passingRate}
        />
      </div>

      <div className="space-y-8">
        <Card>
          <CardHeader>
            <CardTitle>Question Performance</CardTitle>
            <CardDescription>
              See which questions are most challenging for users
            </CardDescription>
          </CardHeader>
          <CardContent>
            <QuestionPerformanceTable questions={questionPerformance} />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>User Responses</CardTitle>
            <CardDescription>
              Individual user performance on this quiz
            </CardDescription>
          </CardHeader>
          <CardContent>
            <UserResponsesTable responses={responses} />
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
