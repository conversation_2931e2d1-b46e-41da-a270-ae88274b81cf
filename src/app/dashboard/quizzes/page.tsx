import { redirect } from "next/navigation";
import { getServerSession } from "next-auth";
import Link from "next/link";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { formatDate } from "@/lib/utils";

export default async function QuizzesPage() {
  const session = await getServerSession(authOptions);

  if (!session) {
    redirect("/auth/login");
  }

  const quizzes = await db.quiz.findMany({
    where: {
      creatorId: session.user.id,
    },
    orderBy: {
      updatedAt: "desc",
    },
  });

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold">My Quizzes</h1>
        <Button asChild>
          <Link href="/dashboard/quizzes/create">Create New Quiz</Link>
        </Button>
      </div>

      {quizzes.length === 0 ? (
        <Card>
          <CardContent className="py-8">
            <div className="text-center">
              <h3 className="text-lg font-medium mb-2">No quizzes yet</h3>
              <p className="text-muted-foreground mb-4">
                You haven't created any quizzes yet. Get started by creating your first quiz.
              </p>
              <Button asChild>
                <Link href="/dashboard/quizzes/create">Create Your First Quiz</Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {quizzes.map((quiz: any) => (
            <Card key={quiz.id}>
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="line-clamp-1">{quiz.title}</CardTitle>
                    <CardDescription>
                      {quiz.isPublished ? "Published" : "Draft"}
                    </CardDescription>
                  </div>
                  <div className="flex items-center space-x-1">
                    {quiz.tags.slice(0, 2).map((tag: any, index: number) => (
                      <span
                        key={index}
                        className="inline-block px-2 py-1 text-xs bg-muted rounded-full"
                      >
                        {tag}
                      </span>
                    ))}
                    {quiz.tags.length > 2 && (
                      <span className="inline-block px-2 py-1 text-xs bg-muted rounded-full">
                        +{quiz.tags.length - 2}
                      </span>
                    )}
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground line-clamp-2 min-h-[40px]">
                  {quiz.description || "No description provided"}
                </p>
                <div className="mt-4 grid grid-cols-2 gap-2 text-sm">
                  <div>
                    <p className="text-muted-foreground">Questions</p>
                    <p className="font-medium">
                      {quiz._count?.questions || 0}
                    </p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Passing Score</p>
                    <p className="font-medium">
                      {quiz.passingScore ? `${quiz.passingScore}%` : "N/A"}
                    </p>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-between border-t pt-4">
                <div className="text-xs text-muted-foreground">
                  Updated {formatDate(quiz.updatedAt)}
                </div>
                <div className="flex space-x-2">
                  <Button variant="outline" size="sm" asChild>
                    <Link href={`/dashboard/quizzes/${quiz.id}`}>
                      Edit
                    </Link>
                  </Button>
                  <Button size="sm" asChild>
                    <Link href={`/dashboard/quizzes/${quiz.id}/preview`}>
                      Preview
                    </Link>
                  </Button>
                </div>
              </CardFooter>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
