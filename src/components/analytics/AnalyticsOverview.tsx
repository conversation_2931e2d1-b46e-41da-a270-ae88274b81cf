"use client";

import { Card, CardContent } from "@/components/ui/card";

interface AnalyticsOverviewProps {
  totalQuizzes: number;
  totalQuizzesTaken: number;
  totalResponsesReceived: number;
  averageScore: number;
  completionRate: number;
}

export default function AnalyticsOverview({
  totalQuizzes,
  totalQuizzesTaken,
  totalResponsesReceived,
  averageScore,
  completionRate,
}: AnalyticsOverviewProps) {
  return (
    <>
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col space-y-2">
            <p className="text-sm text-muted-foreground">Total Quizzes Created</p>
            <p className="text-3xl font-bold">{totalQuizzes}</p>
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col space-y-2">
            <p className="text-sm text-muted-foreground">Quizzes Taken</p>
            <p className="text-3xl font-bold">{totalQuizzesTaken}</p>
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col space-y-2">
            <p className="text-sm text-muted-foreground">Responses Received</p>
            <p className="text-3xl font-bold">{totalResponsesReceived}</p>
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col space-y-2">
            <p className="text-sm text-muted-foreground">Average Score</p>
            <p className="text-3xl font-bold">{averageScore.toFixed(1)}%</p>
          </div>
        </CardContent>
      </Card>
    </>
  );
}
