"use client";

import { useEffect, useRef } from "react";

interface QuizPerformanceData {
  quizTitle: string;
  responseCount: number;
  averageScore: number;
}

interface QuizPerformanceChartProps {
  data: QuizPerformanceData[];
}

export default function QuizPerformanceChart({ data }: QuizPerformanceChartProps) {
  const chartRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!chartRef.current || data.length === 0) return;

    // This is a placeholder for a chart library
    // In a real implementation, you would use a library like Chart.js, Recharts, or D3.js
    
    const chartContainer = chartRef.current;
    chartContainer.innerHTML = "";
    
    // Create a simple bar chart representation
    const chartHeight = 300;
    const barWidth = Math.floor((chartContainer.clientWidth - 100) / data.length);
    const maxResponseCount = Math.max(...data.map(d => d.responseCount));
    
    const chart = document.createElement("div");
    chart.style.position = "relative";
    chart.style.height = `${chartHeight}px`;
    chart.style.display = "flex";
    chart.style.alignItems = "flex-end";
    chart.style.justifyContent = "space-around";
    chart.style.padding = "0 20px";
    
    // Add y-axis labels
    const yAxis = document.createElement("div");
    yAxis.style.position = "absolute";
    yAxis.style.left = "0";
    yAxis.style.top = "0";
    yAxis.style.height = "100%";
    yAxis.style.display = "flex";
    yAxis.style.flexDirection = "column";
    yAxis.style.justifyContent = "space-between";
    
    const yLabels = [100, 75, 50, 25, 0];
    yLabels.forEach(label => {
      const yLabel = document.createElement("div");
      yLabel.style.fontSize = "12px";
      yLabel.style.color = "#666";
      yLabel.textContent = `${label}%`;
      yAxis.appendChild(yLabel);
    });
    
    chart.appendChild(yAxis);
    
    // Add bars
    data.forEach((item, index) => {
      const barContainer = document.createElement("div");
      barContainer.style.display = "flex";
      barContainer.style.flexDirection = "column";
      barContainer.style.alignItems = "center";
      barContainer.style.width = `${barWidth}px`;
      
      // Score bar
      const scoreBar = document.createElement("div");
      const scoreHeight = (item.averageScore / 100) * chartHeight;
      scoreBar.style.height = `${scoreHeight}px`;
      scoreBar.style.width = `${barWidth * 0.4}px`;
      scoreBar.style.backgroundColor = "#3b82f6";
      scoreBar.style.borderRadius = "4px 4px 0 0";
      
      // Response count bar
      const responseBar = document.createElement("div");
      const responseHeight = (item.responseCount / maxResponseCount) * chartHeight;
      responseBar.style.height = `${responseHeight}px`;
      responseBar.style.width = `${barWidth * 0.4}px`;
      responseBar.style.backgroundColor = "#10b981";
      responseBar.style.borderRadius = "4px 4px 0 0";
      responseBar.style.marginLeft = "4px";
      
      const barGroup = document.createElement("div");
      barGroup.style.display = "flex";
      barGroup.style.alignItems = "flex-end";
      barGroup.style.height = "100%";
      barGroup.appendChild(scoreBar);
      barGroup.appendChild(responseBar);
      
      barContainer.appendChild(barGroup);
      
      // Label
      const label = document.createElement("div");
      label.style.fontSize = "12px";
      label.style.marginTop = "8px";
      label.style.textAlign = "center";
      label.style.whiteSpace = "nowrap";
      label.style.overflow = "hidden";
      label.style.textOverflow = "ellipsis";
      label.style.width = "100%";
      label.textContent = item.quizTitle;
      barContainer.appendChild(label);
      
      chart.appendChild(barContainer);
    });
    
    // Add legend
    const legend = document.createElement("div");
    legend.style.display = "flex";
    legend.style.justifyContent = "center";
    legend.style.marginTop = "20px";
    
    const scoreLegend = document.createElement("div");
    scoreLegend.style.display = "flex";
    scoreLegend.style.alignItems = "center";
    scoreLegend.style.marginRight = "20px";
    
    const scoreColor = document.createElement("div");
    scoreColor.style.width = "12px";
    scoreColor.style.height = "12px";
    scoreColor.style.backgroundColor = "#3b82f6";
    scoreColor.style.marginRight = "4px";
    
    const scoreText = document.createElement("span");
    scoreText.textContent = "Average Score";
    
    scoreLegend.appendChild(scoreColor);
    scoreLegend.appendChild(scoreText);
    
    const responseLegend = document.createElement("div");
    responseLegend.style.display = "flex";
    responseLegend.style.alignItems = "center";
    
    const responseColor = document.createElement("div");
    responseColor.style.width = "12px";
    responseColor.style.height = "12px";
    responseColor.style.backgroundColor = "#10b981";
    responseColor.style.marginRight = "4px";
    
    const responseText = document.createElement("span");
    responseText.textContent = "Response Count";
    
    responseLegend.appendChild(responseColor);
    responseLegend.appendChild(responseText);
    
    legend.appendChild(scoreLegend);
    legend.appendChild(responseLegend);
    
    chartContainer.appendChild(chart);
    chartContainer.appendChild(legend);
    
  }, [data]);

  if (data.length === 0) {
    return (
      <div className="flex items-center justify-center h-[300px] bg-muted/20 rounded-md">
        <p className="text-muted-foreground">No data available</p>
      </div>
    );
  }

  return (
    <div ref={chartRef} className="h-[400px]">
      {/* Chart will be rendered here */}
    </div>
  );
}
