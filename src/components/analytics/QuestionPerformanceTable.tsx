"use client";

interface QuestionPerformance {
  id: string;
  text: string;
  type: string;
  correctCount: number;
  totalAttempts: number;
  correctPercentage: number;
}

interface QuestionPerformanceTableProps {
  questions: QuestionPerformance[];
}

export default function QuestionPerformanceTable({ questions }: QuestionPerformanceTableProps) {
  if (questions.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">No question data available</p>
      </div>
    );
  }

  return (
    <div className="overflow-x-auto">
      <table className="w-full">
        <thead>
          <tr className="border-b">
            <th className="text-left py-3 px-4 font-medium">Question</th>
            <th className="text-left py-3 px-4 font-medium">Type</th>
            <th className="text-left py-3 px-4 font-medium">Correct</th>
            <th className="text-left py-3 px-4 font-medium">Total</th>
            <th className="text-left py-3 px-4 font-medium">Success Rate</th>
          </tr>
        </thead>
        <tbody>
          {questions.map((question) => (
            <tr key={question.id} className="border-b hover:bg-muted/50">
              <td className="py-3 px-4">
                <div className="line-clamp-1">{question.text}</div>
              </td>
              <td className="py-3 px-4">
                <span className="capitalize">{formatQuestionType(question.type)}</span>
              </td>
              <td className="py-3 px-4">{question.correctCount}</td>
              <td className="py-3 px-4">{question.totalAttempts}</td>
              <td className="py-3 px-4">
                <div className="flex items-center">
                  <div className="w-full max-w-[100px] bg-muted rounded-full h-2.5 mr-2">
                    <div 
                      className="h-2.5 rounded-full" 
                      style={{ 
                        width: `${question.correctPercentage}%`,
                        backgroundColor: getColorForPercentage(question.correctPercentage)
                      }}
                    ></div>
                  </div>
                  <span>{question.correctPercentage.toFixed(1)}%</span>
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}

function formatQuestionType(type: string): string {
  return type.replace(/_/g, ' ');
}

function getColorForPercentage(percentage: number): string {
  if (percentage < 30) return '#ef4444'; // Red
  if (percentage < 60) return '#f97316'; // Orange
  if (percentage < 80) return '#eab308'; // Yellow
  return '#22c55e'; // Green
}
