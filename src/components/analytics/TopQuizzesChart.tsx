"use client";

import { useEffect, useRef } from "react";

interface TopQuizData {
  title: string;
  responseCount: number;
}

interface TopQuizzesChartProps {
  data: TopQuizData[];
}

export default function TopQuizzesChart({ data }: TopQuizzesChartProps) {
  const chartRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!chartRef.current || data.length === 0) return;

    // This is a placeholder for a chart library
    // In a real implementation, you would use a library like Chart.js, Recharts, or D3.js
    
    const chartContainer = chartRef.current;
    chartContainer.innerHTML = "";
    
    // Create a simple horizontal bar chart
    const chartHeight = 300;
    const barHeight = Math.floor(chartHeight / (data.length * 2));
    const maxResponseCount = Math.max(...data.map(d => d.responseCount));
    
    const chart = document.createElement("div");
    chart.style.position = "relative";
    chart.style.height = `${chartHeight}px`;
    chart.style.display = "flex";
    chart.style.flexDirection = "column";
    chart.style.justifyContent = "space-around";
    chart.style.padding = "10px 0";
    
    // Add bars
    data.forEach((item, index) => {
      const barContainer = document.createElement("div");
      barContainer.style.display = "flex";
      barContainer.style.alignItems = "center";
      barContainer.style.height = `${barHeight * 2}px`;
      
      // Label
      const label = document.createElement("div");
      label.style.width = "40%";
      label.style.fontSize = "14px";
      label.style.whiteSpace = "nowrap";
      label.style.overflow = "hidden";
      label.style.textOverflow = "ellipsis";
      label.style.paddingRight = "10px";
      label.textContent = item.title;
      
      // Bar
      const barWrapper = document.createElement("div");
      barWrapper.style.width = "60%";
      barWrapper.style.height = `${barHeight}px`;
      barWrapper.style.backgroundColor = "#e5e7eb";
      barWrapper.style.borderRadius = "4px";
      barWrapper.style.overflow = "hidden";
      
      const bar = document.createElement("div");
      const barWidth = (item.responseCount / maxResponseCount) * 100;
      bar.style.width = `${barWidth}%`;
      bar.style.height = "100%";
      bar.style.backgroundColor = "#3b82f6";
      
      // Count
      const count = document.createElement("div");
      count.style.marginLeft = "10px";
      count.style.fontSize = "14px";
      count.style.fontWeight = "bold";
      count.textContent = item.responseCount.toString();
      
      barWrapper.appendChild(bar);
      barContainer.appendChild(label);
      barContainer.appendChild(barWrapper);
      barContainer.appendChild(count);
      
      chart.appendChild(barContainer);
    });
    
    chartContainer.appendChild(chart);
    
  }, [data]);

  if (data.length === 0) {
    return (
      <div className="flex items-center justify-center h-[300px] bg-muted/20 rounded-md">
        <p className="text-muted-foreground">No data available</p>
      </div>
    );
  }

  return (
    <div ref={chartRef} className="h-[300px]">
      {/* Chart will be rendered here */}
    </div>
  );
}
