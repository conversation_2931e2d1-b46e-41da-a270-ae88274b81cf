"use client";

import { Card, CardContent } from "@/components/ui/card";

interface QuizAnalyticsOverviewProps {
  totalResponses: number;
  completedResponses: number;
  averageScore: number;
  averageTimeSpent: number;
  passingRate: number;
}

export default function QuizAnalyticsOverview({
  totalResponses,
  completedResponses,
  averageScore,
  averageTimeSpent,
  passingRate,
}: QuizAnalyticsOverviewProps) {
  return (
    <>
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col space-y-2">
            <p className="text-sm text-muted-foreground">Total Responses</p>
            <p className="text-3xl font-bold">{totalResponses}</p>
            <p className="text-xs text-muted-foreground">
              {completedResponses} completed ({Math.round((completedResponses / totalResponses) * 100) || 0}%)
            </p>
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col space-y-2">
            <p className="text-sm text-muted-foreground">Average Score</p>
            <p className="text-3xl font-bold">{averageScore.toFixed(1)}%</p>
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col space-y-2">
            <p className="text-sm text-muted-foreground">Average Time</p>
            <p className="text-3xl font-bold">{formatTime(averageTimeSpent)}</p>
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col space-y-2">
            <p className="text-sm text-muted-foreground">Passing Rate</p>
            <p className="text-3xl font-bold">{passingRate.toFixed(1)}%</p>
          </div>
        </CardContent>
      </Card>
    </>
  );
}

function formatTime(seconds: number): string {
  if (seconds === 0) return "N/A";
  
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.round(seconds % 60);
  
  if (minutes === 0) {
    return `${remainingSeconds}s`;
  }
  
  return `${minutes}m ${remainingSeconds}s`;
}
