"use client";

import { useState } from "react";
import { Quiz } from "@prisma/client";
import { Button } from "@/components/ui/button";

interface QuizDetailsFormProps {
  quiz: Quiz;
  onSave: (updatedDetails: Partial<Quiz>) => void;
  isSaving: boolean;
}

export default function QuizDetailsForm({ quiz, onSave, isSaving }: QuizDetailsFormProps) {
  const [title, setTitle] = useState(quiz.title);
  const [description, setDescription] = useState(quiz.description || "");
  const [tags, setTags] = useState(quiz.tags.join(", "));
  const [passingScore, setPassingScore] = useState(quiz.passingScore?.toString() || "70");
  const [timeLimit, setTimeLimit] = useState(quiz.timeLimit?.toString() || "15");
  const [locale, setLocale] = useState(quiz.locale);
  const [markupFormat, setMarkupFormat] = useState(quiz.markupFormat);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const updatedDetails: Partial<Quiz> = {
      title,
      description: description || null,
      tags: tags.split(",").map(tag => tag.trim()).filter(tag => tag),
      passingScore: passingScore ? parseFloat(passingScore) : null,
      timeLimit: timeLimit ? parseInt(timeLimit) : null,
      locale,
      markupFormat,
    };
    
    onSave(updatedDetails);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="space-y-2">
        <label htmlFor="title" className="text-sm font-medium">
          Quiz Title
        </label>
        <input
          id="title"
          type="text"
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          className="w-full p-2 border rounded-md"
          required
        />
      </div>
      
      <div className="space-y-2">
        <label htmlFor="description" className="text-sm font-medium">
          Description
        </label>
        <textarea
          id="description"
          value={description}
          onChange={(e) => setDescription(e.target.value)}
          className="w-full p-2 border rounded-md min-h-[100px]"
          placeholder="Enter quiz description"
        />
      </div>
      
      <div className="space-y-2">
        <label htmlFor="tags" className="text-sm font-medium">
          Tags
        </label>
        <input
          id="tags"
          type="text"
          value={tags}
          onChange={(e) => setTags(e.target.value)}
          className="w-full p-2 border rounded-md"
          placeholder="Enter tags separated by commas (e.g., security, basics, networking)"
        />
        <p className="text-xs text-muted-foreground">
          Separate tags with commas (e.g., security, basics, networking)
        </p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <label htmlFor="passingScore" className="text-sm font-medium">
            Passing Score (%)
          </label>
          <input
            id="passingScore"
            type="number"
            min="0"
            max="100"
            value={passingScore}
            onChange={(e) => setPassingScore(e.target.value)}
            className="w-full p-2 border rounded-md"
          />
        </div>
        
        <div className="space-y-2">
          <label htmlFor="timeLimit" className="text-sm font-medium">
            Time Limit (minutes)
          </label>
          <input
            id="timeLimit"
            type="number"
            min="1"
            value={timeLimit}
            onChange={(e) => setTimeLimit(e.target.value)}
            className="w-full p-2 border rounded-md"
          />
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <label htmlFor="locale" className="text-sm font-medium">
            Locale
          </label>
          <select
            id="locale"
            value={locale}
            onChange={(e) => setLocale(e.target.value)}
            className="w-full p-2 border rounded-md"
          >
            <option value="en-US">English (US)</option>
            <option value="en-GB">English (UK)</option>
            <option value="es-ES">Spanish</option>
            <option value="fr-FR">French</option>
            <option value="de-DE">German</option>
            <option value="ja-JP">Japanese</option>
            <option value="zh-CN">Chinese (Simplified)</option>
          </select>
        </div>
        
        <div className="space-y-2">
          <label htmlFor="markupFormat" className="text-sm font-medium">
            Markup Format
          </label>
          <select
            id="markupFormat"
            value={markupFormat}
            onChange={(e) => setMarkupFormat(e.target.value)}
            className="w-full p-2 border rounded-md"
          >
            <option value="markdown">Markdown</option>
            <option value="html">HTML</option>
            <option value="plain_text">Plain Text</option>
          </select>
        </div>
      </div>
      
      <div className="flex justify-end pt-4">
        <Button type="submit" disabled={isSaving}>
          {isSaving ? "Saving..." : "Save Details"}
        </Button>
      </div>
    </form>
  );
}
