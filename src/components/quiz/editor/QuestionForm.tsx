"use client";

import { useState, useEffect } from "react";
import { Question } from "@prisma/client";
import { But<PERSON> } from "@/components/ui/button";
import { generateUUID } from "@/lib/utils";

// Form components for different question types
import MultipleChoiceForm from "./question-forms/MultipleChoiceForm";
import TrueFalseForm from "./question-forms/TrueFalseForm";
import ShortAnswerForm from "./question-forms/ShortAnswerForm";
import MatchingForm from "./question-forms/MatchingForm";
import FillInTheBlankForm from "./question-forms/FillInTheBlankForm";
import EssayForm from "./question-forms/EssayForm";

interface QuestionFormProps {
  questionType: string;
  initialData?: any;
  onSubmit: (questionData: any) => void;
  isSaving: boolean;
  submitLabel: string;
}

export default function QuestionForm({
  questionType,
  initialData,
  onSubmit,
  isSaving,
  submitLabel,
}: QuestionFormProps) {
  const [questionText, setQuestionText] = useState(
    initialData?.text
      ? typeof initialData.text === "string"
        ? initialData.text
        : JSON.parse(initialData.text).default || ""
      : ""
  );
  const [points, setPoints] = useState(initialData?.points?.toString() || "1");
  const [feedbackCorrect, setFeedbackCorrect] = useState(initialData?.feedbackCorrect || "");
  const [feedbackIncorrect, setFeedbackIncorrect] = useState(initialData?.feedbackIncorrect || "");
  
  // Type-specific state
  const [typeSpecificData, setTypeSpecificData] = useState<any>(
    initialData ? { ...initialData } : {}
  );

  // Reset form when question type changes
  useEffect(() => {
    if (!initialData) {
      setQuestionText("");
      setPoints("1");
      setFeedbackCorrect("");
      setFeedbackIncorrect("");
      setTypeSpecificData({});
    }
  }, [questionType, initialData]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Prepare common question data
    const questionData = {
      text: questionText,
      points: parseFloat(points),
      feedbackCorrect: feedbackCorrect || null,
      feedbackIncorrect: feedbackIncorrect || null,
      ...typeSpecificData,
    };
    
    onSubmit(questionData);
  };

  const renderTypeSpecificForm = () => {
    switch (questionType) {
      case "multiple_choice":
        return (
          <MultipleChoiceForm
            data={typeSpecificData}
            onChange={setTypeSpecificData}
          />
        );
      case "true_false":
        return (
          <TrueFalseForm
            data={typeSpecificData}
            onChange={setTypeSpecificData}
          />
        );
      case "short_answer":
        return (
          <ShortAnswerForm
            data={typeSpecificData}
            onChange={setTypeSpecificData}
          />
        );
      case "matching":
        return (
          <MatchingForm
            data={typeSpecificData}
            onChange={setTypeSpecificData}
          />
        );
      case "fill_in_the_blank":
        return (
          <FillInTheBlankForm
            data={typeSpecificData}
            onChange={setTypeSpecificData}
          />
        );
      case "essay":
        return (
          <EssayForm
            data={typeSpecificData}
            onChange={setTypeSpecificData}
          />
        );
      default:
        return <p>Unsupported question type: {questionType}</p>;
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="space-y-2">
        <label htmlFor="questionText" className="text-sm font-medium">
          Question Text
        </label>
        <textarea
          id="questionText"
          value={questionText}
          onChange={(e) => setQuestionText(e.target.value)}
          className="w-full p-2 border rounded-md min-h-[100px]"
          placeholder="Enter your question here..."
          required
        />
      </div>
      
      <div className="space-y-2">
        <label htmlFor="points" className="text-sm font-medium">
          Points
        </label>
        <input
          id="points"
          type="number"
          min="0.5"
          step="0.5"
          value={points}
          onChange={(e) => setPoints(e.target.value)}
          className="w-full p-2 border rounded-md"
          required
        />
      </div>
      
      {renderTypeSpecificForm()}
      
      <div className="space-y-2">
        <label htmlFor="feedbackCorrect" className="text-sm font-medium">
          Feedback for Correct Answer
        </label>
        <textarea
          id="feedbackCorrect"
          value={feedbackCorrect}
          onChange={(e) => setFeedbackCorrect(e.target.value)}
          className="w-full p-2 border rounded-md"
          placeholder="Feedback to show when the answer is correct"
        />
      </div>
      
      <div className="space-y-2">
        <label htmlFor="feedbackIncorrect" className="text-sm font-medium">
          Feedback for Incorrect Answer
        </label>
        <textarea
          id="feedbackIncorrect"
          value={feedbackIncorrect}
          onChange={(e) => setFeedbackIncorrect(e.target.value)}
          className="w-full p-2 border rounded-md"
          placeholder="Feedback to show when the answer is incorrect"
        />
      </div>
      
      <div className="flex justify-end pt-4">
        <Button type="submit" disabled={isSaving}>
          {isSaving ? "Saving..." : submitLabel}
        </Button>
      </div>
    </form>
  );
}
