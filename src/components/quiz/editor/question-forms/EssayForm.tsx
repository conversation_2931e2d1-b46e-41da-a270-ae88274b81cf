"use client";

import { useState, useEffect } from "react";

interface EssayFormProps {
  data: any;
  onChange: (data: any) => void;
}

export default function EssayForm({ data, onChange }: EssayFormProps) {
  const [minWordCount, setMinWordCount] = useState(
    data?.min_word_count?.toString() || ""
  );
  const [maxWordCount, setMaxWordCount] = useState(
    data?.max_word_count?.toString() || ""
  );
  const [guidelines, setGuidelines] = useState(data?.guidelines || "");

  // Update parent component when form data changes
  useEffect(() => {
    onChange({
      min_word_count: minWordCount ? parseInt(minWordCount) : undefined,
      max_word_count: maxWordCount ? parseInt(maxWordCount) : undefined,
      guidelines: guidelines || undefined,
    });
  }, [minWordCount, maxWordCount, guidelines, onChange]);

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <label htmlFor="minWordCount" className="text-sm font-medium">
            Minimum Word Count
          </label>
          <input
            id="minWordCount"
            type="number"
            min="0"
            value={minWordCount}
            onChange={(e) => setMinWordCount(e.target.value)}
            className="w-full p-2 border rounded-md"
            placeholder="Optional"
          />
        </div>
        
        <div className="space-y-2">
          <label htmlFor="maxWordCount" className="text-sm font-medium">
            Maximum Word Count
          </label>
          <input
            id="maxWordCount"
            type="number"
            min="0"
            value={maxWordCount}
            onChange={(e) => setMaxWordCount(e.target.value)}
            className="w-full p-2 border rounded-md"
            placeholder="Optional"
          />
        </div>
      </div>
      
      <div className="space-y-2">
        <label htmlFor="guidelines" className="text-sm font-medium">
          Guidelines for Students
        </label>
        <textarea
          id="guidelines"
          value={guidelines}
          onChange={(e) => setGuidelines(e.target.value)}
          className="w-full p-2 border rounded-md min-h-[100px]"
          placeholder="Enter guidelines or prompts for the essay (optional)"
        />
      </div>
      
      <div className="p-4 bg-muted rounded-md">
        <p className="text-sm">
          <strong>Note:</strong> Essay questions typically require manual grading. Students will see the guidelines and word count limits when answering the question.
        </p>
      </div>
    </div>
  );
}
