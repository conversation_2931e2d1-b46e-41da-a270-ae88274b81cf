"use client";

import { useState, useEffect } from "react";

interface TrueFalseFormProps {
  data: any;
  onChange: (data: any) => void;
}

export default function TrueFalseForm({ data, onChange }: TrueFalseFormProps) {
  const [correctAnswer, setCorrectAnswer] = useState(
    data?.correct_answer !== undefined ? data.correct_answer : true
  );

  // Update parent component when form data changes
  useEffect(() => {
    onChange({
      correct_answer: correctAnswer,
    });
  }, [correctAnswer, onChange]);

  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <label className="text-sm font-medium">Correct Answer</label>
        <div className="flex space-x-4">
          <label className="flex items-center space-x-2">
            <input
              type="radio"
              checked={correctAnswer === true}
              onChange={() => setCorrectAnswer(true)}
              name="correctAnswer"
            />
            <span>True</span>
          </label>
          <label className="flex items-center space-x-2">
            <input
              type="radio"
              checked={correctAnswer === false}
              onChange={() => setCorrectAnswer(false)}
              name="correctAnswer"
            />
            <span>False</span>
          </label>
        </div>
      </div>
    </div>
  );
}
