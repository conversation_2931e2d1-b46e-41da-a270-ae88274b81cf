"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { generateUUID } from "@/lib/utils";

interface MatchingFormProps {
  data: any;
  onChange: (data: any) => void;
}

export default function MatchingForm({ data, onChange }: MatchingFormProps) {
  const [stems, setStems] = useState<Array<{
    id: string;
    text: string;
  }>>(
    data?.stems || [
      { id: generateUUID(), text: "" },
      { id: generateUUID(), text: "" },
    ]
  );
  
  const [options, setOptions] = useState<Array<{
    id: string;
    text: string;
  }>>(
    data?.options || [
      { id: generateUUID(), text: "" },
      { id: generateUUID(), text: "" },
    ]
  );
  
  const [correctPairs, setCorrectPairs] = useState<Array<{
    stem_id: string;
    option_id: string;
    points_for_pair?: number;
  }>>(
    data?.correct_pairs || []
  );

  // Initialize correct pairs if they don't exist
  useEffect(() => {
    if (correctPairs.length === 0 && stems.length > 0 && options.length > 0) {
      const initialPairs = stems.map((stem) => ({
        stem_id: stem.id,
        option_id: "",
      }));
      setCorrectPairs(initialPairs);
    }
  }, [stems, options, correctPairs.length]);

  // Update parent component when form data changes
  useEffect(() => {
    onChange({
      stems,
      options,
      correct_pairs: correctPairs.filter(pair => pair.stem_id && pair.option_id),
    });
  }, [stems, options, correctPairs, onChange]);

  const handleStemTextChange = (id: string, text: string) => {
    setStems(
      stems.map((stem) =>
        stem.id === id ? { ...stem, text } : stem
      )
    );
  };

  const handleOptionTextChange = (id: string, text: string) => {
    setOptions(
      options.map((option) =>
        option.id === id ? { ...option, text } : option
      )
    );
  };

  const handlePairChange = (stemId: string, optionId: string) => {
    setCorrectPairs(
      correctPairs.map((pair) =>
        pair.stem_id === stemId ? { ...pair, option_id: optionId } : pair
      )
    );
  };

  const addStem = () => {
    const newStemId = generateUUID();
    setStems([...stems, { id: newStemId, text: "" }]);
    setCorrectPairs([...correctPairs, { stem_id: newStemId, option_id: "" }]);
  };

  const removeStem = (id: string) => {
    if (stems.length <= 2) {
      alert("A matching question must have at least 2 stems.");
      return;
    }
    setStems(stems.filter((stem) => stem.id !== id));
    setCorrectPairs(correctPairs.filter((pair) => pair.stem_id !== id));
  };

  const addOption = () => {
    setOptions([...options, { id: generateUUID(), text: "" }]);
  };

  const removeOption = (id: string) => {
    if (options.length <= 2) {
      alert("A matching question must have at least 2 options.");
      return;
    }
    setOptions(options.filter((option) => option.id !== id));
    setCorrectPairs(
      correctPairs.map((pair) =>
        pair.option_id === id ? { ...pair, option_id: "" } : pair
      )
    );
  };

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <label className="text-sm font-medium">Stems (Left Side)</label>
        {stems.map((stem, index) => (
          <div key={stem.id} className="flex items-center space-x-2">
            <input
              type="text"
              value={stem.text}
              onChange={(e) => handleStemTextChange(stem.id, e.target.value)}
              className="flex-1 p-2 border rounded-md"
              placeholder={`Stem ${index + 1}`}
              required
            />
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => removeStem(stem.id)}
              className="h-8 w-8 p-0"
              disabled={stems.length <= 2}
            >
              <span className="sr-only">Remove</span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                />
              </svg>
            </Button>
          </div>
        ))}
        <Button
          type="button"
          variant="outline"
          onClick={addStem}
          className="w-full"
        >
          Add Stem
        </Button>
      </div>

      <div className="space-y-4">
        <label className="text-sm font-medium">Options (Right Side)</label>
        {options.map((option, index) => (
          <div key={option.id} className="flex items-center space-x-2">
            <input
              type="text"
              value={option.text}
              onChange={(e) => handleOptionTextChange(option.id, e.target.value)}
              className="flex-1 p-2 border rounded-md"
              placeholder={`Option ${index + 1}`}
              required
            />
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => removeOption(option.id)}
              className="h-8 w-8 p-0"
              disabled={options.length <= 2}
            >
              <span className="sr-only">Remove</span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                />
              </svg>
            </Button>
          </div>
        ))}
        <Button
          type="button"
          variant="outline"
          onClick={addOption}
          className="w-full"
        >
          Add Option
        </Button>
      </div>

      <div className="space-y-4">
        <label className="text-sm font-medium">Correct Matches</label>
        <p className="text-xs text-muted-foreground">
          For each stem, select the matching option.
        </p>
        
        {stems.map((stem, index) => {
          const pair = correctPairs.find((p) => p.stem_id === stem.id);
          
          return (
            <div key={stem.id} className="flex items-center space-x-2">
              <div className="flex-1 p-2 border rounded-md bg-muted">
                {stem.text || `Stem ${index + 1}`}
              </div>
              <div className="text-center px-2">matches</div>
              <select
                value={pair?.option_id || ""}
                onChange={(e) => handlePairChange(stem.id, e.target.value)}
                className="flex-1 p-2 border rounded-md"
                required
              >
                <option value="">-- Select matching option --</option>
                {options.map((option) => (
                  <option key={option.id} value={option.id}>
                    {option.text || `Option ${options.findIndex((o) => o.id === option.id) + 1}`}
                  </option>
                ))}
              </select>
            </div>
          );
        })}
      </div>
    </div>
  );
}
