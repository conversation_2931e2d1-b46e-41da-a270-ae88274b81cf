"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";

interface ShortAnswerFormProps {
  data: any;
  onChange: (data: any) => void;
}

export default function ShortAnswerForm({ data, onChange }: ShortAnswerFormProps) {
  const [correctAnswers, setCorrectAnswers] = useState<string[]>(
    data?.correct_answers || [""]
  );
  const [caseSensitive, setCaseSensitive] = useState(
    data?.case_sensitive !== undefined ? data.case_sensitive : false
  );
  const [trimWhitespace, setTrimWhitespace] = useState(
    data?.trim_whitespace !== undefined ? data.trim_whitespace : true
  );
  const [exactMatch, setExactMatch] = useState(
    data?.exact_match !== undefined ? data.exact_match : true
  );

  // Update parent component when form data changes
  useEffect(() => {
    onChange({
      correct_answers: correctAnswers.filter(answer => answer.trim() !== ""),
      case_sensitive: caseSensitive,
      trim_whitespace: trimWhitespace,
      exact_match: exactMatch,
    });
  }, [correctAnswers, caseSensitive, trimWhitespace, exactMatch, onChange]);

  const handleAnswerChange = (index: number, value: string) => {
    const newAnswers = [...correctAnswers];
    newAnswers[index] = value;
    setCorrectAnswers(newAnswers);
  };

  const addAnswer = () => {
    setCorrectAnswers([...correctAnswers, ""]);
  };

  const removeAnswer = (index: number) => {
    if (correctAnswers.length <= 1) {
      alert("You must have at least one correct answer.");
      return;
    }
    const newAnswers = [...correctAnswers];
    newAnswers.splice(index, 1);
    setCorrectAnswers(newAnswers);
  };

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <label className="text-sm font-medium">Correct Answers</label>
        <p className="text-xs text-muted-foreground">
          Add multiple acceptable answers. The question will be marked correct if the student's answer matches any of these.
        </p>
        
        {correctAnswers.map((answer, index) => (
          <div key={index} className="flex items-center space-x-2">
            <input
              type="text"
              value={answer}
              onChange={(e) => handleAnswerChange(index, e.target.value)}
              className="flex-1 p-2 border rounded-md"
              placeholder="Enter a correct answer"
              required
            />
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => removeAnswer(index)}
              className="h-8 w-8 p-0"
              disabled={correctAnswers.length <= 1}
            >
              <span className="sr-only">Remove</span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                />
              </svg>
            </Button>
          </div>
        ))}
        
        <Button
          type="button"
          variant="outline"
          onClick={addAnswer}
          className="w-full"
        >
          Add Another Correct Answer
        </Button>
      </div>

      <div className="space-y-2">
        <label className="text-sm font-medium">Answer Options</label>
        <div className="space-y-2">
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={caseSensitive}
              onChange={(e) => setCaseSensitive(e.target.checked)}
            />
            <span>Case Sensitive</span>
          </label>
          <p className="text-xs text-muted-foreground ml-6">
            If checked, "Answer" and "answer" will be treated as different answers.
          </p>
        </div>
        
        <div className="space-y-2">
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={trimWhitespace}
              onChange={(e) => setTrimWhitespace(e.target.checked)}
            />
            <span>Trim Whitespace</span>
          </label>
          <p className="text-xs text-muted-foreground ml-6">
            If checked, leading and trailing spaces will be ignored.
          </p>
        </div>
        
        <div className="space-y-2">
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={exactMatch}
              onChange={(e) => setExactMatch(e.target.checked)}
            />
            <span>Exact Match Required</span>
          </label>
          <p className="text-xs text-muted-foreground ml-6">
            If unchecked, partial matches or keyword detection may be used (implementation dependent).
          </p>
        </div>
      </div>
    </div>
  );
}
