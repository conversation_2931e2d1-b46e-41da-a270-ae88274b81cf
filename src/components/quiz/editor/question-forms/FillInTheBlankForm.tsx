"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { generateUUID } from "@/lib/utils";

interface FillInTheBlankFormProps {
  data: any;
  onChange: (data: any) => void;
}

export default function FillInTheBlankForm({ data, onChange }: FillInTheBlankFormProps) {
  const [textTemplate, setTextTemplate] = useState(
    data?.text_template || "This is a [BLANK] question with [BLANK] to fill in."
  );
  
  const [blanks, setBlanks] = useState<Array<{
    id: string;
    correct_answers: string[];
    case_sensitive?: boolean;
    trim_whitespace?: boolean;
    hint?: string;
  }>>(
    data?.blanks || []
  );

  // Initialize blanks based on template
  useEffect(() => {
    const blankCount = (textTemplate.match(/\[BLANK\]/g) || []).length;
    
    if (blanks.length !== blankCount) {
      const newBlanks = Array(blankCount).fill(null).map((_, index) => {
        // Reuse existing blank if available
        if (index < blanks.length) {
          return blanks[index];
        }
        
        // Create new blank
        return {
          id: generateUUID(),
          correct_answers: [""],
          case_sensitive: false,
          trim_whitespace: true,
          hint: "",
        };
      });
      
      setBlanks(newBlanks);
    }
  }, [textTemplate, blanks.length]);

  // Update parent component when form data changes
  useEffect(() => {
    onChange({
      text_template: textTemplate,
      blanks,
    });
  }, [textTemplate, blanks, onChange]);

  const handleBlankAnswerChange = (blankIndex: number, answerIndex: number, value: string) => {
    const newBlanks = [...blanks];
    if (newBlanks[blankIndex]) {
      const newAnswers = [...newBlanks[blankIndex].correct_answers];
      newAnswers[answerIndex] = value;
      newBlanks[blankIndex] = {
        ...newBlanks[blankIndex],
        correct_answers: newAnswers,
      };
      setBlanks(newBlanks);
    }
  };

  const handleBlankPropertyChange = (blankIndex: number, property: string, value: any) => {
    const newBlanks = [...blanks];
    if (newBlanks[blankIndex]) {
      newBlanks[blankIndex] = {
        ...newBlanks[blankIndex],
        [property]: value,
      };
      setBlanks(newBlanks);
    }
  };

  const addAnswerToBlank = (blankIndex: number) => {
    const newBlanks = [...blanks];
    if (newBlanks[blankIndex]) {
      newBlanks[blankIndex] = {
        ...newBlanks[blankIndex],
        correct_answers: [...newBlanks[blankIndex].correct_answers, ""],
      };
      setBlanks(newBlanks);
    }
  };

  const removeAnswerFromBlank = (blankIndex: number, answerIndex: number) => {
    const newBlanks = [...blanks];
    if (newBlanks[blankIndex] && newBlanks[blankIndex].correct_answers.length > 1) {
      const newAnswers = [...newBlanks[blankIndex].correct_answers];
      newAnswers.splice(answerIndex, 1);
      newBlanks[blankIndex] = {
        ...newBlanks[blankIndex],
        correct_answers: newAnswers,
      };
      setBlanks(newBlanks);
    }
  };

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <label htmlFor="textTemplate" className="text-sm font-medium">
          Question Text with Blanks
        </label>
        <p className="text-xs text-muted-foreground">
          Use [BLANK] to indicate where students should fill in answers.
        </p>
        <textarea
          id="textTemplate"
          value={textTemplate}
          onChange={(e) => setTextTemplate(e.target.value)}
          className="w-full p-2 border rounded-md min-h-[100px]"
          placeholder="Enter text with [BLANK] placeholders"
          required
        />
      </div>

      {blanks.length > 0 ? (
        <div className="space-y-6">
          <h3 className="text-sm font-medium">Define Blanks</h3>
          
          {blanks.map((blank, blankIndex) => (
            <div key={blank.id} className="border rounded-md p-4 space-y-4">
              <h4 className="font-medium">Blank {blankIndex + 1}</h4>
              
              <div className="space-y-4">
                <label className="text-sm font-medium">Correct Answers</label>
                {blank.correct_answers.map((answer, answerIndex) => (
                  <div key={answerIndex} className="flex items-center space-x-2">
                    <input
                      type="text"
                      value={answer}
                      onChange={(e) => handleBlankAnswerChange(blankIndex, answerIndex, e.target.value)}
                      className="flex-1 p-2 border rounded-md"
                      placeholder="Enter a correct answer"
                      required
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeAnswerFromBlank(blankIndex, answerIndex)}
                      className="h-8 w-8 p-0"
                      disabled={blank.correct_answers.length <= 1}
                    >
                      <span className="sr-only">Remove</span>
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-4 w-4"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                        />
                      </svg>
                    </Button>
                  </div>
                ))}
                
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => addAnswerToBlank(blankIndex)}
                  className="w-full"
                >
                  Add Another Correct Answer
                </Button>
              </div>
              
              <div className="space-y-2">
                <label className="text-sm font-medium">Answer Options</label>
                <div className="space-y-2">
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={blank.case_sensitive}
                      onChange={(e) => handleBlankPropertyChange(blankIndex, "case_sensitive", e.target.checked)}
                    />
                    <span>Case Sensitive</span>
                  </label>
                </div>
                
                <div className="space-y-2">
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={blank.trim_whitespace}
                      onChange={(e) => handleBlankPropertyChange(blankIndex, "trim_whitespace", e.target.checked)}
                    />
                    <span>Trim Whitespace</span>
                  </label>
                </div>
              </div>
              
              <div className="space-y-2">
                <label htmlFor={`hint-${blankIndex}`} className="text-sm font-medium">
                  Hint (Optional)
                </label>
                <input
                  id={`hint-${blankIndex}`}
                  type="text"
                  value={blank.hint || ""}
                  onChange={(e) => handleBlankPropertyChange(blankIndex, "hint", e.target.value)}
                  className="w-full p-2 border rounded-md"
                  placeholder="Enter a hint for this blank"
                />
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="p-4 bg-muted rounded-md">
          <p className="text-center">
            Add [BLANK] placeholders to your text to create blanks for students to fill in.
          </p>
        </div>
      )}
    </div>
  );
}
