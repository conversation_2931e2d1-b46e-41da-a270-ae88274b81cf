"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { generateUUID } from "@/lib/utils";

interface MultipleChoiceFormProps {
  data: any;
  onChange: (data: any) => void;
}

export default function MultipleChoiceForm({ data, onChange }: MultipleChoiceFormProps) {
  const [singleCorrectAnswer, setSingleCorrectAnswer] = useState(
    data?.single_correct_answer !== undefined ? data.single_correct_answer : true
  );
  
  const [options, setOptions] = useState<Array<{
    id: string;
    text: string;
    is_correct: boolean;
    feedback?: string;
  }>>(
    data?.options || [
      { id: generateUUID(), text: "", is_correct: false },
      { id: generateUUID(), text: "", is_correct: false },
    ]
  );
  
  const [scoringMethod, setScoringMethod] = useState(
    data?.scoring_method || "all_or_nothing"
  );

  // Update parent component when form data changes
  useEffect(() => {
    onChange({
      single_correct_answer: singleCorrectAnswer,
      options,
      scoring_method: scoringMethod,
    });
  }, [singleCorrectAnswer, options, scoringMethod, onChange]);

  const handleOptionTextChange = (id: string, text: string) => {
    setOptions(
      options.map((option) =>
        option.id === id ? { ...option, text } : option
      )
    );
  };

  const handleOptionCorrectChange = (id: string, isCorrect: boolean) => {
    if (singleCorrectAnswer && isCorrect) {
      // For single-choice questions, only one option can be correct
      setOptions(
        options.map((option) => ({
          ...option,
          is_correct: option.id === id,
        }))
      );
    } else {
      // For multiple-choice questions, multiple options can be correct
      setOptions(
        options.map((option) =>
          option.id === id ? { ...option, is_correct: isCorrect } : option
        )
      );
    }
  };

  const handleOptionFeedbackChange = (id: string, feedback: string) => {
    setOptions(
      options.map((option) =>
        option.id === id ? { ...option, feedback } : option
      )
    );
  };

  const addOption = () => {
    setOptions([
      ...options,
      { id: generateUUID(), text: "", is_correct: false },
    ]);
  };

  const removeOption = (id: string) => {
    if (options.length <= 2) {
      alert("A multiple choice question must have at least 2 options.");
      return;
    }
    setOptions(options.filter((option) => option.id !== id));
  };

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <label className="text-sm font-medium">Question Type</label>
        <div className="flex space-x-4">
          <label className="flex items-center space-x-2">
            <input
              type="radio"
              checked={singleCorrectAnswer}
              onChange={() => setSingleCorrectAnswer(true)}
            />
            <span>Single Choice (Radio Buttons)</span>
          </label>
          <label className="flex items-center space-x-2">
            <input
              type="radio"
              checked={!singleCorrectAnswer}
              onChange={() => setSingleCorrectAnswer(false)}
            />
            <span>Multiple Choice (Checkboxes)</span>
          </label>
        </div>
      </div>

      {!singleCorrectAnswer && (
        <div className="space-y-2">
          <label className="text-sm font-medium">Scoring Method</label>
          <div className="flex space-x-4">
            <label className="flex items-center space-x-2">
              <input
                type="radio"
                checked={scoringMethod === "all_or_nothing"}
                onChange={() => setScoringMethod("all_or_nothing")}
              />
              <span>All or Nothing</span>
            </label>
            <label className="flex items-center space-x-2">
              <input
                type="radio"
                checked={scoringMethod === "partial_credit"}
                onChange={() => setScoringMethod("partial_credit")}
              />
              <span>Partial Credit</span>
            </label>
          </div>
          <p className="text-xs text-muted-foreground">
            All or Nothing: Full points only if all correct options are selected and no incorrect options.
            <br />
            Partial Credit: Points awarded based on correct selections.
          </p>
        </div>
      )}

      <div className="space-y-4">
        <label className="text-sm font-medium">Options</label>
        {options.map((option, index) => (
          <div key={option.id} className="border rounded-md p-4 space-y-4">
            <div className="flex items-center justify-between">
              <h4 className="font-medium">Option {index + 1}</h4>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => removeOption(option.id)}
                className="h-8 w-8 p-0"
              >
                <span className="sr-only">Remove</span>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-4 w-4"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                  />
                </svg>
              </Button>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Option Text</label>
              <input
                type="text"
                value={option.text}
                onChange={(e) => handleOptionTextChange(option.id, e.target.value)}
                className="w-full p-2 border rounded-md"
                placeholder="Enter option text"
                required
              />
            </div>
            <div className="space-y-2">
              <label className="flex items-center space-x-2">
                <input
                  type={singleCorrectAnswer ? "radio" : "checkbox"}
                  checked={option.is_correct}
                  onChange={(e) => handleOptionCorrectChange(option.id, e.target.checked)}
                  name="correctOption"
                />
                <span>Correct Answer</span>
              </label>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Feedback for this option</label>
              <input
                type="text"
                value={option.feedback || ""}
                onChange={(e) => handleOptionFeedbackChange(option.id, e.target.value)}
                className="w-full p-2 border rounded-md"
                placeholder="Optional feedback when this option is selected"
              />
            </div>
          </div>
        ))}
        <Button
          type="button"
          variant="outline"
          onClick={addOption}
          className="w-full"
        >
          Add Option
        </Button>
      </div>
    </div>
  );
}
