"use client";

import { useState } from "react";
import { Quiz, Question, QuestionPool, SelectionRule } from "@prisma/client";
import { But<PERSON> } from "@/components/ui/button";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { generateUUID } from "@/lib/utils";

type QuizWithRelations = Quiz & {
  questions: Question[];
  questionPools: (QuestionPool & {
    questions: Question[];
  })[];
  selectionRules: SelectionRule[];
};

interface PoolsManagerProps {
  quiz: QuizWithRelations;
  setQuiz: React.Dispatch<React.SetStateAction<QuizWithRelations>>;
  isSaving: boolean;
}

export default function PoolsManager({ quiz, setQuiz, isSaving }: PoolsManagerProps) {
  const [activeTab, setActiveTab] = useState("pools");
  const [selectedPoolId, setSelectedPoolId] = useState<string | null>(null);
  const [newPoolTitle, setNewPoolTitle] = useState("");
  const [newPoolDescription, setNewPoolDescription] = useState("");
  const [editingPool, setEditingPool] = useState<{
    id: string;
    title: string;
    description: string;
  } | null>(null);
  const [savingPool, setSavingPool] = useState(false);

  // Selection rules state
  const [selectedRuleId, setSelectedRuleId] = useState<string | null>(null);
  const [newRulePoolId, setNewRulePoolId] = useState("");
  const [newRuleSelectCount, setNewRuleSelectCount] = useState("1");
  const [newRuleRandomize, setNewRuleRandomize] = useState(true);
  const [newRuleShuffleOrder, setNewRuleShuffleOrder] = useState(false);
  const [editingRule, setEditingRule] = useState<{
    id: string;
    poolId: string;
    selectCount: number;
    randomize: boolean;
    shuffleOrder: boolean;
  } | null>(null);
  const [savingRule, setSavingRule] = useState(false);

  // Pool questions management
  const [availableQuestions, setAvailableQuestions] = useState<Question[]>([]);
  const [poolQuestions, setPoolQuestions] = useState<Question[]>([]);

  // Load pool details when a pool is selected
  const handleSelectPool = async (poolId: string) => {
    setSelectedPoolId(poolId);
    setSelectedRuleId(null);
    
    const pool = quiz.questionPools.find(p => p.id === poolId);
    if (pool) {
      setEditingPool({
        id: pool.id,
        title: pool.title || "",
        description: pool.description || "",
      });
      
      // Load questions for this pool
      setPoolQuestions(pool.questions);
      
      // Set available questions (those not in any pool)
      const questionsInPools = quiz.questionPools.flatMap(p => p.questions.map(q => q.id));
      setAvailableQuestions(quiz.questions.filter(q => !questionsInPools.includes(q.id)));
    }
  };

  // Load rule details when a rule is selected
  const handleSelectRule = (ruleId: string) => {
    setSelectedRuleId(ruleId);
    setSelectedPoolId(null);
    
    const rule = quiz.selectionRules.find(r => r.id === ruleId);
    if (rule) {
      setEditingRule({
        id: rule.id,
        poolId: rule.poolId,
        selectCount: rule.selectCount,
        randomize: rule.randomize,
        shuffleOrder: rule.shuffleOrder,
      });
    }
  };

  // Create a new pool
  const handleCreatePool = async () => {
    setSavingPool(true);
    
    try {
      const response = await fetch(`/api/quizzes/${quiz.id}/pools`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          poolId: generateUUID(),
          title: newPoolTitle,
          description: newPoolDescription,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to create pool");
      }

      const newPool = await response.json();
      
      setQuiz(prev => ({
        ...prev,
        questionPools: [...prev.questionPools, { ...newPool, questions: [] }],
      }));
      
      setNewPoolTitle("");
      setNewPoolDescription("");
      setActiveTab("pools");
    } catch (error) {
      console.error("Error creating pool:", error);
    } finally {
      setSavingPool(false);
    }
  };

  // Update an existing pool
  const handleUpdatePool = async () => {
    if (!editingPool) return;
    
    setSavingPool(true);
    
    try {
      const response = await fetch(`/api/quizzes/${quiz.id}/pools/${editingPool.id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          title: editingPool.title,
          description: editingPool.description,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to update pool");
      }

      const updatedPool = await response.json();
      
      setQuiz(prev => ({
        ...prev,
        questionPools: prev.questionPools.map(pool => 
          pool.id === editingPool.id 
            ? { ...pool, ...updatedPool } 
            : pool
        ),
      }));
    } catch (error) {
      console.error("Error updating pool:", error);
    } finally {
      setSavingPool(false);
    }
  };

  // Delete a pool
  const handleDeletePool = async (poolId: string) => {
    if (!confirm("Are you sure you want to delete this pool? This will also delete any selection rules that use this pool.")) {
      return;
    }
    
    try {
      const response = await fetch(`/api/quizzes/${quiz.id}/pools/${poolId}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        throw new Error("Failed to delete pool");
      }
      
      setQuiz(prev => ({
        ...prev,
        questionPools: prev.questionPools.filter(pool => pool.id !== poolId),
        selectionRules: prev.selectionRules.filter(rule => rule.poolId !== poolId),
      }));
      
      if (selectedPoolId === poolId) {
        setSelectedPoolId(null);
        setEditingPool(null);
      }
    } catch (error) {
      console.error("Error deleting pool:", error);
    }
  };

  // Create a new selection rule
  const handleCreateRule = async () => {
    setSavingRule(true);
    
    try {
      const response = await fetch(`/api/quizzes/${quiz.id}/rules`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          poolId: newRulePoolId,
          selectCount: parseInt(newRuleSelectCount),
          randomize: newRuleRandomize,
          shuffleOrder: newRuleShuffleOrder,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to create rule");
      }

      const newRule = await response.json();
      
      setQuiz(prev => ({
        ...prev,
        selectionRules: [...prev.selectionRules, newRule],
      }));
      
      setNewRulePoolId("");
      setNewRuleSelectCount("1");
      setNewRuleRandomize(true);
      setNewRuleShuffleOrder(false);
      setActiveTab("rules");
    } catch (error) {
      console.error("Error creating rule:", error);
    } finally {
      setSavingRule(false);
    }
  };

  // Update an existing rule
  const handleUpdateRule = async () => {
    if (!editingRule) return;
    
    setSavingRule(true);
    
    try {
      const response = await fetch(`/api/quizzes/${quiz.id}/rules/${editingRule.id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          poolId: editingRule.poolId,
          selectCount: editingRule.selectCount,
          randomize: editingRule.randomize,
          shuffleOrder: editingRule.shuffleOrder,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to update rule");
      }

      const updatedRule = await response.json();
      
      setQuiz(prev => ({
        ...prev,
        selectionRules: prev.selectionRules.map(rule => 
          rule.id === editingRule.id 
            ? { ...rule, ...updatedRule } 
            : rule
        ),
      }));
    } catch (error) {
      console.error("Error updating rule:", error);
    } finally {
      setSavingRule(false);
    }
  };

  // Delete a rule
  const handleDeleteRule = async (ruleId: string) => {
    if (!confirm("Are you sure you want to delete this selection rule?")) {
      return;
    }
    
    try {
      const response = await fetch(`/api/quizzes/${quiz.id}/rules/${ruleId}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        throw new Error("Failed to delete rule");
      }
      
      setQuiz(prev => ({
        ...prev,
        selectionRules: prev.selectionRules.filter(rule => rule.id !== ruleId),
      }));
      
      if (selectedRuleId === ruleId) {
        setSelectedRuleId(null);
        setEditingRule(null);
      }
    } catch (error) {
      console.error("Error deleting rule:", error);
    }
  };

  return (
    <div className="space-y-6">
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid grid-cols-3 w-full max-w-md">
          <TabsTrigger value="pools">Question Pools</TabsTrigger>
          <TabsTrigger value="rules">Selection Rules</TabsTrigger>
          <TabsTrigger value="create">Create New</TabsTrigger>
        </TabsList>

        <TabsContent value="pools">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="border rounded-md p-4 h-[500px] overflow-y-auto">
              <h3 className="font-medium mb-4">Pools ({quiz.questionPools.length})</h3>
              
              {quiz.questionPools.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-muted-foreground mb-4">
                    No question pools yet. Create your first pool to get started.
                  </p>
                  <Button onClick={() => setActiveTab("create")}>
                    Create First Pool
                  </Button>
                </div>
              ) : (
                <ul className="space-y-2">
                  {quiz.questionPools.map((pool) => (
                    <li
                      key={pool.id}
                      className={`p-3 border rounded-md cursor-pointer transition-colors ${
                        selectedPoolId === pool.id
                          ? "border-primary bg-primary/5"
                          : "hover:border-primary/50"
                      }`}
                      onClick={() => handleSelectPool(pool.id)}
                    >
                      <div className="flex justify-between items-start">
                        <div>
                          <p className="font-medium">{pool.title || `Pool ${pool.poolId}`}</p>
                          <p className="text-xs text-muted-foreground">
                            {pool.questions.length} questions
                          </p>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDeletePool(pool.id);
                          }}
                        >
                          <span className="sr-only">Delete</span>
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-4 w-4"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                            />
                          </svg>
                        </Button>
                      </div>
                    </li>
                  ))}
                </ul>
              )}
            </div>

            <div className="border rounded-md p-4 h-[500px] overflow-y-auto">
              {selectedPoolId && editingPool ? (
                <div className="space-y-4">
                  <h3 className="font-medium">Edit Pool</h3>
                  
                  <div className="space-y-2">
                    <label htmlFor="poolTitle" className="text-sm font-medium">
                      Pool Title
                    </label>
                    <input
                      id="poolTitle"
                      type="text"
                      value={editingPool.title}
                      onChange={(e) => setEditingPool({...editingPool, title: e.target.value})}
                      className="w-full p-2 border rounded-md"
                      placeholder="Enter pool title"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <label htmlFor="poolDescription" className="text-sm font-medium">
                      Description
                    </label>
                    <textarea
                      id="poolDescription"
                      value={editingPool.description}
                      onChange={(e) => setEditingPool({...editingPool, description: e.target.value})}
                      className="w-full p-2 border rounded-md"
                      placeholder="Enter pool description"
                    />
                  </div>
                  
                  <div className="flex justify-end">
                    <Button
                      onClick={handleUpdatePool}
                      disabled={savingPool}
                    >
                      {savingPool ? "Saving..." : "Save Pool"}
                    </Button>
                  </div>
                  
                  <div className="pt-4 border-t mt-4">
                    <h4 className="font-medium mb-2">Questions in this Pool</h4>
                    {/* Pool questions management would go here */}
                  </div>
                </div>
              ) : (
                <div className="flex items-center justify-center h-full">
                  <p className="text-muted-foreground">
                    Select a pool to edit
                  </p>
                </div>
              )}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="rules">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="border rounded-md p-4 h-[500px] overflow-y-auto">
              <h3 className="font-medium mb-4">Selection Rules ({quiz.selectionRules.length})</h3>
              
              {quiz.selectionRules.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-muted-foreground mb-4">
                    No selection rules yet. Create your first rule to get started.
                  </p>
                  <Button onClick={() => setActiveTab("create")}>
                    Create First Rule
                  </Button>
                </div>
              ) : (
                <ul className="space-y-2">
                  {quiz.selectionRules.map((rule) => {
                    const pool = quiz.questionPools.find(p => p.id === rule.poolId);
                    
                    return (
                      <li
                        key={rule.id}
                        className={`p-3 border rounded-md cursor-pointer transition-colors ${
                          selectedRuleId === rule.id
                            ? "border-primary bg-primary/5"
                            : "hover:border-primary/50"
                        }`}
                        onClick={() => handleSelectRule(rule.id)}
                      >
                        <div className="flex justify-between items-start">
                          <div>
                            <p className="font-medium">
                              Select {rule.selectCount} from {pool?.title || rule.poolId}
                            </p>
                            <p className="text-xs text-muted-foreground">
                              {rule.randomize ? "Random selection" : "Sequential selection"}
                              {rule.shuffleOrder ? ", shuffled order" : ""}
                            </p>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDeleteRule(rule.id);
                            }}
                          >
                            <span className="sr-only">Delete</span>
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              className="h-4 w-4"
                              fill="none"
                              viewBox="0 0 24 24"
                              stroke="currentColor"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                              />
                            </svg>
                          </Button>
                        </div>
                      </li>
                    );
                  })}
                </ul>
              )}
            </div>

            <div className="border rounded-md p-4 h-[500px] overflow-y-auto">
              {selectedRuleId && editingRule ? (
                <div className="space-y-4">
                  <h3 className="font-medium">Edit Selection Rule</h3>
                  
                  <div className="space-y-2">
                    <label htmlFor="rulePool" className="text-sm font-medium">
                      Question Pool
                    </label>
                    <select
                      id="rulePool"
                      value={editingRule.poolId}
                      onChange={(e) => setEditingRule({...editingRule, poolId: e.target.value})}
                      className="w-full p-2 border rounded-md"
                      required
                    >
                      <option value="">-- Select a pool --</option>
                      {quiz.questionPools.map((pool) => (
                        <option key={pool.id} value={pool.id}>
                          {pool.title || `Pool ${pool.poolId}`} ({pool.questions.length} questions)
                        </option>
                      ))}
                    </select>
                  </div>
                  
                  <div className="space-y-2">
                    <label htmlFor="ruleSelectCount" className="text-sm font-medium">
                      Number of Questions to Select
                    </label>
                    <input
                      id="ruleSelectCount"
                      type="number"
                      min="1"
                      value={editingRule.selectCount}
                      onChange={(e) => setEditingRule({...editingRule, selectCount: parseInt(e.target.value)})}
                      className="w-full p-2 border rounded-md"
                      required
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={editingRule.randomize}
                        onChange={(e) => setEditingRule({...editingRule, randomize: e.target.checked})}
                      />
                      <span>Randomize Selection</span>
                    </label>
                    <p className="text-xs text-muted-foreground ml-6">
                      If checked, questions will be randomly selected from the pool.
                    </p>
                  </div>
                  
                  <div className="space-y-2">
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={editingRule.shuffleOrder}
                        onChange={(e) => setEditingRule({...editingRule, shuffleOrder: e.target.checked})}
                      />
                      <span>Shuffle Order</span>
                    </label>
                    <p className="text-xs text-muted-foreground ml-6">
                      If checked, the order of selected questions will be randomized.
                    </p>
                  </div>
                  
                  <div className="flex justify-end">
                    <Button
                      onClick={handleUpdateRule}
                      disabled={savingRule}
                    >
                      {savingRule ? "Saving..." : "Save Rule"}
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="flex items-center justify-center h-full">
                  <p className="text-muted-foreground">
                    Select a rule to edit
                  </p>
                </div>
              )}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="create">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="border rounded-md p-4">
              <h3 className="font-medium mb-4">Create New Pool</h3>
              
              <div className="space-y-4">
                <div className="space-y-2">
                  <label htmlFor="newPoolTitle" className="text-sm font-medium">
                    Pool Title
                  </label>
                  <input
                    id="newPoolTitle"
                    type="text"
                    value={newPoolTitle}
                    onChange={(e) => setNewPoolTitle(e.target.value)}
                    className="w-full p-2 border rounded-md"
                    placeholder="Enter pool title"
                    required
                  />
                </div>
                
                <div className="space-y-2">
                  <label htmlFor="newPoolDescription" className="text-sm font-medium">
                    Description
                  </label>
                  <textarea
                    id="newPoolDescription"
                    value={newPoolDescription}
                    onChange={(e) => setNewPoolDescription(e.target.value)}
                    className="w-full p-2 border rounded-md"
                    placeholder="Enter pool description (optional)"
                  />
                </div>
                
                <div className="flex justify-end">
                  <Button
                    onClick={handleCreatePool}
                    disabled={!newPoolTitle || savingPool}
                  >
                    {savingPool ? "Creating..." : "Create Pool"}
                  </Button>
                </div>
              </div>
            </div>
            
            <div className="border rounded-md p-4">
              <h3 className="font-medium mb-4">Create New Selection Rule</h3>
              
              <div className="space-y-4">
                <div className="space-y-2">
                  <label htmlFor="newRulePool" className="text-sm font-medium">
                    Question Pool
                  </label>
                  <select
                    id="newRulePool"
                    value={newRulePoolId}
                    onChange={(e) => setNewRulePoolId(e.target.value)}
                    className="w-full p-2 border rounded-md"
                    required
                  >
                    <option value="">-- Select a pool --</option>
                    {quiz.questionPools.map((pool) => (
                      <option key={pool.id} value={pool.id}>
                        {pool.title || `Pool ${pool.poolId}`} ({pool.questions.length} questions)
                      </option>
                    ))}
                  </select>
                </div>
                
                <div className="space-y-2">
                  <label htmlFor="newRuleSelectCount" className="text-sm font-medium">
                    Number of Questions to Select
                  </label>
                  <input
                    id="newRuleSelectCount"
                    type="number"
                    min="1"
                    value={newRuleSelectCount}
                    onChange={(e) => setNewRuleSelectCount(e.target.value)}
                    className="w-full p-2 border rounded-md"
                    required
                  />
                </div>
                
                <div className="space-y-2">
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={newRuleRandomize}
                      onChange={(e) => setNewRuleRandomize(e.target.checked)}
                    />
                    <span>Randomize Selection</span>
                  </label>
                  <p className="text-xs text-muted-foreground ml-6">
                    If checked, questions will be randomly selected from the pool.
                  </p>
                </div>
                
                <div className="space-y-2">
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={newRuleShuffleOrder}
                      onChange={(e) => setNewRuleShuffleOrder(e.target.checked)}
                    />
                    <span>Shuffle Order</span>
                  </label>
                  <p className="text-xs text-muted-foreground ml-6">
                    If checked, the order of selected questions will be randomized.
                  </p>
                </div>
                
                <div className="flex justify-end">
                  <Button
                    onClick={handleCreateRule}
                    disabled={!newRulePoolId || !newRuleSelectCount || savingRule}
                  >
                    {savingRule ? "Creating..." : "Create Rule"}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
