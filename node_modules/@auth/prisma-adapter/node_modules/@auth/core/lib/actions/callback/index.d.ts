import type { InternalOptions, RequestInternal, ResponseInternal } from "../../../types.js";
import type { <PERSON><PERSON>, SessionStore } from "../../utils/cookie.js";
/** Handle callbacks from login services */
export declare function callback(request: RequestInternal, options: InternalOptions, sessionStore: SessionStore, cookies: <PERSON>ie[]): Promise<ResponseInternal>;
//# sourceMappingURL=index.d.ts.map