[{"name": "generate-buildid", "duration": 227, "timestamp": 242804048823, "id": 4, "parentId": 1, "tags": {}, "startTime": 1748095324991, "traceId": "678786a70a75da4f"}, {"name": "load-custom-routes", "duration": 160, "timestamp": 242804049108, "id": 5, "parentId": 1, "tags": {}, "startTime": 1748095324991, "traceId": "678786a70a75da4f"}, {"name": "create-dist-dir", "duration": 143, "timestamp": 242804079718, "id": 6, "parentId": 1, "tags": {}, "startTime": 1748095325022, "traceId": "678786a70a75da4f"}, {"name": "create-pages-mapping", "duration": 161, "timestamp": 242804129770, "id": 7, "parentId": 1, "tags": {}, "startTime": 1748095325072, "traceId": "678786a70a75da4f"}, {"name": "collect-app-paths", "duration": 2271, "timestamp": 242804129953, "id": 8, "parentId": 1, "tags": {}, "startTime": 1748095325072, "traceId": "678786a70a75da4f"}, {"name": "create-app-mapping", "duration": 1553, "timestamp": 242804132237, "id": 9, "parentId": 1, "tags": {}, "startTime": 1748095325075, "traceId": "678786a70a75da4f"}, {"name": "public-dir-conflict-check", "duration": 274, "timestamp": 242804134125, "id": 10, "parentId": 1, "tags": {}, "startTime": 1748095325076, "traceId": "678786a70a75da4f"}, {"name": "generate-routes-manifest", "duration": 1862, "timestamp": 242804134480, "id": 11, "parentId": 1, "tags": {}, "startTime": 1748095325077, "traceId": "678786a70a75da4f"}, {"name": "next-build", "duration": 3781509, "timestamp": 242803980141, "id": 1, "tags": {"buildMode": "default", "isTurboBuild": "false", "version": "15.1.8", "has-custom-webpack-config": "false", "use-build-worker": "true"}, "startTime": 1748095324922, "traceId": "678786a70a75da4f"}]