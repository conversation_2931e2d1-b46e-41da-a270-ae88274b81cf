(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[983],{4258:(e,r,t)=>{Promise.resolve().then(t.bind(t,8468))},6046:(e,r,t)=>{"use strict";var n=t(6658);t.o(n,"usePathname")&&t.d(r,{usePathname:function(){return n.usePathname}}),t.o(n,"useRouter")&&t.d(r,{useRouter:function(){return n.useRouter}})},8468:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>u});var n=t(5155),a=t(2115),s=t(6046),l=t(8173),i=t.n(l),o=t(3312),d=t(9749);function u(){let e=(0,s.useRouter)(),[r,t]=(0,a.useState)(""),[l,u]=(0,a.useState)(""),[c,f]=(0,a.useState)(""),[m,x]=(0,a.useState)(""),[p,h]=(0,a.useState)(null),[g,y]=(0,a.useState)(!1),v=async t=>{if(t.preventDefault(),y(!0),h(null),c!==m){h("Passwords do not match"),y(!1);return}try{let t=await fetch("/api/auth/register",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:r,email:l,password:c})}),n=await t.json();if(!t.ok)throw Error(n.message||"Something went wrong");e.push("/auth/login?registered=true")}catch(e){e instanceof Error?h(e.message):h("An error occurred. Please try again."),y(!1)}};return(0,n.jsx)("div",{className:"flex min-h-screen items-center justify-center p-4",children:(0,n.jsxs)(d.Zp,{className:"w-full max-w-md",children:[(0,n.jsxs)(d.aR,{className:"space-y-1",children:[(0,n.jsx)(d.ZB,{className:"text-2xl font-bold",children:"Create an account"}),(0,n.jsx)(d.BT,{children:"Enter your information to create an account"})]}),(0,n.jsx)(d.Wu,{children:(0,n.jsxs)("form",{onSubmit:v,className:"space-y-4",children:[p&&(0,n.jsx)("div",{className:"p-3 text-sm bg-red-50 text-red-500 rounded-md",children:p}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)("label",{htmlFor:"name",className:"text-sm font-medium leading-none",children:"Name"}),(0,n.jsx)("input",{id:"name",type:"text",value:r,onChange:e=>t(e.target.value),className:"w-full p-2 border rounded-md",required:!0})]}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)("label",{htmlFor:"email",className:"text-sm font-medium leading-none",children:"Email"}),(0,n.jsx)("input",{id:"email",type:"email",value:l,onChange:e=>u(e.target.value),className:"w-full p-2 border rounded-md",required:!0})]}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)("label",{htmlFor:"password",className:"text-sm font-medium leading-none",children:"Password"}),(0,n.jsx)("input",{id:"password",type:"password",value:c,onChange:e=>f(e.target.value),className:"w-full p-2 border rounded-md",required:!0})]}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)("label",{htmlFor:"confirmPassword",className:"text-sm font-medium leading-none",children:"Confirm Password"}),(0,n.jsx)("input",{id:"confirmPassword",type:"password",value:m,onChange:e=>x(e.target.value),className:"w-full p-2 border rounded-md",required:!0})]}),(0,n.jsx)(o.$,{type:"submit",className:"w-full",disabled:g,children:g?"Creating account...":"Register"})]})}),(0,n.jsx)(d.wL,{className:"flex justify-center",children:(0,n.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Already have an account?"," ",(0,n.jsx)(i(),{href:"/auth/login",className:"text-primary hover:underline",children:"Sign in"})]})})]})})}},3312:(e,r,t)=>{"use strict";t.d(r,{$:()=>d});var n=t(5155),a=t(2115),s=t(2317),l=t(1027),i=t(1684);let o=(0,l.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef((e,r)=>{let{className:t,variant:a,size:l,asChild:d=!1,...u}=e,c=d?s.DX:"button";return(0,n.jsx)(c,{className:(0,i.cn)(o({variant:a,size:l,className:t})),ref:r,...u})});d.displayName="Button"},9749:(e,r,t)=>{"use strict";t.d(r,{BT:()=>d,Wu:()=>u,ZB:()=>o,Zp:()=>l,aR:()=>i,wL:()=>c});var n=t(5155),a=t(2115),s=t(1684);let l=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,n.jsx)("div",{ref:r,className:(0,s.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...a})});l.displayName="Card";let i=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,n.jsx)("div",{ref:r,className:(0,s.cn)("flex flex-col space-y-1.5 p-6",t),...a})});i.displayName="CardHeader";let o=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,n.jsx)("h3",{ref:r,className:(0,s.cn)("text-2xl font-semibold leading-none tracking-tight",t),...a})});o.displayName="CardTitle";let d=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,n.jsx)("p",{ref:r,className:(0,s.cn)("text-sm text-muted-foreground",t),...a})});d.displayName="CardDescription";let u=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,n.jsx)("div",{ref:r,className:(0,s.cn)("p-6 pt-0",t),...a})});u.displayName="CardContent";let c=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,n.jsx)("div",{ref:r,className:(0,s.cn)("flex items-center p-6 pt-0",t),...a})});c.displayName="CardFooter"},1684:(e,r,t)=>{"use strict";t.d(r,{$4:()=>o,Yq:()=>i,cn:()=>s,lk:()=>l});var n=t(3463),a=t(9795);function s(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,a.QP)((0,n.$)(r))}function l(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){let r=16*Math.random()|0;return("x"===e?r:3&r|8).toString(16)})}function i(e){return("string"==typeof e?new Date(e):e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})}function o(e,r){return 0===r?0:Math.round(e/r*100)}},8068:(e,r,t)=>{"use strict";t.d(r,{s:()=>l,t:()=>s});var n=t(2115);function a(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}function s(...e){return r=>{let t=!1,n=e.map(e=>{let n=a(e,r);return t||"function"!=typeof n||(t=!0),n});if(t)return()=>{for(let r=0;r<n.length;r++){let t=n[r];"function"==typeof t?t():a(e[r],null)}}}}function l(...e){return n.useCallback(s(...e),e)}},2317:(e,r,t)=>{"use strict";t.d(r,{DX:()=>i,TL:()=>l});var n=t(2115),a=t(8068),s=t(5155);function l(e){let r=function(e){let r=n.forwardRef((e,r)=>{let{children:t,...s}=e;if(n.isValidElement(t)){let e,l;let i=(e=Object.getOwnPropertyDescriptor(t.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?t.ref:(e=Object.getOwnPropertyDescriptor(t,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?t.props.ref:t.props.ref||t.ref,o=function(e,r){let t={...r};for(let n in r){let a=e[n],s=r[n];/^on[A-Z]/.test(n)?a&&s?t[n]=(...e)=>{let r=s(...e);return a(...e),r}:a&&(t[n]=a):"style"===n?t[n]={...a,...s}:"className"===n&&(t[n]=[a,s].filter(Boolean).join(" "))}return{...e,...t}}(s,t.props);return t.type!==n.Fragment&&(o.ref=r?(0,a.t)(r,i):i),n.cloneElement(t,o)}return n.Children.count(t)>1?n.Children.only(null):null});return r.displayName=`${e}.SlotClone`,r}(e),t=n.forwardRef((e,t)=>{let{children:a,...l}=e,i=n.Children.toArray(a),o=i.find(d);if(o){let e=o.props.children,a=i.map(r=>r!==o?r:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,s.jsx)(r,{...l,ref:t,children:n.isValidElement(e)?n.cloneElement(e,void 0,a):null})}return(0,s.jsx)(r,{...l,ref:t,children:a})});return t.displayName=`${e}.Slot`,t}var i=l("Slot"),o=Symbol("radix.slottable");function d(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===o}},1027:(e,r,t)=>{"use strict";t.d(r,{F:()=>l});var n=t(3463);let a=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,s=n.$,l=(e,r)=>t=>{var n;if((null==r?void 0:r.variants)==null)return s(e,null==t?void 0:t.class,null==t?void 0:t.className);let{variants:l,defaultVariants:i}=r,o=Object.keys(l).map(e=>{let r=null==t?void 0:t[e],n=null==i?void 0:i[e];if(null===r)return null;let s=a(r)||a(n);return l[e][s]}),d=t&&Object.entries(t).reduce((e,r)=>{let[t,n]=r;return void 0===n||(e[t]=n),e},{});return s(e,o,null==r?void 0:null===(n=r.compoundVariants)||void 0===n?void 0:n.reduce((e,r)=>{let{class:t,className:n,...a}=r;return Object.entries(a).every(e=>{let[r,t]=e;return Array.isArray(t)?t.includes({...i,...d}[r]):({...i,...d})[r]===t})?[...e,t,n]:e},[]),null==t?void 0:t.class,null==t?void 0:t.className)}}},e=>{var r=r=>e(e.s=r);e.O(0,[173,181,441,517,358],()=>r(4258)),_N_E=e.O()}]);