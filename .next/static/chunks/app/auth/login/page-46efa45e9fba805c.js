(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[859],{288:(e,r,t)=>{Promise.resolve().then(t.bind(t,6286))},6046:(e,r,t)=>{"use strict";var n=t(6658);t.o(n,"usePathname")&&t.d(r,{usePathname:function(){return n.usePathname}}),t.o(n,"useRouter")&&t.d(r,{useRouter:function(){return n.useRouter}})},6286:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>c});var n=t(5155),s=t(2115),l=t(2615),a=t(6046),i=t(8173),o=t.n(i),d=t(3312),u=t(9749);function c(){let e=(0,a.useRouter)(),[r,t]=(0,s.useState)(""),[i,c]=(0,s.useState)(""),[f,m]=(0,s.useState)(null),[x,p]=(0,s.useState)(!1),h=async t=>{t.preventDefault(),p(!0),m(null);try{let t=await (0,l.signIn)("credentials",{email:r,password:i,redirect:!1});if(null==t?void 0:t.error){m("Invalid email or password"),p(!1);return}e.push("/dashboard"),e.refresh()}catch(e){m("An error occurred. Please try again."),p(!1)}};return(0,n.jsx)("div",{className:"flex min-h-screen items-center justify-center p-4",children:(0,n.jsxs)(u.Zp,{className:"w-full max-w-md",children:[(0,n.jsxs)(u.aR,{className:"space-y-1",children:[(0,n.jsx)(u.ZB,{className:"text-2xl font-bold",children:"Login"}),(0,n.jsx)(u.BT,{children:"Enter your credentials to access your account"})]}),(0,n.jsxs)(u.Wu,{children:[(0,n.jsxs)("form",{onSubmit:h,className:"space-y-4",children:[f&&(0,n.jsx)("div",{className:"p-3 text-sm bg-red-50 text-red-500 rounded-md",children:f}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)("label",{htmlFor:"email",className:"text-sm font-medium leading-none",children:"Email"}),(0,n.jsx)("input",{id:"email",type:"email",value:r,onChange:e=>t(e.target.value),className:"w-full p-2 border rounded-md",required:!0})]}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsx)("label",{htmlFor:"password",className:"text-sm font-medium leading-none",children:"Password"}),(0,n.jsx)(o(),{href:"/auth/forgot-password",className:"text-sm text-primary hover:underline",children:"Forgot password?"})]}),(0,n.jsx)("input",{id:"password",type:"password",value:i,onChange:e=>c(e.target.value),className:"w-full p-2 border rounded-md",required:!0})]}),(0,n.jsx)(d.$,{type:"submit",className:"w-full",disabled:x,children:x?"Logging in...":"Login"})]}),(0,n.jsxs)("div",{className:"relative my-4",children:[(0,n.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,n.jsx)("div",{className:"w-full border-t"})}),(0,n.jsx)("div",{className:"relative flex justify-center text-xs uppercase",children:(0,n.jsx)("span",{className:"bg-background px-2 text-muted-foreground",children:"Or continue with"})})]}),(0,n.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,n.jsx)(d.$,{variant:"outline",onClick:()=>(0,l.signIn)("google",{callbackUrl:"/dashboard"}),disabled:x,children:"Google"}),(0,n.jsx)(d.$,{variant:"outline",onClick:()=>(0,l.signIn)("github",{callbackUrl:"/dashboard"}),disabled:x,children:"GitHub"})]})]}),(0,n.jsx)(u.wL,{className:"flex justify-center",children:(0,n.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Don't have an account?"," ",(0,n.jsx)(o(),{href:"/auth/register",className:"text-primary hover:underline",children:"Sign up"})]})})]})})}},3312:(e,r,t)=>{"use strict";t.d(r,{$:()=>d});var n=t(5155),s=t(2115),l=t(2317),a=t(1027),i=t(1684);let o=(0,a.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=s.forwardRef((e,r)=>{let{className:t,variant:s,size:a,asChild:d=!1,...u}=e,c=d?l.DX:"button";return(0,n.jsx)(c,{className:(0,i.cn)(o({variant:s,size:a,className:t})),ref:r,...u})});d.displayName="Button"},9749:(e,r,t)=>{"use strict";t.d(r,{BT:()=>d,Wu:()=>u,ZB:()=>o,Zp:()=>a,aR:()=>i,wL:()=>c});var n=t(5155),s=t(2115),l=t(1684);let a=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,n.jsx)("div",{ref:r,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...s})});a.displayName="Card";let i=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,n.jsx)("div",{ref:r,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",t),...s})});i.displayName="CardHeader";let o=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,n.jsx)("h3",{ref:r,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",t),...s})});o.displayName="CardTitle";let d=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,n.jsx)("p",{ref:r,className:(0,l.cn)("text-sm text-muted-foreground",t),...s})});d.displayName="CardDescription";let u=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,n.jsx)("div",{ref:r,className:(0,l.cn)("p-6 pt-0",t),...s})});u.displayName="CardContent";let c=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,n.jsx)("div",{ref:r,className:(0,l.cn)("flex items-center p-6 pt-0",t),...s})});c.displayName="CardFooter"},1684:(e,r,t)=>{"use strict";t.d(r,{$4:()=>o,Yq:()=>i,cn:()=>l,lk:()=>a});var n=t(3463),s=t(9795);function l(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,s.QP)((0,n.$)(r))}function a(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){let r=16*Math.random()|0;return("x"===e?r:3&r|8).toString(16)})}function i(e){return("string"==typeof e?new Date(e):e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})}function o(e,r){return 0===r?0:Math.round(e/r*100)}},8068:(e,r,t)=>{"use strict";t.d(r,{s:()=>a,t:()=>l});var n=t(2115);function s(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}function l(...e){return r=>{let t=!1,n=e.map(e=>{let n=s(e,r);return t||"function"!=typeof n||(t=!0),n});if(t)return()=>{for(let r=0;r<n.length;r++){let t=n[r];"function"==typeof t?t():s(e[r],null)}}}}function a(...e){return n.useCallback(l(...e),e)}},2317:(e,r,t)=>{"use strict";t.d(r,{DX:()=>i,TL:()=>a});var n=t(2115),s=t(8068),l=t(5155);function a(e){let r=function(e){let r=n.forwardRef((e,r)=>{let{children:t,...l}=e;if(n.isValidElement(t)){let e,a;let i=(e=Object.getOwnPropertyDescriptor(t.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?t.ref:(e=Object.getOwnPropertyDescriptor(t,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?t.props.ref:t.props.ref||t.ref,o=function(e,r){let t={...r};for(let n in r){let s=e[n],l=r[n];/^on[A-Z]/.test(n)?s&&l?t[n]=(...e)=>{let r=l(...e);return s(...e),r}:s&&(t[n]=s):"style"===n?t[n]={...s,...l}:"className"===n&&(t[n]=[s,l].filter(Boolean).join(" "))}return{...e,...t}}(l,t.props);return t.type!==n.Fragment&&(o.ref=r?(0,s.t)(r,i):i),n.cloneElement(t,o)}return n.Children.count(t)>1?n.Children.only(null):null});return r.displayName=`${e}.SlotClone`,r}(e),t=n.forwardRef((e,t)=>{let{children:s,...a}=e,i=n.Children.toArray(s),o=i.find(d);if(o){let e=o.props.children,s=i.map(r=>r!==o?r:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,l.jsx)(r,{...a,ref:t,children:n.isValidElement(e)?n.cloneElement(e,void 0,s):null})}return(0,l.jsx)(r,{...a,ref:t,children:s})});return t.displayName=`${e}.Slot`,t}var i=a("Slot"),o=Symbol("radix.slottable");function d(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===o}},1027:(e,r,t)=>{"use strict";t.d(r,{F:()=>a});var n=t(3463);let s=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,l=n.$,a=(e,r)=>t=>{var n;if((null==r?void 0:r.variants)==null)return l(e,null==t?void 0:t.class,null==t?void 0:t.className);let{variants:a,defaultVariants:i}=r,o=Object.keys(a).map(e=>{let r=null==t?void 0:t[e],n=null==i?void 0:i[e];if(null===r)return null;let l=s(r)||s(n);return a[e][l]}),d=t&&Object.entries(t).reduce((e,r)=>{let[t,n]=r;return void 0===n||(e[t]=n),e},{});return l(e,o,null==r?void 0:null===(n=r.compoundVariants)||void 0===n?void 0:n.reduce((e,r)=>{let{class:t,className:n,...s}=r;return Object.entries(s).every(e=>{let[r,t]=e;return Array.isArray(t)?t.includes({...i,...d}[r]):({...i,...d})[r]===t})?[...e,t,n]:e},[]),null==t?void 0:t.class,null==t?void 0:t.className)}}},e=>{var r=r=>e(e.s=r);e.O(0,[173,181,615,441,517,358],()=>r(288)),_N_E=e.O()}]);