(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[754],{5685:(e,t,l)=>{Promise.resolve().then(l.bind(l,6281)),Promise.resolve().then(l.bind(l,9469)),Promise.resolve().then(l.bind(l,6406)),Promise.resolve().then(l.bind(l,2892))},6281:(e,t,l)=>{"use strict";l.d(t,{default:()=>r});var n=l(5155),s=l(9749);function r(e){let{totalQuizzes:t,totalQuizzesTaken:l,totalResponsesReceived:r,averageScore:a,completionRate:d}=e;return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(s.Zp,{children:(0,n.jsx)(s.<PERSON>,{className:"p-6",children:(0,n.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,n.jsx)("p",{className:"text-sm text-muted-foreground",children:"Total Quizzes Created"}),(0,n.jsx)("p",{className:"text-3xl font-bold",children:t})]})})}),(0,n.jsx)(s.Zp,{children:(0,n.jsx)(s.Wu,{className:"p-6",children:(0,n.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,n.jsx)("p",{className:"text-sm text-muted-foreground",children:"Quizzes Taken"}),(0,n.jsx)("p",{className:"text-3xl font-bold",children:l})]})})}),(0,n.jsx)(s.Zp,{children:(0,n.jsx)(s.Wu,{className:"p-6",children:(0,n.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,n.jsx)("p",{className:"text-sm text-muted-foreground",children:"Responses Received"}),(0,n.jsx)("p",{className:"text-3xl font-bold",children:r})]})})}),(0,n.jsx)(s.Zp,{children:(0,n.jsx)(s.Wu,{className:"p-6",children:(0,n.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,n.jsx)("p",{className:"text-sm text-muted-foreground",children:"Average Score"}),(0,n.jsxs)("p",{className:"text-3xl font-bold",children:[a.toFixed(1),"%"]})]})})})]})}},9469:(e,t,l)=>{"use strict";l.d(t,{default:()=>r});var n=l(5155),s=l(2115);function r(e){let{data:t}=e,l=(0,s.useRef)(null);return((0,s.useEffect)(()=>{if(!l.current||0===t.length)return;let e=l.current;e.innerHTML="";let n=Math.floor((e.clientWidth-100)/t.length),s=Math.max(...t.map(e=>e.responseCount)),r=document.createElement("div");r.style.position="relative",r.style.height="".concat(300,"px"),r.style.display="flex",r.style.alignItems="flex-end",r.style.justifyContent="space-around",r.style.padding="0 20px";let a=document.createElement("div");a.style.position="absolute",a.style.left="0",a.style.top="0",a.style.height="100%",a.style.display="flex",a.style.flexDirection="column",a.style.justifyContent="space-between",[100,75,50,25,0].forEach(e=>{let t=document.createElement("div");t.style.fontSize="12px",t.style.color="#666",t.textContent="".concat(e,"%"),a.appendChild(t)}),r.appendChild(a),t.forEach((e,t)=>{let l=document.createElement("div");l.style.display="flex",l.style.flexDirection="column",l.style.alignItems="center",l.style.width="".concat(n,"px");let a=document.createElement("div"),d=e.averageScore/100*300;a.style.height="".concat(d,"px"),a.style.width="".concat(.4*n,"px"),a.style.backgroundColor="#3b82f6",a.style.borderRadius="4px 4px 0 0";let i=document.createElement("div"),c=e.responseCount/s*300;i.style.height="".concat(c,"px"),i.style.width="".concat(.4*n,"px"),i.style.backgroundColor="#10b981",i.style.borderRadius="4px 4px 0 0",i.style.marginLeft="4px";let o=document.createElement("div");o.style.display="flex",o.style.alignItems="flex-end",o.style.height="100%",o.appendChild(a),o.appendChild(i),l.appendChild(o);let x=document.createElement("div");x.style.fontSize="12px",x.style.marginTop="8px",x.style.textAlign="center",x.style.whiteSpace="nowrap",x.style.overflow="hidden",x.style.textOverflow="ellipsis",x.style.width="100%",x.textContent=e.quizTitle,l.appendChild(x),r.appendChild(l)});let d=document.createElement("div");d.style.display="flex",d.style.justifyContent="center",d.style.marginTop="20px";let i=document.createElement("div");i.style.display="flex",i.style.alignItems="center",i.style.marginRight="20px";let c=document.createElement("div");c.style.width="12px",c.style.height="12px",c.style.backgroundColor="#3b82f6",c.style.marginRight="4px";let o=document.createElement("span");o.textContent="Average Score",i.appendChild(c),i.appendChild(o);let x=document.createElement("div");x.style.display="flex",x.style.alignItems="center";let p=document.createElement("div");p.style.width="12px",p.style.height="12px",p.style.backgroundColor="#10b981",p.style.marginRight="4px";let m=document.createElement("span");m.textContent="Response Count",x.appendChild(p),x.appendChild(m),d.appendChild(i),d.appendChild(x),e.appendChild(r),e.appendChild(d)},[t]),0===t.length)?(0,n.jsx)("div",{className:"flex items-center justify-center h-[300px] bg-muted/20 rounded-md",children:(0,n.jsx)("p",{className:"text-muted-foreground",children:"No data available"})}):(0,n.jsx)("div",{ref:l,className:"h-[400px]"})}},6406:(e,t,l)=>{"use strict";l.d(t,{default:()=>r});var n=l(5155),s=l(1684);function r(e){let{responses:t}=e;return 0===t.length?(0,n.jsx)("div",{className:"text-center py-8",children:(0,n.jsx)("p",{className:"text-muted-foreground",children:"No recent activity"})}):(0,n.jsx)("div",{className:"overflow-x-auto",children:(0,n.jsxs)("table",{className:"w-full",children:[(0,n.jsx)("thead",{children:(0,n.jsxs)("tr",{className:"border-b",children:[(0,n.jsx)("th",{className:"text-left py-3 px-4 font-medium",children:"Quiz"}),(0,n.jsx)("th",{className:"text-left py-3 px-4 font-medium",children:"User"}),(0,n.jsx)("th",{className:"text-left py-3 px-4 font-medium",children:"Score"}),(0,n.jsx)("th",{className:"text-left py-3 px-4 font-medium",children:"Time Spent"}),(0,n.jsx)("th",{className:"text-left py-3 px-4 font-medium",children:"Date"}),(0,n.jsx)("th",{className:"text-left py-3 px-4 font-medium",children:"Status"})]})}),(0,n.jsx)("tbody",{children:t.map(e=>{var t,l;return(0,n.jsxs)("tr",{className:"border-b hover:bg-muted/50",children:[(0,n.jsx)("td",{className:"py-3 px-4",children:e.quiz.title}),(0,n.jsx)("td",{className:"py-3 px-4",children:(null===(t=e.user)||void 0===t?void 0:t.name)||(null===(l=e.user)||void 0===l?void 0:l.email)||"Anonymous"}),(0,n.jsxs)("td",{className:"py-3 px-4",children:[e.score.toFixed(1),"%"]}),(0,n.jsx)("td",{className:"py-3 px-4",children:e.timeSpent?function(e){if(e<60)return"".concat(e," sec");let t=Math.floor(e/60);if(t<60)return"".concat(t," min ").concat(e%60," sec");let l=Math.floor(t/60);return"".concat(l," hr ").concat(t%60," min")}(e.timeSpent):"N/A"}),(0,n.jsx)("td",{className:"py-3 px-4",children:(0,s.Yq)(e.completedAt||e.startedAt)}),(0,n.jsx)("td",{className:"py-3 px-4",children:(0,n.jsx)("span",{className:"inline-block px-2 py-1 text-xs rounded-full ".concat(e.completedAt?"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100":"bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-100"),children:e.completedAt?"Completed":"In Progress"})})]},e.id)})})]})})}},2892:(e,t,l)=>{"use strict";l.d(t,{default:()=>r});var n=l(5155),s=l(2115);function r(e){let{data:t}=e,l=(0,s.useRef)(null);return((0,s.useEffect)(()=>{if(!l.current||0===t.length)return;let e=l.current;e.innerHTML="";let n=Math.floor(300/(2*t.length)),s=Math.max(...t.map(e=>e.responseCount)),r=document.createElement("div");r.style.position="relative",r.style.height="".concat(300,"px"),r.style.display="flex",r.style.flexDirection="column",r.style.justifyContent="space-around",r.style.padding="10px 0",t.forEach((e,t)=>{let l=document.createElement("div");l.style.display="flex",l.style.alignItems="center",l.style.height="".concat(2*n,"px");let a=document.createElement("div");a.style.width="40%",a.style.fontSize="14px",a.style.whiteSpace="nowrap",a.style.overflow="hidden",a.style.textOverflow="ellipsis",a.style.paddingRight="10px",a.textContent=e.title;let d=document.createElement("div");d.style.width="60%",d.style.height="".concat(n,"px"),d.style.backgroundColor="#e5e7eb",d.style.borderRadius="4px",d.style.overflow="hidden";let i=document.createElement("div"),c=e.responseCount/s*100;i.style.width="".concat(c,"%"),i.style.height="100%",i.style.backgroundColor="#3b82f6";let o=document.createElement("div");o.style.marginLeft="10px",o.style.fontSize="14px",o.style.fontWeight="bold",o.textContent=e.responseCount.toString(),d.appendChild(i),l.appendChild(a),l.appendChild(d),l.appendChild(o),r.appendChild(l)}),e.appendChild(r)},[t]),0===t.length)?(0,n.jsx)("div",{className:"flex items-center justify-center h-[300px] bg-muted/20 rounded-md",children:(0,n.jsx)("p",{className:"text-muted-foreground",children:"No data available"})}):(0,n.jsx)("div",{ref:l,className:"h-[300px]"})}},9749:(e,t,l)=>{"use strict";l.d(t,{BT:()=>c,Wu:()=>o,ZB:()=>i,Zp:()=>a,aR:()=>d,wL:()=>x});var n=l(5155),s=l(2115),r=l(1684);let a=s.forwardRef((e,t)=>{let{className:l,...s}=e;return(0,n.jsx)("div",{ref:t,className:(0,r.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",l),...s})});a.displayName="Card";let d=s.forwardRef((e,t)=>{let{className:l,...s}=e;return(0,n.jsx)("div",{ref:t,className:(0,r.cn)("flex flex-col space-y-1.5 p-6",l),...s})});d.displayName="CardHeader";let i=s.forwardRef((e,t)=>{let{className:l,...s}=e;return(0,n.jsx)("h3",{ref:t,className:(0,r.cn)("text-2xl font-semibold leading-none tracking-tight",l),...s})});i.displayName="CardTitle";let c=s.forwardRef((e,t)=>{let{className:l,...s}=e;return(0,n.jsx)("p",{ref:t,className:(0,r.cn)("text-sm text-muted-foreground",l),...s})});c.displayName="CardDescription";let o=s.forwardRef((e,t)=>{let{className:l,...s}=e;return(0,n.jsx)("div",{ref:t,className:(0,r.cn)("p-6 pt-0",l),...s})});o.displayName="CardContent";let x=s.forwardRef((e,t)=>{let{className:l,...s}=e;return(0,n.jsx)("div",{ref:t,className:(0,r.cn)("flex items-center p-6 pt-0",l),...s})});x.displayName="CardFooter"},1684:(e,t,l)=>{"use strict";l.d(t,{$4:()=>i,Yq:()=>d,cn:()=>r,lk:()=>a});var n=l(3463),s=l(9795);function r(){for(var e=arguments.length,t=Array(e),l=0;l<e;l++)t[l]=arguments[l];return(0,s.QP)((0,n.$)(t))}function a(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){let t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)})}function d(e){return("string"==typeof e?new Date(e):e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})}function i(e,t){return 0===t?0:Math.round(e/t*100)}}},e=>{var t=t=>e(e.s=t);e.O(0,[181,441,517,358],()=>t(5685)),_N_E=e.O()}]);