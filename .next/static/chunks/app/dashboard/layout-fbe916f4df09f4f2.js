(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[954],{9865:(e,t,r)=>{Promise.resolve().then(r.bind(r,1114))},6046:(e,t,r)=>{"use strict";var n=r(6658);r.o(n,"usePathname")&&r.d(t,{usePathname:function(){return n.usePathname}}),r.o(n,"useRouter")&&r.d(t,{useRouter:function(){return n.useRouter}})},1114:(e,t,r)=>{"use strict";r.d(t,{default:()=>u});var n=r(5155),i=r(8173),s=r.n(i),a=r(6046),o=r(2615),l=r(3312);function u(){let e=(0,a.usePathname)(),{data:t}=(0,o.useSession)(),r=t=>e===t||e.startsWith("".concat(t,"/"));return(0,n.jsx)("header",{className:"border-b",children:(0,n.jsxs)("div",{className:"container mx-auto px-4 flex h-16 items-center justify-between",children:[(0,n.jsxs)("div",{className:"flex items-center gap-6",children:[(0,n.jsx)(s(),{href:"/",className:"text-xl font-bold",children:"QuizFlow"}),(0,n.jsxs)("nav",{className:"hidden md:flex gap-6",children:[(0,n.jsx)(s(),{href:"/dashboard",className:"text-sm font-medium transition-colors hover:text-primary ".concat(!r("/dashboard")||r("/dashboard/quizzes")||r("/dashboard/activity")?"text-muted-foreground":"text-primary"),children:"Dashboard"}),(0,n.jsx)(s(),{href:"/dashboard/quizzes",className:"text-sm font-medium transition-colors hover:text-primary ".concat(r("/dashboard/quizzes")?"text-primary":"text-muted-foreground"),children:"My Quizzes"}),(0,n.jsx)(s(),{href:"/dashboard/activity",className:"text-sm font-medium transition-colors hover:text-primary ".concat(r("/dashboard/activity")?"text-primary":"text-muted-foreground"),children:"Activity"}),(0,n.jsx)(s(),{href:"/explore",className:"text-sm font-medium transition-colors hover:text-primary ".concat(r("/explore")?"text-primary":"text-muted-foreground"),children:"Explore"})]})]}),(0,n.jsx)("div",{className:"flex items-center gap-4",children:t?(0,n.jsxs)("div",{className:"flex items-center gap-4",children:[(0,n.jsxs)("div",{className:"hidden md:block",children:[(0,n.jsx)("div",{className:"text-sm font-medium",children:t.user.name}),(0,n.jsx)("div",{className:"text-xs text-muted-foreground",children:t.user.email})]}),(0,n.jsx)(l.$,{variant:"outline",size:"sm",onClick:()=>(0,o.signOut)({callbackUrl:"/"}),children:"Sign Out"})]}):(0,n.jsx)(l.$,{asChild:!0,size:"sm",children:(0,n.jsx)(s(),{href:"/auth/login",children:"Sign In"})})})]})})}},3312:(e,t,r)=>{"use strict";r.d(t,{$:()=>u});var n=r(5155),i=r(2115),s=r(2317),a=r(1027),o=r(1684);let l=(0,a.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),u=i.forwardRef((e,t)=>{let{className:r,variant:i,size:a,asChild:u=!1,...d}=e,c=u?s.DX:"button";return(0,n.jsx)(c,{className:(0,o.cn)(l({variant:i,size:a,className:r})),ref:t,...d})});u.displayName="Button"},1684:(e,t,r)=>{"use strict";r.d(t,{$4:()=>l,Yq:()=>o,cn:()=>s,lk:()=>a});var n=r(3463),i=r(9795);function s(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,i.QP)((0,n.$)(t))}function a(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){let t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)})}function o(e){return("string"==typeof e?new Date(e):e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})}function l(e,t){return 0===t?0:Math.round(e/t*100)}},8068:(e,t,r)=>{"use strict";r.d(t,{s:()=>a,t:()=>s});var n=r(2115);function i(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function s(...e){return t=>{let r=!1,n=e.map(e=>{let n=i(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():i(e[t],null)}}}}function a(...e){return n.useCallback(s(...e),e)}},2317:(e,t,r)=>{"use strict";r.d(t,{DX:()=>o,TL:()=>a});var n=r(2115),i=r(8068),s=r(5155);function a(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...s}=e;if(n.isValidElement(r)){let e,a;let o=(e=Object.getOwnPropertyDescriptor(r.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.ref:(e=Object.getOwnPropertyDescriptor(r,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.props.ref:r.props.ref||r.ref,l=function(e,t){let r={...t};for(let n in t){let i=e[n],s=t[n];/^on[A-Z]/.test(n)?i&&s?r[n]=(...e)=>{let t=s(...e);return i(...e),t}:i&&(r[n]=i):"style"===n?r[n]={...i,...s}:"className"===n&&(r[n]=[i,s].filter(Boolean).join(" "))}return{...e,...r}}(s,r.props);return r.type!==n.Fragment&&(l.ref=t?(0,i.t)(t,o):o),n.cloneElement(r,l)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:i,...a}=e,o=n.Children.toArray(i),l=o.find(u);if(l){let e=l.props.children,i=o.map(t=>t!==l?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,s.jsx)(t,{...a,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,i):null})}return(0,s.jsx)(t,{...a,ref:r,children:i})});return r.displayName=`${e}.Slot`,r}var o=a("Slot"),l=Symbol("radix.slottable");function u(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===l}},1027:(e,t,r)=>{"use strict";r.d(t,{F:()=>a});var n=r(3463);let i=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,s=n.$,a=(e,t)=>r=>{var n;if((null==t?void 0:t.variants)==null)return s(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:a,defaultVariants:o}=t,l=Object.keys(a).map(e=>{let t=null==r?void 0:r[e],n=null==o?void 0:o[e];if(null===t)return null;let s=i(t)||i(n);return a[e][s]}),u=r&&Object.entries(r).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return s(e,l,null==t?void 0:null===(n=t.compoundVariants)||void 0===n?void 0:n.reduce((e,t)=>{let{class:r,className:n,...i}=t;return Object.entries(i).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...o,...u}[t]):({...o,...u})[t]===r})?[...e,r,n]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}}},e=>{var t=t=>e(e.s=t);e.O(0,[173,181,615,441,517,358],()=>t(9865)),_N_E=e.O()}]);