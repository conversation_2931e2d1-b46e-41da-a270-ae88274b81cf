(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[132],{1731:(e,t,r)=>{Promise.resolve().then(r.bind(r,9017))},6046:(e,t,r)=>{"use strict";var n=r(6658);r.o(n,"usePathname")&&r.d(t,{usePathname:function(){return n.usePathname}}),r.o(n,"useRouter")&&r.d(t,{useRouter:function(){return n.useRouter}})},9017:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var n=r(5155),a=r(2115),s=r(6046),i=r(2615),l=r(3312),o=r(9749),d=r(1684);function u(){let e=(0,s.useRouter)(),{data:t}=(0,i.useSession)(),[r,u]=(0,a.useState)(""),[c,m]=(0,a.useState)(""),[f,p]=(0,a.useState)(""),[x,h]=(0,a.useState)(70),[g,v]=(0,a.useState)(15),[y,b]=(0,a.useState)(!1),[j,N]=(0,a.useState)(null),w=async n=>{if(n.preventDefault(),b(!0),N(null),!t){N("You must be logged in to create a quiz"),b(!1);return}try{let t={quizId:(0,d.lk)(),title:r,description:c,tags:f.split(",").map(e=>e.trim()).filter(e=>e),passingScore:parseFloat(x.toString()),timeLimit:parseInt(g.toString())},n=await fetch("/api/quizzes",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)}),a=await n.json();if(!n.ok)throw Error(a.message||"Failed to create quiz");e.push("/dashboard/quizzes/".concat(a.id,"/edit"))}catch(e){e instanceof Error?N(e.message):N("An error occurred while creating the quiz"),b(!1)}};return(0,n.jsx)("div",{className:"container mx-auto py-8 px-4",children:(0,n.jsxs)("div",{className:"max-w-3xl mx-auto",children:[(0,n.jsx)("h1",{className:"text-3xl font-bold mb-8",children:"Create New Quiz"}),(0,n.jsxs)(o.Zp,{children:[(0,n.jsxs)(o.aR,{children:[(0,n.jsx)(o.ZB,{children:"Quiz Details"}),(0,n.jsx)(o.BT,{children:"Enter the basic information for your new quiz"})]}),(0,n.jsx)(o.Wu,{children:(0,n.jsxs)("form",{onSubmit:w,className:"space-y-6",children:[j&&(0,n.jsx)("div",{className:"p-3 text-sm bg-red-50 text-red-500 rounded-md",children:j}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)("label",{htmlFor:"title",className:"text-sm font-medium",children:"Quiz Title"}),(0,n.jsx)("input",{id:"title",type:"text",value:r,onChange:e=>u(e.target.value),className:"w-full p-2 border rounded-md",placeholder:"Enter quiz title",required:!0})]}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)("label",{htmlFor:"description",className:"text-sm font-medium",children:"Description"}),(0,n.jsx)("textarea",{id:"description",value:c,onChange:e=>m(e.target.value),className:"w-full p-2 border rounded-md min-h-[100px]",placeholder:"Enter quiz description"})]}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)("label",{htmlFor:"tags",className:"text-sm font-medium",children:"Tags"}),(0,n.jsx)("input",{id:"tags",type:"text",value:f,onChange:e=>p(e.target.value),className:"w-full p-2 border rounded-md",placeholder:"Enter tags separated by commas (e.g., security, basics, networking)"})]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)("label",{htmlFor:"passingScore",className:"text-sm font-medium",children:"Passing Score (%)"}),(0,n.jsx)("input",{id:"passingScore",type:"number",min:"0",max:"100",value:x,onChange:e=>h(parseInt(e.target.value)),className:"w-full p-2 border rounded-md"})]}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)("label",{htmlFor:"timeLimit",className:"text-sm font-medium",children:"Time Limit (minutes)"}),(0,n.jsx)("input",{id:"timeLimit",type:"number",min:"1",value:g,onChange:e=>v(parseInt(e.target.value)),className:"w-full p-2 border rounded-md"})]})]}),(0,n.jsxs)("div",{className:"flex justify-end space-x-4 pt-4",children:[(0,n.jsx)(l.$,{type:"button",variant:"outline",onClick:()=>e.back(),disabled:y,children:"Cancel"}),(0,n.jsx)(l.$,{type:"submit",disabled:y,children:y?"Creating...":"Create Quiz"})]})]})})]})]})})}},3312:(e,t,r)=>{"use strict";r.d(t,{$:()=>d});var n=r(5155),a=r(2115),s=r(2317),i=r(1027),l=r(1684);let o=(0,i.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef((e,t)=>{let{className:r,variant:a,size:i,asChild:d=!1,...u}=e,c=d?s.DX:"button";return(0,n.jsx)(c,{className:(0,l.cn)(o({variant:a,size:i,className:r})),ref:t,...u})});d.displayName="Button"},9749:(e,t,r)=>{"use strict";r.d(t,{BT:()=>d,Wu:()=>u,ZB:()=>o,Zp:()=>i,aR:()=>l,wL:()=>c});var n=r(5155),a=r(2115),s=r(1684);let i=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("div",{ref:t,className:(0,s.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...a})});i.displayName="Card";let l=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("div",{ref:t,className:(0,s.cn)("flex flex-col space-y-1.5 p-6",r),...a})});l.displayName="CardHeader";let o=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("h3",{ref:t,className:(0,s.cn)("text-2xl font-semibold leading-none tracking-tight",r),...a})});o.displayName="CardTitle";let d=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("p",{ref:t,className:(0,s.cn)("text-sm text-muted-foreground",r),...a})});d.displayName="CardDescription";let u=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("div",{ref:t,className:(0,s.cn)("p-6 pt-0",r),...a})});u.displayName="CardContent";let c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("div",{ref:t,className:(0,s.cn)("flex items-center p-6 pt-0",r),...a})});c.displayName="CardFooter"},1684:(e,t,r)=>{"use strict";r.d(t,{$4:()=>o,Yq:()=>l,cn:()=>s,lk:()=>i});var n=r(3463),a=r(9795);function s(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.QP)((0,n.$)(t))}function i(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){let t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)})}function l(e){return("string"==typeof e?new Date(e):e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})}function o(e,t){return 0===t?0:Math.round(e/t*100)}},8068:(e,t,r)=>{"use strict";r.d(t,{s:()=>i,t:()=>s});var n=r(2115);function a(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function s(...e){return t=>{let r=!1,n=e.map(e=>{let n=a(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():a(e[t],null)}}}}function i(...e){return n.useCallback(s(...e),e)}},2317:(e,t,r)=>{"use strict";r.d(t,{DX:()=>l,TL:()=>i});var n=r(2115),a=r(8068),s=r(5155);function i(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...s}=e;if(n.isValidElement(r)){let e,i;let l=(e=Object.getOwnPropertyDescriptor(r.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.ref:(e=Object.getOwnPropertyDescriptor(r,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.props.ref:r.props.ref||r.ref,o=function(e,t){let r={...t};for(let n in t){let a=e[n],s=t[n];/^on[A-Z]/.test(n)?a&&s?r[n]=(...e)=>{let t=s(...e);return a(...e),t}:a&&(r[n]=a):"style"===n?r[n]={...a,...s}:"className"===n&&(r[n]=[a,s].filter(Boolean).join(" "))}return{...e,...r}}(s,r.props);return r.type!==n.Fragment&&(o.ref=t?(0,a.t)(t,l):l),n.cloneElement(r,o)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:a,...i}=e,l=n.Children.toArray(a),o=l.find(d);if(o){let e=o.props.children,a=l.map(t=>t!==o?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,s.jsx)(t,{...i,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,a):null})}return(0,s.jsx)(t,{...i,ref:r,children:a})});return r.displayName=`${e}.Slot`,r}var l=i("Slot"),o=Symbol("radix.slottable");function d(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===o}},1027:(e,t,r)=>{"use strict";r.d(t,{F:()=>i});var n=r(3463);let a=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,s=n.$,i=(e,t)=>r=>{var n;if((null==t?void 0:t.variants)==null)return s(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:i,defaultVariants:l}=t,o=Object.keys(i).map(e=>{let t=null==r?void 0:r[e],n=null==l?void 0:l[e];if(null===t)return null;let s=a(t)||a(n);return i[e][s]}),d=r&&Object.entries(r).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return s(e,o,null==t?void 0:null===(n=t.compoundVariants)||void 0===n?void 0:n.reduce((e,t)=>{let{class:r,className:n,...a}=t;return Object.entries(a).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...l,...d}[t]):({...l,...d})[t]===r})?[...e,r,n]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}}},e=>{var t=t=>e(e.s=t);e.O(0,[181,615,441,517,358],()=>t(1731)),_N_E=e.O()}]);