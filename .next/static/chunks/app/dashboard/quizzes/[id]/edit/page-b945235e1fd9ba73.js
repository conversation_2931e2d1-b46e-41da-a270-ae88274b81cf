(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[500],{4770:(e,s,t)=>{Promise.resolve().then(t.bind(t,8659))},8659:(e,s,t)=>{"use strict";t.d(s,{default:()=>q});var l=t(5155),i=t(2115),a=t(6046),n=t(4663),r=t(1684);let o=n.bL,d=i.forwardRef((e,s)=>{let{className:t,...i}=e;return(0,l.jsx)(n.B8,{ref:s,className:(0,r.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",t),...i})});d.displayName=n.B8.displayName;let c=i.forwardRef((e,s)=>{let{className:t,...i}=e;return(0,l.jsx)(n.l9,{ref:s,className:(0,r.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",t),...i})});c.displayName=n.l9.displayName;let u=i.forwardRef((e,s)=>{let{className:t,...i}=e;return(0,l.jsx)(n.UC,{ref:s,className:(0,r.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",t),...i})});u.displayName=n.UC.displayName;var m=t(3312),h=t(9749);function x(e){var s,t;let{quiz:a,onSave:n,isSaving:r}=e,[o,d]=(0,i.useState)(a.title),[c,u]=(0,i.useState)(a.description||""),[h,x]=(0,i.useState)(a.tags.join(", ")),[p,f]=(0,i.useState)((null===(s=a.passingScore)||void 0===s?void 0:s.toString())||"70"),[j,v]=(0,i.useState)((null===(t=a.timeLimit)||void 0===t?void 0:t.toString())||"15"),[g,N]=(0,i.useState)(a.locale),[b,y]=(0,i.useState)(a.markupFormat);return(0,l.jsxs)("form",{onSubmit:e=>{e.preventDefault(),n({title:o,description:c||null,tags:h.split(",").map(e=>e.trim()).filter(e=>e),passingScore:p?parseFloat(p):null,timeLimit:j?parseInt(j):null,locale:g,markupFormat:b})},className:"space-y-6",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("label",{htmlFor:"title",className:"text-sm font-medium",children:"Quiz Title"}),(0,l.jsx)("input",{id:"title",type:"text",value:o,onChange:e=>d(e.target.value),className:"w-full p-2 border rounded-md",required:!0})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("label",{htmlFor:"description",className:"text-sm font-medium",children:"Description"}),(0,l.jsx)("textarea",{id:"description",value:c,onChange:e=>u(e.target.value),className:"w-full p-2 border rounded-md min-h-[100px]",placeholder:"Enter quiz description"})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("label",{htmlFor:"tags",className:"text-sm font-medium",children:"Tags"}),(0,l.jsx)("input",{id:"tags",type:"text",value:h,onChange:e=>x(e.target.value),className:"w-full p-2 border rounded-md",placeholder:"Enter tags separated by commas (e.g., security, basics, networking)"}),(0,l.jsx)("p",{className:"text-xs text-muted-foreground",children:"Separate tags with commas (e.g., security, basics, networking)"})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("label",{htmlFor:"passingScore",className:"text-sm font-medium",children:"Passing Score (%)"}),(0,l.jsx)("input",{id:"passingScore",type:"number",min:"0",max:"100",value:p,onChange:e=>f(e.target.value),className:"w-full p-2 border rounded-md"})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("label",{htmlFor:"timeLimit",className:"text-sm font-medium",children:"Time Limit (minutes)"}),(0,l.jsx)("input",{id:"timeLimit",type:"number",min:"1",value:j,onChange:e=>v(e.target.value),className:"w-full p-2 border rounded-md"})]})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("label",{htmlFor:"locale",className:"text-sm font-medium",children:"Locale"}),(0,l.jsxs)("select",{id:"locale",value:g,onChange:e=>N(e.target.value),className:"w-full p-2 border rounded-md",children:[(0,l.jsx)("option",{value:"en-US",children:"English (US)"}),(0,l.jsx)("option",{value:"en-GB",children:"English (UK)"}),(0,l.jsx)("option",{value:"es-ES",children:"Spanish"}),(0,l.jsx)("option",{value:"fr-FR",children:"French"}),(0,l.jsx)("option",{value:"de-DE",children:"German"}),(0,l.jsx)("option",{value:"ja-JP",children:"Japanese"}),(0,l.jsx)("option",{value:"zh-CN",children:"Chinese (Simplified)"})]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("label",{htmlFor:"markupFormat",className:"text-sm font-medium",children:"Markup Format"}),(0,l.jsxs)("select",{id:"markupFormat",value:b,onChange:e=>y(e.target.value),className:"w-full p-2 border rounded-md",children:[(0,l.jsx)("option",{value:"markdown",children:"Markdown"}),(0,l.jsx)("option",{value:"html",children:"HTML"}),(0,l.jsx)("option",{value:"plain_text",children:"Plain Text"})]})]})]}),(0,l.jsx)("div",{className:"flex justify-end pt-4",children:(0,l.jsx)(m.$,{type:"submit",disabled:r,children:r?"Saving...":"Save Details"})})]})}function p(e){let{data:s,onChange:t}=e,[a,n]=(0,i.useState)((null==s?void 0:s.single_correct_answer)===void 0||s.single_correct_answer),[o,d]=(0,i.useState)((null==s?void 0:s.options)||[{id:(0,r.lk)(),text:"",is_correct:!1},{id:(0,r.lk)(),text:"",is_correct:!1}]),[c,u]=(0,i.useState)((null==s?void 0:s.scoring_method)||"all_or_nothing");(0,i.useEffect)(()=>{t({single_correct_answer:a,options:o,scoring_method:c})},[a,o,c,t]);let h=(e,s)=>{d(o.map(t=>t.id===e?{...t,text:s}:t))},x=(e,s)=>{a&&s?d(o.map(s=>({...s,is_correct:s.id===e}))):d(o.map(t=>t.id===e?{...t,is_correct:s}:t))},p=(e,s)=>{d(o.map(t=>t.id===e?{...t,feedback:s}:t))},f=e=>{if(o.length<=2){alert("A multiple choice question must have at least 2 options.");return}d(o.filter(s=>s.id!==e))};return(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("label",{className:"text-sm font-medium",children:"Question Type"}),(0,l.jsxs)("div",{className:"flex space-x-4",children:[(0,l.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,l.jsx)("input",{type:"radio",checked:a,onChange:()=>n(!0)}),(0,l.jsx)("span",{children:"Single Choice (Radio Buttons)"})]}),(0,l.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,l.jsx)("input",{type:"radio",checked:!a,onChange:()=>n(!1)}),(0,l.jsx)("span",{children:"Multiple Choice (Checkboxes)"})]})]})]}),!a&&(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("label",{className:"text-sm font-medium",children:"Scoring Method"}),(0,l.jsxs)("div",{className:"flex space-x-4",children:[(0,l.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,l.jsx)("input",{type:"radio",checked:"all_or_nothing"===c,onChange:()=>u("all_or_nothing")}),(0,l.jsx)("span",{children:"All or Nothing"})]}),(0,l.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,l.jsx)("input",{type:"radio",checked:"partial_credit"===c,onChange:()=>u("partial_credit")}),(0,l.jsx)("span",{children:"Partial Credit"})]})]}),(0,l.jsxs)("p",{className:"text-xs text-muted-foreground",children:["All or Nothing: Full points only if all correct options are selected and no incorrect options.",(0,l.jsx)("br",{}),"Partial Credit: Points awarded based on correct selections."]})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("label",{className:"text-sm font-medium",children:"Options"}),o.map((e,s)=>(0,l.jsxs)("div",{className:"border rounded-md p-4 space-y-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("h4",{className:"font-medium",children:["Option ",s+1]}),(0,l.jsxs)(m.$,{type:"button",variant:"ghost",size:"sm",onClick:()=>f(e.id),className:"h-8 w-8 p-0",children:[(0,l.jsx)("span",{className:"sr-only",children:"Remove"}),(0,l.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,l.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("label",{className:"text-sm font-medium",children:"Option Text"}),(0,l.jsx)("input",{type:"text",value:e.text,onChange:s=>h(e.id,s.target.value),className:"w-full p-2 border rounded-md",placeholder:"Enter option text",required:!0})]}),(0,l.jsx)("div",{className:"space-y-2",children:(0,l.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,l.jsx)("input",{type:a?"radio":"checkbox",checked:e.is_correct,onChange:s=>x(e.id,s.target.checked),name:"correctOption"}),(0,l.jsx)("span",{children:"Correct Answer"})]})}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("label",{className:"text-sm font-medium",children:"Feedback for this option"}),(0,l.jsx)("input",{type:"text",value:e.feedback||"",onChange:s=>p(e.id,s.target.value),className:"w-full p-2 border rounded-md",placeholder:"Optional feedback when this option is selected"})]})]},e.id)),(0,l.jsx)(m.$,{type:"button",variant:"outline",onClick:()=>{d([...o,{id:(0,r.lk)(),text:"",is_correct:!1}])},className:"w-full",children:"Add Option"})]})]})}function f(e){let{data:s,onChange:t}=e,[a,n]=(0,i.useState)((null==s?void 0:s.correct_answer)===void 0||s.correct_answer);return(0,i.useEffect)(()=>{t({correct_answer:a})},[a,t]),(0,l.jsx)("div",{className:"space-y-4",children:(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("label",{className:"text-sm font-medium",children:"Correct Answer"}),(0,l.jsxs)("div",{className:"flex space-x-4",children:[(0,l.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,l.jsx)("input",{type:"radio",checked:!0===a,onChange:()=>n(!0),name:"correctAnswer"}),(0,l.jsx)("span",{children:"True"})]}),(0,l.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,l.jsx)("input",{type:"radio",checked:!1===a,onChange:()=>n(!1),name:"correctAnswer"}),(0,l.jsx)("span",{children:"False"})]})]})]})})}function j(e){let{data:s,onChange:t}=e,[a,n]=(0,i.useState)((null==s?void 0:s.correct_answers)||[""]),[r,o]=(0,i.useState)((null==s?void 0:s.case_sensitive)!==void 0&&s.case_sensitive),[d,c]=(0,i.useState)((null==s?void 0:s.trim_whitespace)===void 0||s.trim_whitespace),[u,h]=(0,i.useState)((null==s?void 0:s.exact_match)===void 0||s.exact_match);(0,i.useEffect)(()=>{t({correct_answers:a.filter(e=>""!==e.trim()),case_sensitive:r,trim_whitespace:d,exact_match:u})},[a,r,d,u,t]);let x=(e,s)=>{let t=[...a];t[e]=s,n(t)},p=e=>{if(a.length<=1){alert("You must have at least one correct answer.");return}let s=[...a];s.splice(e,1),n(s)};return(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("label",{className:"text-sm font-medium",children:"Correct Answers"}),(0,l.jsx)("p",{className:"text-xs text-muted-foreground",children:"Add multiple acceptable answers. The question will be marked correct if the student's answer matches any of these."}),a.map((e,s)=>(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)("input",{type:"text",value:e,onChange:e=>x(s,e.target.value),className:"flex-1 p-2 border rounded-md",placeholder:"Enter a correct answer",required:!0}),(0,l.jsxs)(m.$,{type:"button",variant:"ghost",size:"sm",onClick:()=>p(s),className:"h-8 w-8 p-0",disabled:a.length<=1,children:[(0,l.jsx)("span",{className:"sr-only",children:"Remove"}),(0,l.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,l.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})]})]},s)),(0,l.jsx)(m.$,{type:"button",variant:"outline",onClick:()=>{n([...a,""])},className:"w-full",children:"Add Another Correct Answer"})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("label",{className:"text-sm font-medium",children:"Answer Options"}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,l.jsx)("input",{type:"checkbox",checked:r,onChange:e=>o(e.target.checked)}),(0,l.jsx)("span",{children:"Case Sensitive"})]}),(0,l.jsx)("p",{className:"text-xs text-muted-foreground ml-6",children:'If checked, "Answer" and "answer" will be treated as different answers.'})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,l.jsx)("input",{type:"checkbox",checked:d,onChange:e=>c(e.target.checked)}),(0,l.jsx)("span",{children:"Trim Whitespace"})]}),(0,l.jsx)("p",{className:"text-xs text-muted-foreground ml-6",children:"If checked, leading and trailing spaces will be ignored."})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,l.jsx)("input",{type:"checkbox",checked:u,onChange:e=>h(e.target.checked)}),(0,l.jsx)("span",{children:"Exact Match Required"})]}),(0,l.jsx)("p",{className:"text-xs text-muted-foreground ml-6",children:"If unchecked, partial matches or keyword detection may be used (implementation dependent)."})]})]})]})}function v(e){let{data:s,onChange:t}=e,[a,n]=(0,i.useState)((null==s?void 0:s.stems)||[{id:(0,r.lk)(),text:""},{id:(0,r.lk)(),text:""}]),[o,d]=(0,i.useState)((null==s?void 0:s.options)||[{id:(0,r.lk)(),text:""},{id:(0,r.lk)(),text:""}]),[c,u]=(0,i.useState)((null==s?void 0:s.correct_pairs)||[]);(0,i.useEffect)(()=>{0===c.length&&a.length>0&&o.length>0&&u(a.map(e=>({stem_id:e.id,option_id:""})))},[a,o,c.length]),(0,i.useEffect)(()=>{t({stems:a,options:o,correct_pairs:c.filter(e=>e.stem_id&&e.option_id)})},[a,o,c,t]);let h=(e,s)=>{n(a.map(t=>t.id===e?{...t,text:s}:t))},x=(e,s)=>{d(o.map(t=>t.id===e?{...t,text:s}:t))},p=(e,s)=>{u(c.map(t=>t.stem_id===e?{...t,option_id:s}:t))},f=e=>{if(a.length<=2){alert("A matching question must have at least 2 stems.");return}n(a.filter(s=>s.id!==e)),u(c.filter(s=>s.stem_id!==e))},j=e=>{if(o.length<=2){alert("A matching question must have at least 2 options.");return}d(o.filter(s=>s.id!==e)),u(c.map(s=>s.option_id===e?{...s,option_id:""}:s))};return(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("label",{className:"text-sm font-medium",children:"Stems (Left Side)"}),a.map((e,s)=>(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)("input",{type:"text",value:e.text,onChange:s=>h(e.id,s.target.value),className:"flex-1 p-2 border rounded-md",placeholder:"Stem ".concat(s+1),required:!0}),(0,l.jsxs)(m.$,{type:"button",variant:"ghost",size:"sm",onClick:()=>f(e.id),className:"h-8 w-8 p-0",disabled:a.length<=2,children:[(0,l.jsx)("span",{className:"sr-only",children:"Remove"}),(0,l.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,l.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})]})]},e.id)),(0,l.jsx)(m.$,{type:"button",variant:"outline",onClick:()=>{let e=(0,r.lk)();n([...a,{id:e,text:""}]),u([...c,{stem_id:e,option_id:""}])},className:"w-full",children:"Add Stem"})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("label",{className:"text-sm font-medium",children:"Options (Right Side)"}),o.map((e,s)=>(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)("input",{type:"text",value:e.text,onChange:s=>x(e.id,s.target.value),className:"flex-1 p-2 border rounded-md",placeholder:"Option ".concat(s+1),required:!0}),(0,l.jsxs)(m.$,{type:"button",variant:"ghost",size:"sm",onClick:()=>j(e.id),className:"h-8 w-8 p-0",disabled:o.length<=2,children:[(0,l.jsx)("span",{className:"sr-only",children:"Remove"}),(0,l.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,l.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})]})]},e.id)),(0,l.jsx)(m.$,{type:"button",variant:"outline",onClick:()=>{d([...o,{id:(0,r.lk)(),text:""}])},className:"w-full",children:"Add Option"})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("label",{className:"text-sm font-medium",children:"Correct Matches"}),(0,l.jsx)("p",{className:"text-xs text-muted-foreground",children:"For each stem, select the matching option."}),a.map((e,s)=>{let t=c.find(s=>s.stem_id===e.id);return(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)("div",{className:"flex-1 p-2 border rounded-md bg-muted",children:e.text||"Stem ".concat(s+1)}),(0,l.jsx)("div",{className:"text-center px-2",children:"matches"}),(0,l.jsxs)("select",{value:(null==t?void 0:t.option_id)||"",onChange:s=>p(e.id,s.target.value),className:"flex-1 p-2 border rounded-md",required:!0,children:[(0,l.jsx)("option",{value:"",children:"-- Select matching option --"}),o.map(e=>(0,l.jsx)("option",{value:e.id,children:e.text||"Option ".concat(o.findIndex(s=>s.id===e.id)+1)},e.id))]})]},e.id)})]})]})}function g(e){let{data:s,onChange:t}=e,[a,n]=(0,i.useState)((null==s?void 0:s.text_template)||"This is a [BLANK] question with [BLANK] to fill in."),[o,d]=(0,i.useState)((null==s?void 0:s.blanks)||[]);(0,i.useEffect)(()=>{let e=(a.match(/\[BLANK\]/g)||[]).length;o.length!==e&&d(Array(e).fill(null).map((e,s)=>s<o.length?o[s]:{id:(0,r.lk)(),correct_answers:[""],case_sensitive:!1,trim_whitespace:!0,hint:""}))},[a,o.length]),(0,i.useEffect)(()=>{t({text_template:a,blanks:o})},[a,o,t]);let c=(e,s,t)=>{let l=[...o];if(l[e]){let i=[...l[e].correct_answers];i[s]=t,l[e]={...l[e],correct_answers:i},d(l)}},u=(e,s,t)=>{let l=[...o];l[e]&&(l[e]={...l[e],[s]:t},d(l))},h=e=>{let s=[...o];s[e]&&(s[e]={...s[e],correct_answers:[...s[e].correct_answers,""]},d(s))},x=(e,s)=>{let t=[...o];if(t[e]&&t[e].correct_answers.length>1){let l=[...t[e].correct_answers];l.splice(s,1),t[e]={...t[e],correct_answers:l},d(t)}};return(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("label",{htmlFor:"textTemplate",className:"text-sm font-medium",children:"Question Text with Blanks"}),(0,l.jsx)("p",{className:"text-xs text-muted-foreground",children:"Use [BLANK] to indicate where students should fill in answers."}),(0,l.jsx)("textarea",{id:"textTemplate",value:a,onChange:e=>n(e.target.value),className:"w-full p-2 border rounded-md min-h-[100px]",placeholder:"Enter text with [BLANK] placeholders",required:!0})]}),o.length>0?(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsx)("h3",{className:"text-sm font-medium",children:"Define Blanks"}),o.map((e,s)=>(0,l.jsxs)("div",{className:"border rounded-md p-4 space-y-4",children:[(0,l.jsxs)("h4",{className:"font-medium",children:["Blank ",s+1]}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("label",{className:"text-sm font-medium",children:"Correct Answers"}),e.correct_answers.map((t,i)=>(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)("input",{type:"text",value:t,onChange:e=>c(s,i,e.target.value),className:"flex-1 p-2 border rounded-md",placeholder:"Enter a correct answer",required:!0}),(0,l.jsxs)(m.$,{type:"button",variant:"ghost",size:"sm",onClick:()=>x(s,i),className:"h-8 w-8 p-0",disabled:e.correct_answers.length<=1,children:[(0,l.jsx)("span",{className:"sr-only",children:"Remove"}),(0,l.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,l.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})]})]},i)),(0,l.jsx)(m.$,{type:"button",variant:"outline",onClick:()=>h(s),className:"w-full",children:"Add Another Correct Answer"})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("label",{className:"text-sm font-medium",children:"Answer Options"}),(0,l.jsx)("div",{className:"space-y-2",children:(0,l.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,l.jsx)("input",{type:"checkbox",checked:e.case_sensitive,onChange:e=>u(s,"case_sensitive",e.target.checked)}),(0,l.jsx)("span",{children:"Case Sensitive"})]})}),(0,l.jsx)("div",{className:"space-y-2",children:(0,l.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,l.jsx)("input",{type:"checkbox",checked:e.trim_whitespace,onChange:e=>u(s,"trim_whitespace",e.target.checked)}),(0,l.jsx)("span",{children:"Trim Whitespace"})]})})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("label",{htmlFor:"hint-".concat(s),className:"text-sm font-medium",children:"Hint (Optional)"}),(0,l.jsx)("input",{id:"hint-".concat(s),type:"text",value:e.hint||"",onChange:e=>u(s,"hint",e.target.value),className:"w-full p-2 border rounded-md",placeholder:"Enter a hint for this blank"})]})]},e.id))]}):(0,l.jsx)("div",{className:"p-4 bg-muted rounded-md",children:(0,l.jsx)("p",{className:"text-center",children:"Add [BLANK] placeholders to your text to create blanks for students to fill in."})})]})}function N(e){var s,t;let{data:a,onChange:n}=e,[r,o]=(0,i.useState)((null==a?void 0:null===(s=a.min_word_count)||void 0===s?void 0:s.toString())||""),[d,c]=(0,i.useState)((null==a?void 0:null===(t=a.max_word_count)||void 0===t?void 0:t.toString())||""),[u,m]=(0,i.useState)((null==a?void 0:a.guidelines)||"");return(0,i.useEffect)(()=>{n({min_word_count:r?parseInt(r):void 0,max_word_count:d?parseInt(d):void 0,guidelines:u||void 0})},[r,d,u,n]),(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("label",{htmlFor:"minWordCount",className:"text-sm font-medium",children:"Minimum Word Count"}),(0,l.jsx)("input",{id:"minWordCount",type:"number",min:"0",value:r,onChange:e=>o(e.target.value),className:"w-full p-2 border rounded-md",placeholder:"Optional"})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("label",{htmlFor:"maxWordCount",className:"text-sm font-medium",children:"Maximum Word Count"}),(0,l.jsx)("input",{id:"maxWordCount",type:"number",min:"0",value:d,onChange:e=>c(e.target.value),className:"w-full p-2 border rounded-md",placeholder:"Optional"})]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("label",{htmlFor:"guidelines",className:"text-sm font-medium",children:"Guidelines for Students"}),(0,l.jsx)("textarea",{id:"guidelines",value:u,onChange:e=>m(e.target.value),className:"w-full p-2 border rounded-md min-h-[100px]",placeholder:"Enter guidelines or prompts for the essay (optional)"})]}),(0,l.jsx)("div",{className:"p-4 bg-muted rounded-md",children:(0,l.jsxs)("p",{className:"text-sm",children:[(0,l.jsx)("strong",{children:"Note:"})," Essay questions typically require manual grading. Students will see the guidelines and word count limits when answering the question."]})})]})}function b(e){var s;let{questionType:t,initialData:a,onSubmit:n,isSaving:r,submitLabel:o}=e,[d,c]=(0,i.useState)((null==a?void 0:a.text)?"string"==typeof a.text?a.text:JSON.parse(a.text).default||"":""),[u,h]=(0,i.useState)((null==a?void 0:null===(s=a.points)||void 0===s?void 0:s.toString())||"1"),[x,b]=(0,i.useState)((null==a?void 0:a.feedbackCorrect)||""),[y,w]=(0,i.useState)((null==a?void 0:a.feedbackIncorrect)||""),[C,k]=(0,i.useState)(a?{...a}:{});return(0,i.useEffect)(()=>{a||(c(""),h("1"),b(""),w(""),k({}))},[t,a]),(0,l.jsxs)("form",{onSubmit:e=>{e.preventDefault(),n({text:d,points:parseFloat(u),feedbackCorrect:x||null,feedbackIncorrect:y||null,...C})},className:"space-y-6",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("label",{htmlFor:"questionText",className:"text-sm font-medium",children:"Question Text"}),(0,l.jsx)("textarea",{id:"questionText",value:d,onChange:e=>c(e.target.value),className:"w-full p-2 border rounded-md min-h-[100px]",placeholder:"Enter your question here...",required:!0})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("label",{htmlFor:"points",className:"text-sm font-medium",children:"Points"}),(0,l.jsx)("input",{id:"points",type:"number",min:"0.5",step:"0.5",value:u,onChange:e=>h(e.target.value),className:"w-full p-2 border rounded-md",required:!0})]}),(()=>{switch(t){case"multiple_choice":return(0,l.jsx)(p,{data:C,onChange:k});case"true_false":return(0,l.jsx)(f,{data:C,onChange:k});case"short_answer":return(0,l.jsx)(j,{data:C,onChange:k});case"matching":return(0,l.jsx)(v,{data:C,onChange:k});case"fill_in_the_blank":return(0,l.jsx)(g,{data:C,onChange:k});case"essay":return(0,l.jsx)(N,{data:C,onChange:k});default:return(0,l.jsxs)("p",{children:["Unsupported question type: ",t]})}})(),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("label",{htmlFor:"feedbackCorrect",className:"text-sm font-medium",children:"Feedback for Correct Answer"}),(0,l.jsx)("textarea",{id:"feedbackCorrect",value:x,onChange:e=>b(e.target.value),className:"w-full p-2 border rounded-md",placeholder:"Feedback to show when the answer is correct"})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("label",{htmlFor:"feedbackIncorrect",className:"text-sm font-medium",children:"Feedback for Incorrect Answer"}),(0,l.jsx)("textarea",{id:"feedbackIncorrect",value:y,onChange:e=>w(e.target.value),className:"w-full p-2 border rounded-md",placeholder:"Feedback to show when the answer is incorrect"})]}),(0,l.jsx)("div",{className:"flex justify-end pt-4",children:(0,l.jsx)(m.$,{type:"submit",disabled:r,children:r?"Saving...":o})})]})}function y(e){let{questions:s,onAddQuestion:t,onUpdateQuestion:a,onDeleteQuestion:n,isSaving:h}=e,[x,p]=(0,i.useState)("existing"),[f,j]=(0,i.useState)(null),[v,g]=(0,i.useState)("multiple_choice"),N=e=>{confirm("Are you sure you want to delete this question?")&&(n(e),f===e&&j(null))},y=f?s.find(e=>e.id===f):null;return(0,l.jsx)("div",{className:"space-y-6",children:(0,l.jsxs)(o,{value:x,onValueChange:p,children:[(0,l.jsxs)(d,{className:"grid grid-cols-2 w-full max-w-md",children:[(0,l.jsx)(c,{value:"existing",children:"Existing Questions"}),(0,l.jsx)(c,{value:"add",children:"Add Question"})]}),(0,l.jsx)(u,{value:"existing",children:(0,l.jsx)("div",{className:"space-y-6",children:0===s.length?(0,l.jsxs)("div",{className:"text-center py-8",children:[(0,l.jsx)("p",{className:"text-muted-foreground mb-4",children:"No questions yet. Add your first question to get started."}),(0,l.jsx)(m.$,{onClick:()=>p("add"),children:"Add First Question"})]}):(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"border rounded-md p-4 h-[500px] overflow-y-auto",children:[(0,l.jsxs)("h3",{className:"font-medium mb-4",children:["Questions (",s.length,")"]}),(0,l.jsx)("ul",{className:"space-y-2",children:s.map(e=>(0,l.jsx)("li",{className:"p-3 border rounded-md cursor-pointer transition-colors ".concat(f===e.id?"border-primary bg-primary/5":"hover:border-primary/50"),onClick:()=>j(e.id),children:(0,l.jsxs)("div",{className:"flex justify-between items-start",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"font-medium line-clamp-1",children:"string"==typeof e.text?e.text:JSON.parse(e.text).default||"Question"}),(0,l.jsxs)("p",{className:"text-xs text-muted-foreground",children:[e.type," • ",e.points," points"]})]}),(0,l.jsxs)(m.$,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0",onClick:s=>{s.stopPropagation(),N(e.id)},children:[(0,l.jsx)("span",{className:"sr-only",children:"Delete"}),(0,l.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,l.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})]})]})},e.id))})]}),(0,l.jsx)("div",{className:"border rounded-md p-4 h-[500px] overflow-y-auto",children:y?(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"font-medium mb-4",children:"Edit Question"}),(0,l.jsx)(b,{questionType:y.type,initialData:y,onSubmit:e=>{f&&(a(f,e),j(null))},isSaving:h,submitLabel:"Update Question"})]}):(0,l.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,l.jsx)("p",{className:"text-muted-foreground",children:"Select a question to edit"})})})]})})}),(0,l.jsx)(u,{value:"add",children:(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("label",{htmlFor:"questionType",className:"text-sm font-medium",children:"Question Type"}),(0,l.jsxs)("select",{id:"questionType",value:v,onChange:e=>g(e.target.value),className:"w-full p-2 border rounded-md",children:[(0,l.jsx)("option",{value:"multiple_choice",children:"Multiple Choice"}),(0,l.jsx)("option",{value:"true_false",children:"True/False"}),(0,l.jsx)("option",{value:"short_answer",children:"Short Answer"}),(0,l.jsx)("option",{value:"matching",children:"Matching"}),(0,l.jsx)("option",{value:"fill_in_the_blank",children:"Fill in the Blank"}),(0,l.jsx)("option",{value:"essay",children:"Essay"})]})]}),(0,l.jsxs)("div",{className:"border rounded-md p-4",children:[(0,l.jsx)("h3",{className:"font-medium mb-4",children:"Add New Question"}),(0,l.jsx)(b,{questionType:v,onSubmit:e=>{t({questionId:(0,r.lk)(),type:v,...e}),p("existing")},isSaving:h,submitLabel:"Add Question"})]})]})})]})})}function w(e){let{quiz:s,setQuiz:t,isSaving:a}=e,[n,h]=(0,i.useState)("pools"),[x,p]=(0,i.useState)(null),[f,j]=(0,i.useState)(""),[v,g]=(0,i.useState)(""),[N,b]=(0,i.useState)(null),[y,w]=(0,i.useState)(!1),[C,k]=(0,i.useState)(null),[S,q]=(0,i.useState)(""),[_,z]=(0,i.useState)("1"),[P,E]=(0,i.useState)(!0),[T,A]=(0,i.useState)(!1),[F,L]=(0,i.useState)(null),[R,Q]=(0,i.useState)(!1),[O,B]=(0,i.useState)([]),[I,$]=(0,i.useState)([]),M=async e=>{p(e),k(null);let t=s.questionPools.find(s=>s.id===e);if(t){b({id:t.id,title:t.title||"",description:t.description||""}),$(t.questions);let e=s.questionPools.flatMap(e=>e.questions.map(e=>e.id));B(s.questions.filter(s=>!e.includes(s.id)))}},D=e=>{k(e),p(null);let t=s.selectionRules.find(s=>s.id===e);t&&L({id:t.id,poolId:t.poolId,selectCount:t.selectCount,randomize:t.randomize,shuffleOrder:t.shuffleOrder})},W=async()=>{w(!0);try{let e=await fetch("/api/quizzes/".concat(s.id,"/pools"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({poolId:(0,r.lk)(),title:f,description:v})});if(!e.ok)throw Error("Failed to create pool");let l=await e.json();t(e=>({...e,questionPools:[...e.questionPools,{...l,questions:[]}]})),j(""),g(""),h("pools")}catch(e){console.error("Error creating pool:",e)}finally{w(!1)}},H=async()=>{if(N){w(!0);try{let e=await fetch("/api/quizzes/".concat(s.id,"/pools/").concat(N.id),{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({title:N.title,description:N.description})});if(!e.ok)throw Error("Failed to update pool");let l=await e.json();t(e=>({...e,questionPools:e.questionPools.map(e=>e.id===N.id?{...e,...l}:e)}))}catch(e){console.error("Error updating pool:",e)}finally{w(!1)}}},V=async e=>{if(confirm("Are you sure you want to delete this pool? This will also delete any selection rules that use this pool."))try{if(!(await fetch("/api/quizzes/".concat(s.id,"/pools/").concat(e),{method:"DELETE"})).ok)throw Error("Failed to delete pool");t(s=>({...s,questionPools:s.questionPools.filter(s=>s.id!==e),selectionRules:s.selectionRules.filter(s=>s.poolId!==e)})),x===e&&(p(null),b(null))}catch(e){console.error("Error deleting pool:",e)}},J=async()=>{Q(!0);try{let e=await fetch("/api/quizzes/".concat(s.id,"/rules"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({poolId:S,selectCount:parseInt(_),randomize:P,shuffleOrder:T})});if(!e.ok)throw Error("Failed to create rule");let l=await e.json();t(e=>({...e,selectionRules:[...e.selectionRules,l]})),q(""),z("1"),E(!0),A(!1),h("rules")}catch(e){console.error("Error creating rule:",e)}finally{Q(!1)}},U=async()=>{if(F){Q(!0);try{let e=await fetch("/api/quizzes/".concat(s.id,"/rules/").concat(F.id),{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({poolId:F.poolId,selectCount:F.selectCount,randomize:F.randomize,shuffleOrder:F.shuffleOrder})});if(!e.ok)throw Error("Failed to update rule");let l=await e.json();t(e=>({...e,selectionRules:e.selectionRules.map(e=>e.id===F.id?{...e,...l}:e)}))}catch(e){console.error("Error updating rule:",e)}finally{Q(!1)}}},Z=async e=>{if(confirm("Are you sure you want to delete this selection rule?"))try{if(!(await fetch("/api/quizzes/".concat(s.id,"/rules/").concat(e),{method:"DELETE"})).ok)throw Error("Failed to delete rule");t(s=>({...s,selectionRules:s.selectionRules.filter(s=>s.id!==e)})),C===e&&(k(null),L(null))}catch(e){console.error("Error deleting rule:",e)}};return(0,l.jsx)("div",{className:"space-y-6",children:(0,l.jsxs)(o,{value:n,onValueChange:h,children:[(0,l.jsxs)(d,{className:"grid grid-cols-3 w-full max-w-md",children:[(0,l.jsx)(c,{value:"pools",children:"Question Pools"}),(0,l.jsx)(c,{value:"rules",children:"Selection Rules"}),(0,l.jsx)(c,{value:"create",children:"Create New"})]}),(0,l.jsx)(u,{value:"pools",children:(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"border rounded-md p-4 h-[500px] overflow-y-auto",children:[(0,l.jsxs)("h3",{className:"font-medium mb-4",children:["Pools (",s.questionPools.length,")"]}),0===s.questionPools.length?(0,l.jsxs)("div",{className:"text-center py-8",children:[(0,l.jsx)("p",{className:"text-muted-foreground mb-4",children:"No question pools yet. Create your first pool to get started."}),(0,l.jsx)(m.$,{onClick:()=>h("create"),children:"Create First Pool"})]}):(0,l.jsx)("ul",{className:"space-y-2",children:s.questionPools.map(e=>(0,l.jsx)("li",{className:"p-3 border rounded-md cursor-pointer transition-colors ".concat(x===e.id?"border-primary bg-primary/5":"hover:border-primary/50"),onClick:()=>M(e.id),children:(0,l.jsxs)("div",{className:"flex justify-between items-start",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"font-medium",children:e.title||"Pool ".concat(e.poolId)}),(0,l.jsxs)("p",{className:"text-xs text-muted-foreground",children:[e.questions.length," questions"]})]}),(0,l.jsxs)(m.$,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0",onClick:s=>{s.stopPropagation(),V(e.id)},children:[(0,l.jsx)("span",{className:"sr-only",children:"Delete"}),(0,l.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,l.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})]})]})},e.id))})]}),(0,l.jsx)("div",{className:"border rounded-md p-4 h-[500px] overflow-y-auto",children:x&&N?(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("h3",{className:"font-medium",children:"Edit Pool"}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("label",{htmlFor:"poolTitle",className:"text-sm font-medium",children:"Pool Title"}),(0,l.jsx)("input",{id:"poolTitle",type:"text",value:N.title,onChange:e=>b({...N,title:e.target.value}),className:"w-full p-2 border rounded-md",placeholder:"Enter pool title"})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("label",{htmlFor:"poolDescription",className:"text-sm font-medium",children:"Description"}),(0,l.jsx)("textarea",{id:"poolDescription",value:N.description,onChange:e=>b({...N,description:e.target.value}),className:"w-full p-2 border rounded-md",placeholder:"Enter pool description"})]}),(0,l.jsx)("div",{className:"flex justify-end",children:(0,l.jsx)(m.$,{onClick:H,disabled:y,children:y?"Saving...":"Save Pool"})}),(0,l.jsx)("div",{className:"pt-4 border-t mt-4",children:(0,l.jsx)("h4",{className:"font-medium mb-2",children:"Questions in this Pool"})})]}):(0,l.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,l.jsx)("p",{className:"text-muted-foreground",children:"Select a pool to edit"})})})]})}),(0,l.jsx)(u,{value:"rules",children:(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"border rounded-md p-4 h-[500px] overflow-y-auto",children:[(0,l.jsxs)("h3",{className:"font-medium mb-4",children:["Selection Rules (",s.selectionRules.length,")"]}),0===s.selectionRules.length?(0,l.jsxs)("div",{className:"text-center py-8",children:[(0,l.jsx)("p",{className:"text-muted-foreground mb-4",children:"No selection rules yet. Create your first rule to get started."}),(0,l.jsx)(m.$,{onClick:()=>h("create"),children:"Create First Rule"})]}):(0,l.jsx)("ul",{className:"space-y-2",children:s.selectionRules.map(e=>{let t=s.questionPools.find(s=>s.id===e.poolId);return(0,l.jsx)("li",{className:"p-3 border rounded-md cursor-pointer transition-colors ".concat(C===e.id?"border-primary bg-primary/5":"hover:border-primary/50"),onClick:()=>D(e.id),children:(0,l.jsxs)("div",{className:"flex justify-between items-start",children:[(0,l.jsxs)("div",{children:[(0,l.jsxs)("p",{className:"font-medium",children:["Select ",e.selectCount," from ",(null==t?void 0:t.title)||e.poolId]}),(0,l.jsxs)("p",{className:"text-xs text-muted-foreground",children:[e.randomize?"Random selection":"Sequential selection",e.shuffleOrder?", shuffled order":""]})]}),(0,l.jsxs)(m.$,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0",onClick:s=>{s.stopPropagation(),Z(e.id)},children:[(0,l.jsx)("span",{className:"sr-only",children:"Delete"}),(0,l.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,l.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})]})]})},e.id)})})]}),(0,l.jsx)("div",{className:"border rounded-md p-4 h-[500px] overflow-y-auto",children:C&&F?(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("h3",{className:"font-medium",children:"Edit Selection Rule"}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("label",{htmlFor:"rulePool",className:"text-sm font-medium",children:"Question Pool"}),(0,l.jsxs)("select",{id:"rulePool",value:F.poolId,onChange:e=>L({...F,poolId:e.target.value}),className:"w-full p-2 border rounded-md",required:!0,children:[(0,l.jsx)("option",{value:"",children:"-- Select a pool --"}),s.questionPools.map(e=>(0,l.jsxs)("option",{value:e.id,children:[e.title||"Pool ".concat(e.poolId)," (",e.questions.length," questions)"]},e.id))]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("label",{htmlFor:"ruleSelectCount",className:"text-sm font-medium",children:"Number of Questions to Select"}),(0,l.jsx)("input",{id:"ruleSelectCount",type:"number",min:"1",value:F.selectCount,onChange:e=>L({...F,selectCount:parseInt(e.target.value)}),className:"w-full p-2 border rounded-md",required:!0})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,l.jsx)("input",{type:"checkbox",checked:F.randomize,onChange:e=>L({...F,randomize:e.target.checked})}),(0,l.jsx)("span",{children:"Randomize Selection"})]}),(0,l.jsx)("p",{className:"text-xs text-muted-foreground ml-6",children:"If checked, questions will be randomly selected from the pool."})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,l.jsx)("input",{type:"checkbox",checked:F.shuffleOrder,onChange:e=>L({...F,shuffleOrder:e.target.checked})}),(0,l.jsx)("span",{children:"Shuffle Order"})]}),(0,l.jsx)("p",{className:"text-xs text-muted-foreground ml-6",children:"If checked, the order of selected questions will be randomized."})]}),(0,l.jsx)("div",{className:"flex justify-end",children:(0,l.jsx)(m.$,{onClick:U,disabled:R,children:R?"Saving...":"Save Rule"})})]}):(0,l.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,l.jsx)("p",{className:"text-muted-foreground",children:"Select a rule to edit"})})})]})}),(0,l.jsx)(u,{value:"create",children:(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,l.jsxs)("div",{className:"border rounded-md p-4",children:[(0,l.jsx)("h3",{className:"font-medium mb-4",children:"Create New Pool"}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("label",{htmlFor:"newPoolTitle",className:"text-sm font-medium",children:"Pool Title"}),(0,l.jsx)("input",{id:"newPoolTitle",type:"text",value:f,onChange:e=>j(e.target.value),className:"w-full p-2 border rounded-md",placeholder:"Enter pool title",required:!0})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("label",{htmlFor:"newPoolDescription",className:"text-sm font-medium",children:"Description"}),(0,l.jsx)("textarea",{id:"newPoolDescription",value:v,onChange:e=>g(e.target.value),className:"w-full p-2 border rounded-md",placeholder:"Enter pool description (optional)"})]}),(0,l.jsx)("div",{className:"flex justify-end",children:(0,l.jsx)(m.$,{onClick:W,disabled:!f||y,children:y?"Creating...":"Create Pool"})})]})]}),(0,l.jsxs)("div",{className:"border rounded-md p-4",children:[(0,l.jsx)("h3",{className:"font-medium mb-4",children:"Create New Selection Rule"}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("label",{htmlFor:"newRulePool",className:"text-sm font-medium",children:"Question Pool"}),(0,l.jsxs)("select",{id:"newRulePool",value:S,onChange:e=>q(e.target.value),className:"w-full p-2 border rounded-md",required:!0,children:[(0,l.jsx)("option",{value:"",children:"-- Select a pool --"}),s.questionPools.map(e=>(0,l.jsxs)("option",{value:e.id,children:[e.title||"Pool ".concat(e.poolId)," (",e.questions.length," questions)"]},e.id))]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("label",{htmlFor:"newRuleSelectCount",className:"text-sm font-medium",children:"Number of Questions to Select"}),(0,l.jsx)("input",{id:"newRuleSelectCount",type:"number",min:"1",value:_,onChange:e=>z(e.target.value),className:"w-full p-2 border rounded-md",required:!0})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,l.jsx)("input",{type:"checkbox",checked:P,onChange:e=>E(e.target.checked)}),(0,l.jsx)("span",{children:"Randomize Selection"})]}),(0,l.jsx)("p",{className:"text-xs text-muted-foreground ml-6",children:"If checked, questions will be randomly selected from the pool."})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,l.jsx)("input",{type:"checkbox",checked:T,onChange:e=>A(e.target.checked)}),(0,l.jsx)("span",{children:"Shuffle Order"})]}),(0,l.jsx)("p",{className:"text-xs text-muted-foreground ml-6",children:"If checked, the order of selected questions will be randomized."})]}),(0,l.jsx)("div",{className:"flex justify-end",children:(0,l.jsx)(m.$,{onClick:J,disabled:!S||!_||R,children:R?"Creating...":"Create Rule"})})]})]})]})})]})})}var C=t(8173),k=t.n(C);function S(e){let{quiz:s,onPublishStatusChange:t,isSaving:a}=e,[n,r]=(0,i.useState)(!1);return(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"p-4 border rounded-md bg-muted/30",children:[(0,l.jsx)("h3",{className:"font-medium mb-2",children:"Current Status"}),(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)("div",{className:"w-3 h-3 rounded-full ".concat(s.isPublished?"bg-green-500":"bg-amber-500")}),(0,l.jsx)("p",{children:s.isPublished?"Published - This quiz is visible to others":"Draft - Only you can see this quiz"})]})]}),n?(0,l.jsxs)("div",{className:"p-4 border rounded-md bg-primary/5",children:[(0,l.jsx)("h3",{className:"font-medium mb-2",children:"Publish Confirmation"}),(0,l.jsx)("p",{className:"mb-4",children:"Are you sure you want to publish this quiz? Once published, it will be visible to others."}),(0,l.jsxs)("div",{className:"flex space-x-4",children:[(0,l.jsx)(m.$,{onClick:()=>{t(!0),r(!1)},disabled:a,children:a?"Publishing...":"Yes, Publish Quiz"}),(0,l.jsx)(m.$,{variant:"outline",onClick:()=>{r(!1)},disabled:a,children:"Cancel"})]})]}):(0,l.jsxs)("div",{className:"flex space-x-4",children:[(0,l.jsx)(m.$,{onClick:()=>{s.isPublished?t(!1):r(!0)},variant:s.isPublished?"outline":"default",disabled:a,children:a?s.isPublished?"Unpublishing...":"Publishing...":s.isPublished?"Unpublish Quiz":"Publish Quiz"}),s.isPublished&&(0,l.jsx)(m.$,{asChild:!0,children:(0,l.jsx)(k(),{href:"/quiz/".concat(s.id),target:"_blank",children:"View Published Quiz"})})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("h3",{className:"font-medium",children:"Publishing Checklist"}),(0,l.jsxs)("ul",{className:"space-y-2",children:[(0,l.jsxs)("li",{className:"flex items-start space-x-2",children:[(0,l.jsx)("div",{className:"mt-1 w-5 h-5 flex items-center justify-center rounded-full ".concat(s.title?"bg-green-100 text-green-600":"bg-amber-100 text-amber-600"),children:s.title?"✓":"!"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"font-medium",children:"Quiz Title"}),(0,l.jsx)("p",{className:"text-sm text-muted-foreground",children:s.title?"Title is set":"Quiz needs a title"})]})]}),(0,l.jsxs)("li",{className:"flex items-start space-x-2",children:[(0,l.jsx)("div",{className:"mt-1 w-5 h-5 flex items-center justify-center rounded-full ".concat(s.questions.length>0?"bg-green-100 text-green-600":"bg-amber-100 text-amber-600"),children:s.questions.length>0?"✓":"!"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"font-medium",children:"Questions"}),(0,l.jsx)("p",{className:"text-sm text-muted-foreground",children:s.questions.length>0?"Quiz has ".concat(s.questions.length," question(s)"):"Quiz needs at least one question"})]})]}),(0,l.jsxs)("li",{className:"flex items-start space-x-2",children:[(0,l.jsx)("div",{className:"mt-1 w-5 h-5 flex items-center justify-center rounded-full ".concat(null!==s.passingScore?"bg-green-100 text-green-600":"bg-amber-100 text-amber-600"),children:null!==s.passingScore?"✓":"!"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"font-medium",children:"Passing Score"}),(0,l.jsx)("p",{className:"text-sm text-muted-foreground",children:null!==s.passingScore?"Passing score is set to ".concat(s.passingScore,"%"):"Consider setting a passing score"})]})]}),(0,l.jsxs)("li",{className:"flex items-start space-x-2",children:[(0,l.jsx)("div",{className:"mt-1 w-5 h-5 flex items-center justify-center rounded-full ".concat(null!==s.timeLimit?"bg-green-100 text-green-600":"bg-amber-100 text-amber-600"),children:null!==s.timeLimit?"✓":"!"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"font-medium",children:"Time Limit"}),(0,l.jsx)("p",{className:"text-sm text-muted-foreground",children:null!==s.timeLimit?"Time limit is set to ".concat(s.timeLimit," minutes"):"Consider setting a time limit"})]})]})]})]})]})}function q(e){let{quiz:s}=e,t=(0,a.useRouter)(),[n,r]=(0,i.useState)(s),[p,f]=(0,i.useState)("details"),[j,v]=(0,i.useState)(!1),[g,N]=(0,i.useState)(null),b=async e=>{v(!0),N(null);try{let s=await fetch("/api/quizzes/".concat(n.id),{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!s.ok)throw Error("Failed to update quiz details");let t=await s.json();r(e=>({...e,...t})),N("Quiz details saved successfully")}catch(e){console.error("Error saving quiz details:",e),N("Error saving quiz details")}finally{v(!1)}},C=async e=>{v(!0),N(null);try{let s=await fetch("/api/quizzes/".concat(n.id,"/questions"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!s.ok)throw Error("Failed to add question");let t=await s.json();r(e=>({...e,questions:[...e.questions,t]})),N("Question added successfully")}catch(e){console.error("Error adding question:",e),N("Error adding question")}finally{v(!1)}},k=async(e,s)=>{v(!0),N(null);try{let t=await fetch("/api/quizzes/".concat(n.id,"/questions/").concat(e),{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify(s)});if(!t.ok)throw Error("Failed to update question");let l=await t.json();r(s=>({...s,questions:s.questions.map(s=>s.id===e?{...s,...l}:s)})),N("Question updated successfully")}catch(e){console.error("Error updating question:",e),N("Error updating question")}finally{v(!1)}},q=async e=>{v(!0),N(null);try{if(!(await fetch("/api/quizzes/".concat(n.id,"/questions/").concat(e),{method:"DELETE"})).ok)throw Error("Failed to delete question");r(s=>({...s,questions:s.questions.filter(s=>s.id!==e)})),N("Question deleted successfully")}catch(e){console.error("Error deleting question:",e),N("Error deleting question")}finally{v(!1)}},_=async e=>{v(!0),N(null);try{if(!(await fetch("/api/quizzes/".concat(n.id,"/publish"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({isPublished:e})})).ok)throw Error("Failed to ".concat(e?"publish":"unpublish"," quiz"));r(s=>({...s,isPublished:e})),N("Quiz ".concat(e?"published":"unpublished"," successfully"))}catch(s){console.error("Error ".concat(e?"publishing":"unpublishing"," quiz:"),s),N("Error ".concat(e?"publishing":"unpublishing"," quiz"))}finally{v(!1)}};return(0,i.useEffect)(()=>{if(g){let e=setTimeout(()=>{N(null)},3e3);return()=>clearTimeout(e)}},[g]),(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"flex justify-between items-center",children:[(0,l.jsx)("h1",{className:"text-3xl font-bold",children:"Edit Quiz"}),(0,l.jsxs)("div",{className:"flex items-center gap-4",children:[g&&(0,l.jsx)("p",{className:"text-sm ".concat(g.includes("Error")?"text-red-500":"text-green-500"),children:g}),(0,l.jsx)(m.$,{variant:"outline",onClick:()=>t.push("/dashboard/quizzes"),children:"Back to Quizzes"}),(0,l.jsx)(m.$,{onClick:()=>t.push("/dashboard/quizzes/".concat(n.id,"/preview")),children:"Preview Quiz"})]})]}),(0,l.jsxs)(o,{value:p,onValueChange:f,children:[(0,l.jsxs)(d,{className:"grid grid-cols-4 w-full max-w-3xl",children:[(0,l.jsx)(c,{value:"details",children:"Quiz Details"}),(0,l.jsx)(c,{value:"questions",children:"Questions"}),(0,l.jsx)(c,{value:"pools",children:"Question Pools"}),(0,l.jsx)(c,{value:"publish",children:"Publish"})]}),(0,l.jsx)(u,{value:"details",children:(0,l.jsxs)(h.Zp,{children:[(0,l.jsxs)(h.aR,{children:[(0,l.jsx)(h.ZB,{children:"Quiz Details"}),(0,l.jsx)(h.BT,{children:"Edit the basic information for your quiz"})]}),(0,l.jsx)(h.Wu,{children:(0,l.jsx)(x,{quiz:n,onSave:b,isSaving:j})})]})}),(0,l.jsx)(u,{value:"questions",children:(0,l.jsxs)(h.Zp,{children:[(0,l.jsxs)(h.aR,{children:[(0,l.jsx)(h.ZB,{children:"Questions"}),(0,l.jsx)(h.BT,{children:"Add, edit, or remove questions from your quiz"})]}),(0,l.jsx)(h.Wu,{children:(0,l.jsx)(y,{questions:n.questions,onAddQuestion:C,onUpdateQuestion:k,onDeleteQuestion:q,isSaving:j})})]})}),(0,l.jsx)(u,{value:"pools",children:(0,l.jsxs)(h.Zp,{children:[(0,l.jsxs)(h.aR,{children:[(0,l.jsx)(h.ZB,{children:"Question Pools"}),(0,l.jsx)(h.BT,{children:"Create pools of questions for dynamic selection"})]}),(0,l.jsx)(h.Wu,{children:(0,l.jsx)(w,{quiz:n,setQuiz:r,isSaving:j})})]})}),(0,l.jsx)(u,{value:"publish",children:(0,l.jsxs)(h.Zp,{children:[(0,l.jsxs)(h.aR,{children:[(0,l.jsx)(h.ZB,{children:"Publish Settings"}),(0,l.jsx)(h.BT,{children:"Control the visibility and availability of your quiz"})]}),(0,l.jsx)(h.Wu,{children:(0,l.jsx)(S,{quiz:n,onPublishStatusChange:_,isSaving:j})})]})})]})]})}},3312:(e,s,t)=>{"use strict";t.d(s,{$:()=>d});var l=t(5155),i=t(2115),a=t(2317),n=t(1027),r=t(1684);let o=(0,n.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=i.forwardRef((e,s)=>{let{className:t,variant:i,size:n,asChild:d=!1,...c}=e,u=d?a.DX:"button";return(0,l.jsx)(u,{className:(0,r.cn)(o({variant:i,size:n,className:t})),ref:s,...c})});d.displayName="Button"},9749:(e,s,t)=>{"use strict";t.d(s,{BT:()=>d,Wu:()=>c,ZB:()=>o,Zp:()=>n,aR:()=>r,wL:()=>u});var l=t(5155),i=t(2115),a=t(1684);let n=i.forwardRef((e,s)=>{let{className:t,...i}=e;return(0,l.jsx)("div",{ref:s,className:(0,a.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...i})});n.displayName="Card";let r=i.forwardRef((e,s)=>{let{className:t,...i}=e;return(0,l.jsx)("div",{ref:s,className:(0,a.cn)("flex flex-col space-y-1.5 p-6",t),...i})});r.displayName="CardHeader";let o=i.forwardRef((e,s)=>{let{className:t,...i}=e;return(0,l.jsx)("h3",{ref:s,className:(0,a.cn)("text-2xl font-semibold leading-none tracking-tight",t),...i})});o.displayName="CardTitle";let d=i.forwardRef((e,s)=>{let{className:t,...i}=e;return(0,l.jsx)("p",{ref:s,className:(0,a.cn)("text-sm text-muted-foreground",t),...i})});d.displayName="CardDescription";let c=i.forwardRef((e,s)=>{let{className:t,...i}=e;return(0,l.jsx)("div",{ref:s,className:(0,a.cn)("p-6 pt-0",t),...i})});c.displayName="CardContent";let u=i.forwardRef((e,s)=>{let{className:t,...i}=e;return(0,l.jsx)("div",{ref:s,className:(0,a.cn)("flex items-center p-6 pt-0",t),...i})});u.displayName="CardFooter"},1684:(e,s,t)=>{"use strict";t.d(s,{$4:()=>o,Yq:()=>r,cn:()=>a,lk:()=>n});var l=t(3463),i=t(9795);function a(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,i.QP)((0,l.$)(s))}function n(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){let s=16*Math.random()|0;return("x"===e?s:3&s|8).toString(16)})}function r(e){return("string"==typeof e?new Date(e):e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})}function o(e,s){return 0===s?0:Math.round(e/s*100)}}},e=>{var s=s=>e(e.s=s);e.O(0,[173,181,164,441,517,358],()=>s(4770)),_N_E=e.O()}]);