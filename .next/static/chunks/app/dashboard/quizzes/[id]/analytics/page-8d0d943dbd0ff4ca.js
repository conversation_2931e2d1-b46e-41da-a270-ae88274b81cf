(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[952],{2367:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,8173,23)),Promise.resolve().then(s.bind(s,1322)),Promise.resolve().then(s.bind(s,56)),Promise.resolve().then(s.bind(s,7809))},1322:(e,t,s)=>{"use strict";s.d(t,{default:()=>l});var r=s(5155);function l(e){let{questions:t}=e;return 0===t.length?(0,r.jsx)("div",{className:"text-center py-8",children:(0,r.jsx)("p",{className:"text-muted-foreground",children:"No question data available"})}):(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"w-full",children:[(0,r.jsx)("thead",{children:(0,r.jsxs)("tr",{className:"border-b",children:[(0,r.jsx)("th",{className:"text-left py-3 px-4 font-medium",children:"Question"}),(0,r.jsx)("th",{className:"text-left py-3 px-4 font-medium",children:"Type"}),(0,r.jsx)("th",{className:"text-left py-3 px-4 font-medium",children:"Correct"}),(0,r.jsx)("th",{className:"text-left py-3 px-4 font-medium",children:"Total"}),(0,r.jsx)("th",{className:"text-left py-3 px-4 font-medium",children:"Success Rate"})]})}),(0,r.jsx)("tbody",{children:t.map(e=>{var t;return(0,r.jsxs)("tr",{className:"border-b hover:bg-muted/50",children:[(0,r.jsx)("td",{className:"py-3 px-4",children:(0,r.jsx)("div",{className:"line-clamp-1",children:e.text})}),(0,r.jsx)("td",{className:"py-3 px-4",children:(0,r.jsx)("span",{className:"capitalize",children:e.type.replace(/_/g," ")})}),(0,r.jsx)("td",{className:"py-3 px-4",children:e.correctCount}),(0,r.jsx)("td",{className:"py-3 px-4",children:e.totalAttempts}),(0,r.jsx)("td",{className:"py-3 px-4",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-full max-w-[100px] bg-muted rounded-full h-2.5 mr-2",children:(0,r.jsx)("div",{className:"h-2.5 rounded-full",style:{width:"".concat(e.correctPercentage,"%"),backgroundColor:(t=e.correctPercentage)<30?"#ef4444":t<60?"#f97316":t<80?"#eab308":"#22c55e"}})}),(0,r.jsxs)("span",{children:[e.correctPercentage.toFixed(1),"%"]})]})})]},e.id)})})]})})}},56:(e,t,s)=>{"use strict";s.d(t,{default:()=>a});var r=s(5155),l=s(9749);function a(e){let{totalResponses:t,completedResponses:s,averageScore:a,averageTimeSpent:c,passingRate:n}=e;return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(l.Zp,{children:(0,r.jsx)(l.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Total Responses"}),(0,r.jsx)("p",{className:"text-3xl font-bold",children:t}),(0,r.jsxs)("p",{className:"text-xs text-muted-foreground",children:[s," completed (",Math.round(s/t*100)||0,"%)"]})]})})}),(0,r.jsx)(l.Zp,{children:(0,r.jsx)(l.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Average Score"}),(0,r.jsxs)("p",{className:"text-3xl font-bold",children:[a.toFixed(1),"%"]})]})})}),(0,r.jsx)(l.Zp,{children:(0,r.jsx)(l.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Average Time"}),(0,r.jsx)("p",{className:"text-3xl font-bold",children:function(e){if(0===e)return"N/A";let t=Math.floor(e/60),s=Math.round(e%60);return 0===t?"".concat(s,"s"):"".concat(t,"m ").concat(s,"s")}(c)})]})})}),(0,r.jsx)(l.Zp,{children:(0,r.jsx)(l.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Passing Rate"}),(0,r.jsxs)("p",{className:"text-3xl font-bold",children:[n.toFixed(1),"%"]})]})})})]})}},7809:(e,t,s)=>{"use strict";s.d(t,{default:()=>a});var r=s(5155),l=s(1684);function a(e){let{responses:t}=e;return 0===t.length?(0,r.jsx)("div",{className:"text-center py-8",children:(0,r.jsx)("p",{className:"text-muted-foreground",children:"No responses yet"})}):(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"w-full",children:[(0,r.jsx)("thead",{children:(0,r.jsxs)("tr",{className:"border-b",children:[(0,r.jsx)("th",{className:"text-left py-3 px-4 font-medium",children:"User"}),(0,r.jsx)("th",{className:"text-left py-3 px-4 font-medium",children:"Score"}),(0,r.jsx)("th",{className:"text-left py-3 px-4 font-medium",children:"Time Spent"}),(0,r.jsx)("th",{className:"text-left py-3 px-4 font-medium",children:"Started"}),(0,r.jsx)("th",{className:"text-left py-3 px-4 font-medium",children:"Completed"}),(0,r.jsx)("th",{className:"text-left py-3 px-4 font-medium",children:"Status"})]})}),(0,r.jsx)("tbody",{children:t.map(e=>{var t,s;return(0,r.jsxs)("tr",{className:"border-b hover:bg-muted/50",children:[(0,r.jsx)("td",{className:"py-3 px-4",children:(null===(t=e.user)||void 0===t?void 0:t.name)||(null===(s=e.user)||void 0===s?void 0:s.email)||"Anonymous"}),(0,r.jsxs)("td",{className:"py-3 px-4",children:[e.score.toFixed(1),"%"]}),(0,r.jsx)("td",{className:"py-3 px-4",children:e.timeSpent?function(e){if(e<60)return"".concat(e," sec");let t=Math.floor(e/60);if(t<60)return"".concat(t," min ").concat(e%60," sec");let s=Math.floor(t/60);return"".concat(s," hr ").concat(t%60," min")}(e.timeSpent):"N/A"}),(0,r.jsx)("td",{className:"py-3 px-4",children:(0,l.Yq)(e.startedAt)}),(0,r.jsx)("td",{className:"py-3 px-4",children:e.completedAt?(0,l.Yq)(e.completedAt):"N/A"}),(0,r.jsx)("td",{className:"py-3 px-4",children:(0,r.jsx)("span",{className:"inline-block px-2 py-1 text-xs rounded-full ".concat(e.completedAt?"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100":"bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-100"),children:e.completedAt?"Completed":"In Progress"})})]},e.id)})})]})})}},9749:(e,t,s)=>{"use strict";s.d(t,{BT:()=>x,Wu:()=>i,ZB:()=>d,Zp:()=>c,aR:()=>n,wL:()=>o});var r=s(5155),l=s(2115),a=s(1684);let c=l.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,r.jsx)("div",{ref:t,className:(0,a.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",s),...l})});c.displayName="Card";let n=l.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,r.jsx)("div",{ref:t,className:(0,a.cn)("flex flex-col space-y-1.5 p-6",s),...l})});n.displayName="CardHeader";let d=l.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,r.jsx)("h3",{ref:t,className:(0,a.cn)("text-2xl font-semibold leading-none tracking-tight",s),...l})});d.displayName="CardTitle";let x=l.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,r.jsx)("p",{ref:t,className:(0,a.cn)("text-sm text-muted-foreground",s),...l})});x.displayName="CardDescription";let i=l.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,r.jsx)("div",{ref:t,className:(0,a.cn)("p-6 pt-0",s),...l})});i.displayName="CardContent";let o=l.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,r.jsx)("div",{ref:t,className:(0,a.cn)("flex items-center p-6 pt-0",s),...l})});o.displayName="CardFooter"},1684:(e,t,s)=>{"use strict";s.d(t,{$4:()=>d,Yq:()=>n,cn:()=>a,lk:()=>c});var r=s(3463),l=s(9795);function a(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,l.QP)((0,r.$)(t))}function c(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){let t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)})}function n(e){return("string"==typeof e?new Date(e):e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})}function d(e,t){return 0===t?0:Math.round(e/t*100)}}},e=>{var t=t=>e(e.s=t);e.O(0,[173,181,441,517,358],()=>t(2367)),_N_E=e.O()}]);