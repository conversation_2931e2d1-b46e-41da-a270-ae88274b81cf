"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[164],{6046:(e,t,n)=>{var r=n(6658);n.o(r,"usePathname")&&n.d(t,{usePathname:function(){return r.usePathname}}),n.o(r,"useRouter")&&n.d(t,{useRouter:function(){return r.useRouter}})},8068:(e,t,n)=>{n.d(t,{s:()=>i,t:()=>l});var r=n(2115);function o(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function l(...e){return t=>{let n=!1,r=e.map(e=>{let r=o(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():o(e[t],null)}}}}function i(...e){return r.useCallback(l(...e),e)}},2317:(e,t,n)=>{n.d(t,{DX:()=>u,TL:()=>i});var r=n(2115),o=n(8068),l=n(5155);function i(e){let t=function(e){let t=r.forwardRef((e,t)=>{let{children:n,...l}=e;if(r.isValidElement(n)){let e,i;let u=(e=Object.getOwnPropertyDescriptor(n.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?n.ref:(e=Object.getOwnPropertyDescriptor(n,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?n.props.ref:n.props.ref||n.ref,a=function(e,t){let n={...t};for(let r in t){let o=e[r],l=t[r];/^on[A-Z]/.test(r)?o&&l?n[r]=(...e)=>{let t=l(...e);return o(...e),t}:o&&(n[r]=o):"style"===r?n[r]={...o,...l}:"className"===r&&(n[r]=[o,l].filter(Boolean).join(" "))}return{...e,...n}}(l,n.props);return n.type!==r.Fragment&&(a.ref=t?(0,o.t)(t,u):u),r.cloneElement(n,a)}return r.Children.count(n)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=r.forwardRef((e,n)=>{let{children:o,...i}=e,u=r.Children.toArray(o),a=u.find(s);if(a){let e=a.props.children,o=u.map(t=>t!==a?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,l.jsx)(t,{...i,ref:n,children:r.isValidElement(e)?r.cloneElement(e,void 0,o):null})}return(0,l.jsx)(t,{...i,ref:n,children:o})});return n.displayName=`${e}.Slot`,n}var u=i("Slot"),a=Symbol("radix.slottable");function s(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===a}},4663:(e,t,n)=>{n.d(t,{UC:()=>er,B8:()=>et,bL:()=>ee,l9:()=>en});var r,o=n(2115),l=n.t(o,2);function i(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}var u=n(5155);function a(e,t=[]){let n=[],r=()=>{let t=n.map(e=>o.createContext(e));return function(n){let r=n?.[e]||t;return o.useMemo(()=>({[`__scope${e}`]:{...n,[e]:r}}),[n,r])}};return r.scopeName=e,[function(t,r){let l=o.createContext(r),i=n.length;n=[...n,r];let a=t=>{let{scope:n,children:r,...a}=t,s=n?.[e]?.[i]||l,c=o.useMemo(()=>a,Object.values(a));return(0,u.jsx)(s.Provider,{value:c,children:r})};return a.displayName=t+"Provider",[a,function(n,u){let a=u?.[e]?.[i]||l,s=o.useContext(a);if(s)return s;if(void 0!==r)return r;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let r=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return o.useMemo(()=>({[`__scope${t.scopeName}`]:r}),[r])}};return n.scopeName=t.scopeName,n}(r,...t)]}function s(e,t,n){if(!t.has(e))throw TypeError("attempted to "+n+" private field on non-instance");return t.get(e)}var c=n(8068),d=n(2317),f=globalThis?.document?o.useLayoutEffect:()=>{},m=l[" useId ".trim().toString()]||(()=>void 0),p=0;function v(e){let[t,n]=o.useState(m());return f(()=>{e||n(e=>e??String(p++))},[e]),e||(t?`radix-${t}`:"")}n(7650);var y=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,d.TL)(`Primitive.${t}`),r=o.forwardRef((e,r)=>{let{asChild:o,...l}=e,i=o?n:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,u.jsx)(i,{...l,ref:r})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{}),b=l[" useInsertionEffect ".trim().toString()]||f;function h({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[l,i,u]=function({defaultProp:e,onChange:t}){let[n,r]=o.useState(e),l=o.useRef(n),i=o.useRef(t);return b(()=>{i.current=t},[t]),o.useEffect(()=>{l.current!==n&&(i.current?.(n),l.current=n)},[n,l]),[n,r,i]}({defaultProp:t,onChange:n}),a=void 0!==e,s=a?e:l;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==a){let t=a?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=a},[a,r])}return[s,o.useCallback(t=>{if(a){let n="function"==typeof t?t(e):t;n!==e&&u.current?.(n)}else i(t)},[a,e,i,u])]}Symbol("RADIX:SYNC_STATE");var g=o.createContext(void 0);function w(e){let t=o.useContext(g);return e||t||"ltr"}var R="rovingFocusGroup.onEntryFocus",N={bubbles:!1,cancelable:!0},x="RovingFocusGroup",[C,E,T]=function(e){let t=e+"CollectionProvider",[n,r]=a(t),[l,i]=n(t,{collectionRef:{current:null},itemMap:new Map}),s=e=>{let{scope:t,children:n}=e,r=o.useRef(null),i=o.useRef(new Map).current;return(0,u.jsx)(l,{scope:t,itemMap:i,collectionRef:r,children:n})};s.displayName=t;let f=e+"CollectionSlot",m=(0,d.TL)(f),p=o.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=i(f,n),l=(0,c.s)(t,o.collectionRef);return(0,u.jsx)(m,{ref:l,children:r})});p.displayName=f;let v=e+"CollectionItemSlot",y="data-radix-collection-item",b=(0,d.TL)(v),h=o.forwardRef((e,t)=>{let{scope:n,children:r,...l}=e,a=o.useRef(null),s=(0,c.s)(t,a),d=i(v,n);return o.useEffect(()=>(d.itemMap.set(a,{ref:a,...l}),()=>void d.itemMap.delete(a))),(0,u.jsx)(b,{[y]:"",ref:s,children:r})});return h.displayName=v,[{Provider:s,Slot:p,ItemSlot:h},function(t){let n=i(e+"CollectionConsumer",t);return o.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(y,"]")));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},r]}(x),[I,A]=a(x,[T]),[M,S]=I(x),j=o.forwardRef((e,t)=>(0,u.jsx)(C.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,u.jsx)(C.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,u.jsx)(D,{...e,ref:t})})}));j.displayName=x;var D=o.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,orientation:r,loop:l=!1,dir:a,currentTabStopId:s,defaultCurrentTabStopId:d,onCurrentTabStopIdChange:f,onEntryFocus:m,preventScrollOnEntryFocus:p=!1,...v}=e,b=o.useRef(null),g=(0,c.s)(t,b),C=w(a),[T,I]=h({prop:s,defaultProp:null!=d?d:null,onChange:f,caller:x}),[A,S]=o.useState(!1),j=function(e){let t=o.useRef(e);return o.useEffect(()=>{t.current=e}),o.useMemo(()=>(...e)=>t.current?.(...e),[])}(m),D=E(n),O=o.useRef(!1),[F,P]=o.useState(0);return o.useEffect(()=>{let e=b.current;if(e)return e.addEventListener(R,j),()=>e.removeEventListener(R,j)},[j]),(0,u.jsx)(M,{scope:n,orientation:r,dir:C,loop:l,currentTabStopId:T,onItemFocus:o.useCallback(e=>I(e),[I]),onItemShiftTab:o.useCallback(()=>S(!0),[]),onFocusableItemAdd:o.useCallback(()=>P(e=>e+1),[]),onFocusableItemRemove:o.useCallback(()=>P(e=>e-1),[]),children:(0,u.jsx)(y.div,{tabIndex:A||0===F?-1:0,"data-orientation":r,...v,ref:g,style:{outline:"none",...e.style},onMouseDown:i(e.onMouseDown,()=>{O.current=!0}),onFocus:i(e.onFocus,e=>{let t=!O.current;if(e.target===e.currentTarget&&t&&!A){let t=new CustomEvent(R,N);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=D().filter(e=>e.focusable);_([e.find(e=>e.active),e.find(e=>e.id===T),...e].filter(Boolean).map(e=>e.ref.current),p)}}O.current=!1}),onBlur:i(e.onBlur,()=>S(!1))})})}),O="RovingFocusGroupItem",F=o.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,focusable:r=!0,active:l=!1,tabStopId:a,children:s,...c}=e,d=v(),f=a||d,m=S(O,n),p=m.currentTabStopId===f,b=E(n),{onFocusableItemAdd:h,onFocusableItemRemove:g,currentTabStopId:w}=m;return o.useEffect(()=>{if(r)return h(),()=>g()},[r,h,g]),(0,u.jsx)(C.ItemSlot,{scope:n,id:f,focusable:r,active:l,children:(0,u.jsx)(y.span,{tabIndex:p?0:-1,"data-orientation":m.orientation,...c,ref:t,onMouseDown:i(e.onMouseDown,e=>{r?m.onItemFocus(f):e.preventDefault()}),onFocus:i(e.onFocus,()=>m.onItemFocus(f)),onKeyDown:i(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){m.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,n){var r;let o=(r=e.key,"rtl"!==n?r:"ArrowLeft"===r?"ArrowRight":"ArrowRight"===r?"ArrowLeft":r);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return P[o]}(e,m.orientation,m.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let n=b().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)n.reverse();else if("prev"===t||"next"===t){"prev"===t&&n.reverse();let r=n.indexOf(e.currentTarget);n=m.loop?function(e,t){return e.map((n,r)=>e[(t+r)%e.length])}(n,r+1):n.slice(r+1)}setTimeout(()=>_(n))}}),children:"function"==typeof s?s({isCurrentTabStop:p,hasTabStop:null!=w}):s})})});F.displayName=O;var P={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function _(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=document.activeElement;for(let r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}var L=e=>{let{present:t,children:n}=e,r=function(e){var t,n;let[r,l]=o.useState(),i=o.useRef(null),u=o.useRef(e),a=o.useRef("none"),[s,c]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},o.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return o.useEffect(()=>{let e=k(i.current);a.current="mounted"===s?e:"none"},[s]),f(()=>{let t=i.current,n=u.current;if(n!==e){let r=a.current,o=k(t);e?c("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?c("UNMOUNT"):n&&r!==o?c("ANIMATION_OUT"):c("UNMOUNT"),u.current=e}},[e,c]),f(()=>{if(r){var e;let t;let n=null!==(e=r.ownerDocument.defaultView)&&void 0!==e?e:window,o=e=>{let o=k(i.current).includes(e.animationName);if(e.target===r&&o&&(c("ANIMATION_END"),!u.current)){let e=r.style.animationFillMode;r.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===r.style.animationFillMode&&(r.style.animationFillMode=e)})}},l=e=>{e.target===r&&(a.current=k(i.current))};return r.addEventListener("animationstart",l),r.addEventListener("animationcancel",o),r.addEventListener("animationend",o),()=>{n.clearTimeout(t),r.removeEventListener("animationstart",l),r.removeEventListener("animationcancel",o),r.removeEventListener("animationend",o)}}c("ANIMATION_END")},[r,c]),{isPresent:["mounted","unmountSuspended"].includes(s),ref:o.useCallback(e=>{i.current=e?getComputedStyle(e):null,l(e)},[])}}(t),l="function"==typeof n?n({present:r.isPresent}):o.Children.only(n),i=(0,c.s)(r.ref,function(e){var t,n;let r=null===(t=Object.getOwnPropertyDescriptor(e.props,"ref"))||void 0===t?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null===(n=Object.getOwnPropertyDescriptor(e,"ref"))||void 0===n?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(l));return"function"==typeof n||r.isPresent?o.cloneElement(l,{ref:i}):null};function k(e){return(null==e?void 0:e.animationName)||"none"}L.displayName="Presence";var U="Tabs",[$,K]=a(U,[A]),V=A(),[W,B]=$(U),G=o.forwardRef((e,t)=>{let{__scopeTabs:n,value:r,onValueChange:o,defaultValue:l,orientation:i="horizontal",dir:a,activationMode:s="automatic",...c}=e,d=w(a),[f,m]=h({prop:r,onChange:o,defaultProp:null!=l?l:"",caller:U});return(0,u.jsx)(W,{scope:n,baseId:v(),value:f,onValueChange:m,orientation:i,dir:d,activationMode:s,children:(0,u.jsx)(y.div,{dir:d,"data-orientation":i,...c,ref:t})})});G.displayName=U;var q="TabsList",z=o.forwardRef((e,t)=>{let{__scopeTabs:n,loop:r=!0,...o}=e,l=B(q,n),i=V(n);return(0,u.jsx)(j,{asChild:!0,...i,orientation:l.orientation,dir:l.dir,loop:r,children:(0,u.jsx)(y.div,{role:"tablist","aria-orientation":l.orientation,...o,ref:t})})});z.displayName=q;var X="TabsTrigger",H=o.forwardRef((e,t)=>{let{__scopeTabs:n,value:r,disabled:o=!1,...l}=e,a=B(X,n),s=V(n),c=J(a.baseId,r),d=Q(a.baseId,r),f=r===a.value;return(0,u.jsx)(F,{asChild:!0,...s,focusable:!o,active:f,children:(0,u.jsx)(y.button,{type:"button",role:"tab","aria-selected":f,"aria-controls":d,"data-state":f?"active":"inactive","data-disabled":o?"":void 0,disabled:o,id:c,...l,ref:t,onMouseDown:i(e.onMouseDown,e=>{o||0!==e.button||!1!==e.ctrlKey?e.preventDefault():a.onValueChange(r)}),onKeyDown:i(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&a.onValueChange(r)}),onFocus:i(e.onFocus,()=>{let e="manual"!==a.activationMode;f||o||!e||a.onValueChange(r)})})})});H.displayName=X;var Y="TabsContent",Z=o.forwardRef((e,t)=>{let{__scopeTabs:n,value:r,forceMount:l,children:i,...a}=e,s=B(Y,n),c=J(s.baseId,r),d=Q(s.baseId,r),f=r===s.value,m=o.useRef(f);return o.useEffect(()=>{let e=requestAnimationFrame(()=>m.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,u.jsx)(L,{present:l||f,children:n=>{let{present:r}=n;return(0,u.jsx)(y.div,{"data-state":f?"active":"inactive","data-orientation":s.orientation,role:"tabpanel","aria-labelledby":c,hidden:!r,id:d,tabIndex:0,...a,ref:t,style:{...e.style,animationDuration:m.current?"0s":void 0},children:r&&i})}})});function J(e,t){return"".concat(e,"-trigger-").concat(t)}function Q(e,t){return"".concat(e,"-content-").concat(t)}Z.displayName=Y;var ee=G,et=z,en=H,er=Z},1027:(e,t,n)=>{n.d(t,{F:()=>i});var r=n(3463);let o=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,l=r.$,i=(e,t)=>n=>{var r;if((null==t?void 0:t.variants)==null)return l(e,null==n?void 0:n.class,null==n?void 0:n.className);let{variants:i,defaultVariants:u}=t,a=Object.keys(i).map(e=>{let t=null==n?void 0:n[e],r=null==u?void 0:u[e];if(null===t)return null;let l=o(t)||o(r);return i[e][l]}),s=n&&Object.entries(n).reduce((e,t)=>{let[n,r]=t;return void 0===r||(e[n]=r),e},{});return l(e,a,null==t?void 0:null===(r=t.compoundVariants)||void 0===r?void 0:r.reduce((e,t)=>{let{class:n,className:r,...o}=t;return Object.entries(o).every(e=>{let[t,n]=e;return Array.isArray(n)?n.includes({...u,...s}[t]):({...u,...s})[t]===n})?[...e,n,r]:e},[]),null==n?void 0:n.class,null==n?void 0:n.className)}}}]);