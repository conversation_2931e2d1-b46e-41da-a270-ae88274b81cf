"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[586],{7586:(e,r,t)=>{t.r(r),t.d(r,{default:()=>N});var s=t(5155),n=t(2115),i=t(842);function a(e,r){return"string"==typeof e?e:r&&e[r]?e[r]:e.default}let o=i.z.object({type:i.z.enum(["image","audio","video"]),url:i.z.string().url(),alt_text:i.z.string().optional(),caption:i.z.string().optional()}),l=i.z.object({text:i.z.string(),delay_seconds:i.z.number().optional()}),d=i.z.object({question_id:i.z.string(),condition:i.z.enum(["correct","incorrect","answered","not_answered","answer_is","selected_option_id"]),value:i.z.union([i.z.string(),i.z.array(i.z.string())]).optional()}),c=i.z.object({question_id:i.z.string(),type:i.z.string(),text:i.z.union([i.z.string(),i.z.record(i.z.string())]),points:i.z.number().positive(),feedback_correct:i.z.string().optional(),feedback_incorrect:i.z.string().optional(),media:i.z.array(o).optional(),hint:i.z.array(l).optional(),depends_on:d.optional()}),u=i.z.object({format_version:i.z.string(),quiz_id:i.z.string(),title:i.z.string(),description:i.z.string().optional(),author:i.z.string().optional(),creation_date:i.z.string().optional(),tags:i.z.array(i.z.string()).optional(),passing_score_percentage:i.z.number().min(0).max(100).optional(),time_limit_minutes:i.z.number().positive().optional(),markup_format:i.z.enum(["markdown","html","plain_text"]).optional(),locale:i.z.string().optional()});function m(e){let r=[...e];for(let e=r.length-1;e>0;e--){let t=Math.floor(Math.random()*(e+1));[r[e],r[t]]=[r[t],r[e]]}return r}i.z.object({quiz:i.z.object({$schema:i.z.string().optional(),metadata:u,questions:i.z.array(c).optional(),question_pools:i.z.array(i.z.object({pool_id:i.z.string(),title:i.z.string().optional(),description:i.z.string().optional(),questions:i.z.array(c)})).optional(),selection_rules:i.z.array(i.z.object({pool_id:i.z.string(),select_count:i.z.number().positive(),randomize:i.z.boolean().optional(),shuffle_order:i.z.boolean().optional()})).optional()})});var x=t(9749),p=t(3312),h=t(3628),f=t(1684);let g=e=>{let{question:r,answer:t,onAnswerChange:n,locale:i="en-US",disabled:o=!1}=e,l=e=>{n(e)},d=e=>{let r=Array.isArray(t)?t:t?[t]:[];r.includes(e)?n(r.filter(r=>r!==e)):n([...r,e])};return(0,s.jsx)("div",{className:"space-y-4",children:(0,s.jsx)("div",{className:"space-y-2",children:r.options.map(e=>{let n=Array.isArray(t)?t.includes(e.id):t===e.id;return(0,s.jsxs)("div",{className:(0,f.cn)("flex items-start space-x-3 p-3 border rounded-md cursor-pointer transition-colors",n?"border-primary bg-primary/5":"border-input hover:border-primary/50",o&&"opacity-60 cursor-not-allowed"),onClick:()=>{o||(r.single_correct_answer?l(e.id):d(e.id))},children:[(0,s.jsx)("div",{className:"flex-shrink-0 mt-0.5",children:r.single_correct_answer?(0,s.jsx)("div",{className:(0,f.cn)("w-5 h-5 border-2 rounded-full flex items-center justify-center",n?"border-primary":"border-muted-foreground"),children:n&&(0,s.jsx)("div",{className:"w-3 h-3 rounded-full bg-primary"})}):(0,s.jsx)("div",{className:(0,f.cn)("w-5 h-5 border-2 rounded-sm flex items-center justify-center",n?"border-primary bg-primary":"border-muted-foreground"),children:n&&(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"3",strokeLinecap:"round",strokeLinejoin:"round",className:"w-3 h-3 text-primary-foreground",children:(0,s.jsx)("polyline",{points:"20 6 9 17 4 12"})})})}),(0,s.jsx)("div",{className:"flex-1",children:(0,s.jsx)("div",{className:"text-sm font-medium",children:a(e.text,i)})})]},e.id)})})})},j=e=>{let{question:r,answer:t,onAnswerChange:n,disabled:i=!1}=e;return(0,s.jsx)("div",{className:"space-y-4",children:(0,s.jsxs)("div",{className:"flex flex-col space-y-3 sm:flex-row sm:space-y-0 sm:space-x-3",children:[(0,s.jsx)("div",{className:(0,f.cn)("flex-1 p-4 border rounded-md cursor-pointer transition-colors text-center",!0===t?"border-primary bg-primary/5":"border-input hover:border-primary/50",i&&"opacity-60 cursor-not-allowed"),onClick:()=>{i||n(!0)},children:(0,s.jsx)("div",{className:"font-medium",children:"True"})}),(0,s.jsx)("div",{className:(0,f.cn)("flex-1 p-4 border rounded-md cursor-pointer transition-colors text-center",!1===t?"border-primary bg-primary/5":"border-input hover:border-primary/50",i&&"opacity-60 cursor-not-allowed"),onClick:()=>{i||n(!1)},children:(0,s.jsx)("div",{className:"font-medium",children:"False"})})]})})},v=e=>{let{question:r,answer:t,onAnswerChange:n,disabled:i=!1}=e;return(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("div",{className:"relative",children:(0,s.jsx)("input",{type:"text",value:t||"",onChange:e=>n(e.target.value),disabled:i,placeholder:"Type your answer here...",className:"w-full p-3 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary disabled:opacity-60 disabled:cursor-not-allowed"})}),!1===r.exact_match&&(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Note: Your answer will be evaluated based on keywords, not exact matching."})]})},y=e=>{let{question:r,answer:t={},onAnswerChange:n,locale:i="en-US",disabled:o=!1}=e,l=(e,r)=>{n({...t,[e]:r})};return(0,s.jsx)("div",{className:"space-y-6",children:r.stems.map(e=>(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-4",children:[(0,s.jsx)("div",{className:"sm:w-1/2 font-medium",children:a(e.text,i)}),(0,s.jsx)("div",{className:"sm:w-1/2",children:(0,s.jsxs)("select",{value:t[e.id]||"",onChange:r=>l(e.id,r.target.value),disabled:o,className:"w-full p-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary disabled:opacity-60 disabled:cursor-not-allowed",children:[(0,s.jsx)("option",{value:"",children:"-- Select an option --"}),r.options.map(e=>(0,s.jsx)("option",{value:e.id,children:a(e.text,i)},e.id))]})})]},e.id))})},b=e=>{let{question:r,answer:t={},onAnswerChange:n,locale:i="en-US",disabled:o=!1}=e,l=(e,r)=>{n({...t,[e]:r})};return(0,s.jsx)("div",{className:"space-y-4",children:(0,s.jsx)("div",{className:"text-lg leading-relaxed",children:(()=>{let e=a(r.text_template,i).split(/(\[BLANK\])/g),n=0;return(0,s.jsx)("div",{className:"space-y-4",children:e.map((e,i)=>{if("[BLANK]"===e){let e=r.blanks[n];return n++,(0,s.jsx)("input",{type:"text",value:t[e.id]||"",onChange:r=>l(e.id,r.target.value),disabled:o,placeholder:e.hint||"...",className:"inline-block w-32 p-1 mx-1 border-b-2 border-primary focus:outline-none focus:border-b-2 focus:border-primary disabled:opacity-60 disabled:cursor-not-allowed text-center"},i)}return(0,s.jsx)("span",{children:e},i)})})})()})})},w=e=>{let{question:r,answer:t="",onAnswerChange:i,disabled:a=!1}=e,[o,l]=(0,n.useState)(0);(0,n.useEffect)(()=>{let e=t.trim().split(/\s+/);l(""===t.trim()?0:e.length)},[t]);let d=void 0!==r.min_word_count&&o<r.min_word_count,c=void 0!==r.max_word_count&&o>r.max_word_count;return(0,s.jsxs)("div",{className:"space-y-4",children:[r.guidelines&&(0,s.jsxs)("div",{className:"p-4 bg-muted rounded-md",children:[(0,s.jsx)("h4",{className:"font-medium mb-2",children:"Guidelines:"}),(0,s.jsx)("div",{className:"text-sm",children:r.guidelines})]}),(0,s.jsx)("div",{className:"relative",children:(0,s.jsx)("textarea",{value:t,onChange:e=>i(e.target.value),disabled:a,placeholder:"Type your answer here...",rows:8,className:"w-full p-3 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary disabled:opacity-60 disabled:cursor-not-allowed"})}),(0,s.jsx)("div",{className:"flex justify-between text-sm",children:(0,s.jsx)("div",{children:(void 0!==r.min_word_count||void 0!==r.max_word_count)&&(0,s.jsxs)("span",{className:d||c?"text-destructive":"text-muted-foreground",children:["Word count: ",o,void 0!==r.min_word_count&&" (min: ".concat(r.min_word_count,")"),void 0!==r.max_word_count&&" (max: ".concat(r.max_word_count,")")]})})})]})},_=e=>{let{question:r,answer:t,onAnswerChange:i,locale:o="en-US",showFeedback:l=!1}=e,[d,c]=(0,n.useState)([]);return(0,n.useEffect)(()=>{if(!r.hint||0===r.hint.length)return;let e=[];return r.hint.forEach(r=>{if(r.delay_seconds&&r.delay_seconds>0){let t=setTimeout(()=>{c(e=>[...e,r.text])},1e3*r.delay_seconds);e.push(t)}else c(e=>[...e,r.text])}),()=>{e.forEach(e=>clearTimeout(e))}},[r.hint]),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(()=>{let e=a(r.text,o);return(0,s.jsx)("div",{className:"prose dark:prose-invert max-w-none",children:(0,s.jsx)(h.oz,{children:e})})})(),r.media&&0!==r.media.length?(0,s.jsx)("div",{className:"mt-4 space-y-4",children:r.media.map((e,r)=>"image"===e.type?(0,s.jsxs)("figure",{className:"relative",children:[(0,s.jsx)("img",{src:e.url,alt:e.alt_text||"Question image",className:"max-w-full rounded-md"}),e.caption&&(0,s.jsx)("figcaption",{className:"text-sm text-muted-foreground mt-2",children:e.caption})]},r):"video"===e.type?(0,s.jsxs)("figure",{className:"relative",children:[(0,s.jsx)("video",{src:e.url,controls:!0,className:"max-w-full rounded-md"}),e.caption&&(0,s.jsx)("figcaption",{className:"text-sm text-muted-foreground mt-2",children:e.caption})]},r):"audio"===e.type?(0,s.jsxs)("figure",{className:"relative",children:[(0,s.jsx)("audio",{src:e.url,controls:!0,className:"w-full"}),e.caption&&(0,s.jsx)("figcaption",{className:"text-sm text-muted-foreground mt-2",children:e.caption})]},r):null)}):null]}),(0,s.jsx)("div",{className:"mt-4",children:(()=>{switch(r.type){case"multiple_choice":return(0,s.jsx)(g,{question:r,answer:t,onAnswerChange:i,locale:o});case"true_false":return(0,s.jsx)(j,{question:r,answer:t,onAnswerChange:i});case"short_answer":return(0,s.jsx)(v,{question:r,answer:t,onAnswerChange:i});case"matching":return(0,s.jsx)(y,{question:r,answer:t,onAnswerChange:i,locale:o});case"fill_in_the_blank":return(0,s.jsx)(b,{question:r,answer:t,onAnswerChange:i,locale:o});case"essay":return(0,s.jsx)(w,{question:r,answer:t,onAnswerChange:i});default:return(0,s.jsxs)("div",{className:"p-4 bg-yellow-50 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-50 rounded-md",children:["Unsupported question type: ",r.type]})}})()}),0===d.length?null:(0,s.jsxs)("div",{className:"mt-4 p-4 bg-muted rounded-md",children:[(0,s.jsx)("h4",{className:"font-medium mb-2",children:"Hints:"}),(0,s.jsx)("ul",{className:"list-disc pl-5 space-y-1",children:d.map((e,r)=>(0,s.jsx)("li",{children:e},r))})]}),l&&r.feedback_incorrect?(0,s.jsx)("div",{className:"mt-4 p-4 bg-red-50 text-red-800 dark:bg-red-900 dark:text-red-50 rounded-md",children:(0,s.jsx)(h.oz,{children:r.feedback_incorrect})}):null]})},N=e=>{let{quiz:r,onComplete:t}=e,[i,o]=(0,n.useState)(0),[l,d]=(0,n.useState)([]),[c,u]=(0,n.useState)({}),[h,g]=(0,n.useState)(0),[j,v]=(0,n.useState)(!1),[y,b]=(0,n.useState)(null);(0,n.useEffect)(()=>{d(function(e){if(!e.quiz.question_pools||!e.quiz.selection_rules||0===e.quiz.question_pools.length)return e.quiz.questions||[];let r=[];return e.quiz.selection_rules.forEach(t=>{var s;let n=null===(s=e.quiz.question_pools)||void 0===s?void 0:s.find(e=>e.pool_id===t.pool_id);if(n&&n.questions.length>0){let e=[...n.questions];t.randomize&&(e=m(e));let s=Math.min(t.select_count,e.length),i=e.slice(0,s);t.shuffle_order&&m(i),r.push(...i)}}),e.quiz.questions&&e.quiz.questions.length>0&&r.push(...e.quiz.questions),r}(r)),r.quiz.metadata.time_limit_minutes&&b(60*r.quiz.metadata.time_limit_minutes)},[r]),(0,n.useEffect)(()=>{if(null===y||y<=0||j)return;let e=setInterval(()=>{b(r=>null===r||r<=1?(clearInterval(e),1===r&&N(),0):r-1)},1e3);return()=>clearInterval(e)},[y,j,N]);let w=(e,r)=>{u(t=>({...t,[e]:r}))},N=(0,n.useCallback)(()=>{let e=0;l.forEach(r=>{let t=c[r.question_id];if(t){if("true_false"===r.type&&t===r.correct_answer)e+=r.points;else if("multiple_choice"===r.type&&!0===r.single_correct_answer){let s=r.options.find(e=>e.is_correct);s&&t===s.id&&(e+=r.points)}else"short_answer"===r.type&&r.correct_answers.includes(t)&&(e+=r.points)}}),g(e),v(!0),t&&t(e,c)},[l,c,t]);if(0===l.length)return(0,s.jsx)("div",{children:"Loading quiz..."});let z=l[i],q=r.quiz.metadata,k=q.locale||"en-US";if(j){let e=(0,f.$4)(h,l.reduce((e,r)=>e+r.points,0)),r=!q.passing_score_percentage||e>=q.passing_score_percentage;return(0,s.jsxs)(x.Zp,{className:"w-full max-w-4xl mx-auto",children:[(0,s.jsxs)(x.aR,{children:[(0,s.jsx)(x.ZB,{children:"Quiz Results"}),(0,s.jsx)(x.BT,{children:a(q.title,k)})]}),(0,s.jsx)(x.Wu,{children:(0,s.jsx)("div",{className:"space-y-4",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h3",{className:"text-2xl font-bold",children:r?"Congratulations!":"Quiz Completed"}),(0,s.jsxs)("p",{className:"text-xl mt-2",children:["Your score: ",h," / ",l.reduce((e,r)=>e+r.points,0)," (",e,"%)"]}),q.passing_score_percentage&&(0,s.jsx)("p",{className:"mt-2",children:r?"You passed the quiz!":"You need ".concat(q.passing_score_percentage,"% to pass the quiz.")})]})})}),(0,s.jsx)(x.wL,{className:"justify-center",children:(0,s.jsx)(p.$,{onClick:()=>window.location.reload(),children:"Take Another Quiz"})})]})}return(0,s.jsxs)(x.Zp,{className:"w-full max-w-4xl mx-auto",children:[(0,s.jsx)(x.aR,{children:(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(x.ZB,{children:a(q.title,k)}),(0,s.jsxs)(x.BT,{children:["Question ",i+1," of ",l.length]})]}),null!==y&&(0,s.jsxs)("div",{className:"text-right",children:[(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Time Remaining"}),(0,s.jsx)("p",{className:"text-xl font-semibold",children:(e=>{let r=Math.floor(e/60);return"".concat(r,":").concat((e%60).toString().padStart(2,"0"))})(y)})]})]})}),(0,s.jsx)(x.Wu,{children:(0,s.jsx)(_,{question:z,answer:c[z.question_id],onAnswerChange:e=>w(z.question_id,e),locale:k})}),(0,s.jsxs)(x.wL,{className:"flex justify-between",children:[(0,s.jsx)(p.$,{variant:"outline",onClick:()=>{i>0&&o(e=>e-1)},disabled:0===i,children:"Previous"}),(0,s.jsx)("div",{className:"flex gap-2",children:i<l.length-1?(0,s.jsx)(p.$,{onClick:()=>{i<l.length-1&&o(e=>e+1)},children:"Next"}):(0,s.jsx)(p.$,{onClick:N,children:"Submit Quiz"})})]})]})}},3312:(e,r,t)=>{t.d(r,{$:()=>d});var s=t(5155),n=t(2115),i=t(2317),a=t(1027),o=t(1684);let l=(0,a.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=n.forwardRef((e,r)=>{let{className:t,variant:n,size:a,asChild:d=!1,...c}=e,u=d?i.DX:"button";return(0,s.jsx)(u,{className:(0,o.cn)(l({variant:n,size:a,className:t})),ref:r,...c})});d.displayName="Button"},9749:(e,r,t)=>{t.d(r,{BT:()=>d,Wu:()=>c,ZB:()=>l,Zp:()=>a,aR:()=>o,wL:()=>u});var s=t(5155),n=t(2115),i=t(1684);let a=n.forwardRef((e,r)=>{let{className:t,...n}=e;return(0,s.jsx)("div",{ref:r,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...n})});a.displayName="Card";let o=n.forwardRef((e,r)=>{let{className:t,...n}=e;return(0,s.jsx)("div",{ref:r,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",t),...n})});o.displayName="CardHeader";let l=n.forwardRef((e,r)=>{let{className:t,...n}=e;return(0,s.jsx)("h3",{ref:r,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",t),...n})});l.displayName="CardTitle";let d=n.forwardRef((e,r)=>{let{className:t,...n}=e;return(0,s.jsx)("p",{ref:r,className:(0,i.cn)("text-sm text-muted-foreground",t),...n})});d.displayName="CardDescription";let c=n.forwardRef((e,r)=>{let{className:t,...n}=e;return(0,s.jsx)("div",{ref:r,className:(0,i.cn)("p-6 pt-0",t),...n})});c.displayName="CardContent";let u=n.forwardRef((e,r)=>{let{className:t,...n}=e;return(0,s.jsx)("div",{ref:r,className:(0,i.cn)("flex items-center p-6 pt-0",t),...n})});u.displayName="CardFooter"},1684:(e,r,t)=>{t.d(r,{$4:()=>l,Yq:()=>o,cn:()=>i,lk:()=>a});var s=t(3463),n=t(9795);function i(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,n.QP)((0,s.$)(r))}function a(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){let r=16*Math.random()|0;return("x"===e?r:3&r|8).toString(16)})}function o(e){return("string"==typeof e?new Date(e):e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})}function l(e,r){return 0===r?0:Math.round(e/r*100)}}}]);