(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[434],{9721:e=>{"use strict";var t=Object.prototype.hasOwnProperty,n=Object.prototype.toString,r=Object.defineProperty,i=Object.getOwnPropertyDescriptor,a=function(e){return"function"==typeof Array.isArray?Array.isArray(e):"[object Array]"===n.call(e)},l=function(e){if(!e||"[object Object]"!==n.call(e))return!1;var r,i=t.call(e,"constructor"),a=e.constructor&&e.constructor.prototype&&t.call(e.constructor.prototype,"isPrototypeOf");if(e.constructor&&!i&&!a)return!1;for(r in e);return void 0===r||t.call(e,r)},o=function(e,t){r&&"__proto__"===t.name?r(e,t.name,{enumerable:!0,configurable:!0,value:t.newValue,writable:!0}):e[t.name]=t.newValue},s=function(e,n){if("__proto__"===n){if(!t.call(e,n))return;if(i)return i(e,n).value}return e[n]};e.exports=function e(){var t,n,r,i,u,c,d=arguments[0],p=1,f=arguments.length,h=!1;for("boolean"==typeof d&&(h=d,d=arguments[1]||{},p=2),(null==d||"object"!=typeof d&&"function"!=typeof d)&&(d={});p<f;++p)if(t=arguments[p],null!=t)for(n in t)r=s(d,n),d!==(i=s(t,n))&&(h&&i&&(l(i)||(u=a(i)))?(u?(u=!1,c=r&&a(r)?r:[]):c=r&&l(r)?r:{},o(d,{name:n,newValue:e(h,c,i)})):void 0!==i&&o(d,{name:n,newValue:i}));return d}},466:e=>{var t=/\/\*[^*]*\*+([^/*][^*]*\*+)*\//g,n=/\n/g,r=/^\s*/,i=/^(\*?[-#/*\\\w]+(\[[0-9a-z_-]+\])?)\s*/,a=/^:\s*/,l=/^((?:'(?:\\'|.)*?'|"(?:\\"|.)*?"|\([^)]*?\)|[^};])+)/,o=/^[;\s]*/,s=/^\s+|\s+$/g;function u(e){return e?e.replace(s,""):""}e.exports=function(e,s){if("string"!=typeof e)throw TypeError("First argument must be a string");if(!e)return[];s=s||{};var c=1,d=1;function p(e){var t=e.match(n);t&&(c+=t.length);var r=e.lastIndexOf("\n");d=~r?e.length-r:d+e.length}function f(){var e={line:c,column:d};return function(t){return t.position=new h(e),y(r),t}}function h(e){this.start=e,this.end={line:c,column:d},this.source=s.source}h.prototype.content=e;var m=[];function g(t){var n=Error(s.source+":"+c+":"+d+": "+t);if(n.reason=t,n.filename=s.source,n.line=c,n.column=d,n.source=e,s.silent)m.push(n);else throw n}function y(t){var n=t.exec(e);if(n){var r=n[0];return p(r),e=e.slice(r.length),n}}function v(e){var t;for(e=e||[];t=x();)!1!==t&&e.push(t);return e}function x(){var t=f();if("/"==e.charAt(0)&&"*"==e.charAt(1)){for(var n=2;""!=e.charAt(n)&&("*"!=e.charAt(n)||"/"!=e.charAt(n+1));)++n;if(n+=2,""===e.charAt(n-1))return g("End of comment missing");var r=e.slice(2,n-2);return d+=2,p(r),e=e.slice(n),d+=2,t({type:"comment",comment:r})}}return y(r),function(){var e,n=[];for(v(n);e=function(){var e=f(),n=y(i);if(n){if(x(),!y(a))return g("property missing ':'");var r=y(l),s=e({type:"declaration",property:u(n[0].replace(t,"")),value:r?u(r[0].replace(t,"")):""});return y(o),s}}();)!1!==e&&(n.push(e),v(n));return n}()}},8591:function(e,t,n){"use strict";var r=(this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}})(n(4919)),i=n(6643);function a(e,t){var n={};return e&&"string"==typeof e&&(0,r.default)(e,function(e,r){e&&r&&(n[(0,i.camelCase)(e,t)]=r)}),n}a.default=a,e.exports=a},6643:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.camelCase=void 0;var n=/^--[a-zA-Z0-9_-]+$/,r=/-([a-z])/g,i=/^[^-]+$/,a=/^-(webkit|moz|ms|o|khtml)-/,l=/^-(ms)-/,o=function(e,t){return t.toUpperCase()},s=function(e,t){return"".concat(t,"-")};t.camelCase=function(e,t){var u;return(void 0===t&&(t={}),!(u=e)||i.test(u)||n.test(u))?e:(e=e.toLowerCase(),(e=t.reactCompat?e.replace(l,s):e.replace(a,s)).replace(r,o))}},4919:function(e,t,n){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){var n=null;if(!e||"string"!=typeof e)return n;var r=(0,i.default)(e),a="function"==typeof t;return r.forEach(function(e){if("declaration"===e.type){var r=e.property,i=e.value;a?t(r,i,e):i&&((n=n||{})[r]=i)}}),n};var i=r(n(466))},8068:(e,t,n)=>{"use strict";n.d(t,{s:()=>l,t:()=>a});var r=n(2115);function i(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function a(...e){return t=>{let n=!1,r=e.map(e=>{let r=i(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():i(e[t],null)}}}}function l(...e){return r.useCallback(a(...e),e)}},2317:(e,t,n)=>{"use strict";n.d(t,{DX:()=>o,TL:()=>l});var r=n(2115),i=n(8068),a=n(5155);function l(e){let t=function(e){let t=r.forwardRef((e,t)=>{let{children:n,...a}=e;if(r.isValidElement(n)){let e,l;let o=(e=Object.getOwnPropertyDescriptor(n.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?n.ref:(e=Object.getOwnPropertyDescriptor(n,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?n.props.ref:n.props.ref||n.ref,s=function(e,t){let n={...t};for(let r in t){let i=e[r],a=t[r];/^on[A-Z]/.test(r)?i&&a?n[r]=(...e)=>{let t=a(...e);return i(...e),t}:i&&(n[r]=i):"style"===r?n[r]={...i,...a}:"className"===r&&(n[r]=[i,a].filter(Boolean).join(" "))}return{...e,...n}}(a,n.props);return n.type!==r.Fragment&&(s.ref=t?(0,i.t)(t,o):o),r.cloneElement(n,s)}return r.Children.count(n)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=r.forwardRef((e,n)=>{let{children:i,...l}=e,o=r.Children.toArray(i),s=o.find(u);if(s){let e=s.props.children,i=o.map(t=>t!==s?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,a.jsx)(t,{...l,ref:n,children:r.isValidElement(e)?r.cloneElement(e,void 0,i):null})}return(0,a.jsx)(t,{...l,ref:n,children:i})});return n.displayName=`${e}.Slot`,n}var o=l("Slot"),s=Symbol("radix.slottable");function u(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===s}},1027:(e,t,n)=>{"use strict";n.d(t,{F:()=>l});var r=n(3463);let i=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,a=r.$,l=(e,t)=>n=>{var r;if((null==t?void 0:t.variants)==null)return a(e,null==n?void 0:n.class,null==n?void 0:n.className);let{variants:l,defaultVariants:o}=t,s=Object.keys(l).map(e=>{let t=null==n?void 0:n[e],r=null==o?void 0:o[e];if(null===t)return null;let a=i(t)||i(r);return l[e][a]}),u=n&&Object.entries(n).reduce((e,t)=>{let[n,r]=t;return void 0===r||(e[n]=r),e},{});return a(e,s,null==t?void 0:null===(r=t.compoundVariants)||void 0===r?void 0:r.reduce((e,t)=>{let{class:n,className:r,...i}=t;return Object.entries(i).every(e=>{let[t,n]=e;return Array.isArray(n)?n.includes({...o,...u}[t]):({...o,...u})[t]===n})?[...e,n,r]:e},[]),null==n?void 0:n.class,null==n?void 0:n.className)}},3628:(e,t,n)=>{"use strict";n.d(t,{oz:()=>nO});var r={};n.r(r),n.d(r,{boolean:()=>g,booleanish:()=>y,commaOrSpaceSeparated:()=>b,commaSeparated:()=>_,number:()=>x,overloadedBoolean:()=>v,spaceSeparated:()=>k});var i={};n.r(i),n.d(i,{attentionMarkers:()=>tE,contentInitial:()=>t_,disable:()=>tI,document:()=>tk,flow:()=>tw,flowInitial:()=>tb,insideSpan:()=>tC,string:()=>tS,text:()=>tT});let a=/^[$_\p{ID_Start}][$_\u{200C}\u{200D}\p{ID_Continue}]*$/u,l=/^[$_\p{ID_Start}][-$_\u{200C}\u{200D}\p{ID_Continue}]*$/u,o={};function s(e,t){return((t||o).jsx?l:a).test(e)}let u=/[ \t\n\f\r]/g;function c(e){return""===e.replace(u,"")}class d{constructor(e,t,n){this.normal=t,this.property=e,n&&(this.space=n)}}function p(e,t){let n={},r={};for(let t of e)Object.assign(n,t.property),Object.assign(r,t.normal);return new d(n,r,t)}function f(e){return e.toLowerCase()}d.prototype.normal={},d.prototype.property={},d.prototype.space=void 0;class h{constructor(e,t){this.attribute=t,this.property=e}}h.prototype.attribute="",h.prototype.booleanish=!1,h.prototype.boolean=!1,h.prototype.commaOrSpaceSeparated=!1,h.prototype.commaSeparated=!1,h.prototype.defined=!1,h.prototype.mustUseProperty=!1,h.prototype.number=!1,h.prototype.overloadedBoolean=!1,h.prototype.property="",h.prototype.spaceSeparated=!1,h.prototype.space=void 0;let m=0,g=w(),y=w(),v=w(),x=w(),k=w(),_=w(),b=w();function w(){return 2**++m}let S=Object.keys(r);class T extends h{constructor(e,t,n,i){let a=-1;if(super(e,t),function(e,t,n){n&&(e[t]=n)}(this,"space",i),"number"==typeof n)for(;++a<S.length;){let e=S[a];!function(e,t,n){n&&(e[t]=n)}(this,S[a],(n&r[e])===r[e])}}}function C(e){let t={},n={};for(let[r,i]of Object.entries(e.properties)){let a=new T(r,e.transform(e.attributes||{},r),i,e.space);e.mustUseProperty&&e.mustUseProperty.includes(r)&&(a.mustUseProperty=!0),t[r]=a,n[f(r)]=r,n[f(a.attribute)]=r}return new d(t,n,e.space)}T.prototype.defined=!0;let E=C({properties:{ariaActiveDescendant:null,ariaAtomic:y,ariaAutoComplete:null,ariaBusy:y,ariaChecked:y,ariaColCount:x,ariaColIndex:x,ariaColSpan:x,ariaControls:k,ariaCurrent:null,ariaDescribedBy:k,ariaDetails:null,ariaDisabled:y,ariaDropEffect:k,ariaErrorMessage:null,ariaExpanded:y,ariaFlowTo:k,ariaGrabbed:y,ariaHasPopup:null,ariaHidden:y,ariaInvalid:null,ariaKeyShortcuts:null,ariaLabel:null,ariaLabelledBy:k,ariaLevel:x,ariaLive:null,ariaModal:y,ariaMultiLine:y,ariaMultiSelectable:y,ariaOrientation:null,ariaOwns:k,ariaPlaceholder:null,ariaPosInSet:x,ariaPressed:y,ariaReadOnly:y,ariaRelevant:null,ariaRequired:y,ariaRoleDescription:k,ariaRowCount:x,ariaRowIndex:x,ariaRowSpan:x,ariaSelected:y,ariaSetSize:x,ariaSort:null,ariaValueMax:x,ariaValueMin:x,ariaValueNow:x,ariaValueText:null,role:null},transform:(e,t)=>"role"===t?t:"aria-"+t.slice(4).toLowerCase()});function I(e,t){return t in e?e[t]:t}function A(e,t){return I(e,t.toLowerCase())}let O=C({attributes:{acceptcharset:"accept-charset",classname:"class",htmlfor:"for",httpequiv:"http-equiv"},mustUseProperty:["checked","multiple","muted","selected"],properties:{abbr:null,accept:_,acceptCharset:k,accessKey:k,action:null,allow:null,allowFullScreen:g,allowPaymentRequest:g,allowUserMedia:g,alt:null,as:null,async:g,autoCapitalize:null,autoComplete:k,autoFocus:g,autoPlay:g,blocking:k,capture:null,charSet:null,checked:g,cite:null,className:k,cols:x,colSpan:null,content:null,contentEditable:y,controls:g,controlsList:k,coords:x|_,crossOrigin:null,data:null,dateTime:null,decoding:null,default:g,defer:g,dir:null,dirName:null,disabled:g,download:v,draggable:y,encType:null,enterKeyHint:null,fetchPriority:null,form:null,formAction:null,formEncType:null,formMethod:null,formNoValidate:g,formTarget:null,headers:k,height:x,hidden:v,high:x,href:null,hrefLang:null,htmlFor:k,httpEquiv:k,id:null,imageSizes:null,imageSrcSet:null,inert:g,inputMode:null,integrity:null,is:null,isMap:g,itemId:null,itemProp:k,itemRef:k,itemScope:g,itemType:k,kind:null,label:null,lang:null,language:null,list:null,loading:null,loop:g,low:x,manifest:null,max:null,maxLength:x,media:null,method:null,min:null,minLength:x,multiple:g,muted:g,name:null,nonce:null,noModule:g,noValidate:g,onAbort:null,onAfterPrint:null,onAuxClick:null,onBeforeMatch:null,onBeforePrint:null,onBeforeToggle:null,onBeforeUnload:null,onBlur:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onContextLost:null,onContextMenu:null,onContextRestored:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnded:null,onError:null,onFocus:null,onFormData:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLanguageChange:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadEnd:null,onLoadStart:null,onMessage:null,onMessageError:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRejectionHandled:null,onReset:null,onResize:null,onScroll:null,onScrollEnd:null,onSecurityPolicyViolation:null,onSeeked:null,onSeeking:null,onSelect:null,onSlotChange:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnhandledRejection:null,onUnload:null,onVolumeChange:null,onWaiting:null,onWheel:null,open:g,optimum:x,pattern:null,ping:k,placeholder:null,playsInline:g,popover:null,popoverTarget:null,popoverTargetAction:null,poster:null,preload:null,readOnly:g,referrerPolicy:null,rel:k,required:g,reversed:g,rows:x,rowSpan:x,sandbox:k,scope:null,scoped:g,seamless:g,selected:g,shadowRootClonable:g,shadowRootDelegatesFocus:g,shadowRootMode:null,shape:null,size:x,sizes:null,slot:null,span:x,spellCheck:y,src:null,srcDoc:null,srcLang:null,srcSet:null,start:x,step:null,style:null,tabIndex:x,target:null,title:null,translate:null,type:null,typeMustMatch:g,useMap:null,value:y,width:x,wrap:null,writingSuggestions:null,align:null,aLink:null,archive:k,axis:null,background:null,bgColor:null,border:x,borderColor:null,bottomMargin:x,cellPadding:null,cellSpacing:null,char:null,charOff:null,classId:null,clear:null,code:null,codeBase:null,codeType:null,color:null,compact:g,declare:g,event:null,face:null,frame:null,frameBorder:null,hSpace:x,leftMargin:x,link:null,longDesc:null,lowSrc:null,marginHeight:x,marginWidth:x,noResize:g,noHref:g,noShade:g,noWrap:g,object:null,profile:null,prompt:null,rev:null,rightMargin:x,rules:null,scheme:null,scrolling:y,standby:null,summary:null,text:null,topMargin:x,valueType:null,version:null,vAlign:null,vLink:null,vSpace:x,allowTransparency:null,autoCorrect:null,autoSave:null,disablePictureInPicture:g,disableRemotePlayback:g,prefix:null,property:null,results:x,security:null,unselectable:null},space:"html",transform:A}),P=C({attributes:{accentHeight:"accent-height",alignmentBaseline:"alignment-baseline",arabicForm:"arabic-form",baselineShift:"baseline-shift",capHeight:"cap-height",className:"class",clipPath:"clip-path",clipRule:"clip-rule",colorInterpolation:"color-interpolation",colorInterpolationFilters:"color-interpolation-filters",colorProfile:"color-profile",colorRendering:"color-rendering",crossOrigin:"crossorigin",dataType:"datatype",dominantBaseline:"dominant-baseline",enableBackground:"enable-background",fillOpacity:"fill-opacity",fillRule:"fill-rule",floodColor:"flood-color",floodOpacity:"flood-opacity",fontFamily:"font-family",fontSize:"font-size",fontSizeAdjust:"font-size-adjust",fontStretch:"font-stretch",fontStyle:"font-style",fontVariant:"font-variant",fontWeight:"font-weight",glyphName:"glyph-name",glyphOrientationHorizontal:"glyph-orientation-horizontal",glyphOrientationVertical:"glyph-orientation-vertical",hrefLang:"hreflang",horizAdvX:"horiz-adv-x",horizOriginX:"horiz-origin-x",horizOriginY:"horiz-origin-y",imageRendering:"image-rendering",letterSpacing:"letter-spacing",lightingColor:"lighting-color",markerEnd:"marker-end",markerMid:"marker-mid",markerStart:"marker-start",navDown:"nav-down",navDownLeft:"nav-down-left",navDownRight:"nav-down-right",navLeft:"nav-left",navNext:"nav-next",navPrev:"nav-prev",navRight:"nav-right",navUp:"nav-up",navUpLeft:"nav-up-left",navUpRight:"nav-up-right",onAbort:"onabort",onActivate:"onactivate",onAfterPrint:"onafterprint",onBeforePrint:"onbeforeprint",onBegin:"onbegin",onCancel:"oncancel",onCanPlay:"oncanplay",onCanPlayThrough:"oncanplaythrough",onChange:"onchange",onClick:"onclick",onClose:"onclose",onCopy:"oncopy",onCueChange:"oncuechange",onCut:"oncut",onDblClick:"ondblclick",onDrag:"ondrag",onDragEnd:"ondragend",onDragEnter:"ondragenter",onDragExit:"ondragexit",onDragLeave:"ondragleave",onDragOver:"ondragover",onDragStart:"ondragstart",onDrop:"ondrop",onDurationChange:"ondurationchange",onEmptied:"onemptied",onEnd:"onend",onEnded:"onended",onError:"onerror",onFocus:"onfocus",onFocusIn:"onfocusin",onFocusOut:"onfocusout",onHashChange:"onhashchange",onInput:"oninput",onInvalid:"oninvalid",onKeyDown:"onkeydown",onKeyPress:"onkeypress",onKeyUp:"onkeyup",onLoad:"onload",onLoadedData:"onloadeddata",onLoadedMetadata:"onloadedmetadata",onLoadStart:"onloadstart",onMessage:"onmessage",onMouseDown:"onmousedown",onMouseEnter:"onmouseenter",onMouseLeave:"onmouseleave",onMouseMove:"onmousemove",onMouseOut:"onmouseout",onMouseOver:"onmouseover",onMouseUp:"onmouseup",onMouseWheel:"onmousewheel",onOffline:"onoffline",onOnline:"ononline",onPageHide:"onpagehide",onPageShow:"onpageshow",onPaste:"onpaste",onPause:"onpause",onPlay:"onplay",onPlaying:"onplaying",onPopState:"onpopstate",onProgress:"onprogress",onRateChange:"onratechange",onRepeat:"onrepeat",onReset:"onreset",onResize:"onresize",onScroll:"onscroll",onSeeked:"onseeked",onSeeking:"onseeking",onSelect:"onselect",onShow:"onshow",onStalled:"onstalled",onStorage:"onstorage",onSubmit:"onsubmit",onSuspend:"onsuspend",onTimeUpdate:"ontimeupdate",onToggle:"ontoggle",onUnload:"onunload",onVolumeChange:"onvolumechange",onWaiting:"onwaiting",onZoom:"onzoom",overlinePosition:"overline-position",overlineThickness:"overline-thickness",paintOrder:"paint-order",panose1:"panose-1",pointerEvents:"pointer-events",referrerPolicy:"referrerpolicy",renderingIntent:"rendering-intent",shapeRendering:"shape-rendering",stopColor:"stop-color",stopOpacity:"stop-opacity",strikethroughPosition:"strikethrough-position",strikethroughThickness:"strikethrough-thickness",strokeDashArray:"stroke-dasharray",strokeDashOffset:"stroke-dashoffset",strokeLineCap:"stroke-linecap",strokeLineJoin:"stroke-linejoin",strokeMiterLimit:"stroke-miterlimit",strokeOpacity:"stroke-opacity",strokeWidth:"stroke-width",tabIndex:"tabindex",textAnchor:"text-anchor",textDecoration:"text-decoration",textRendering:"text-rendering",transformOrigin:"transform-origin",typeOf:"typeof",underlinePosition:"underline-position",underlineThickness:"underline-thickness",unicodeBidi:"unicode-bidi",unicodeRange:"unicode-range",unitsPerEm:"units-per-em",vAlphabetic:"v-alphabetic",vHanging:"v-hanging",vIdeographic:"v-ideographic",vMathematical:"v-mathematical",vectorEffect:"vector-effect",vertAdvY:"vert-adv-y",vertOriginX:"vert-origin-x",vertOriginY:"vert-origin-y",wordSpacing:"word-spacing",writingMode:"writing-mode",xHeight:"x-height",playbackOrder:"playbackorder",timelineBegin:"timelinebegin"},properties:{about:b,accentHeight:x,accumulate:null,additive:null,alignmentBaseline:null,alphabetic:x,amplitude:x,arabicForm:null,ascent:x,attributeName:null,attributeType:null,azimuth:x,bandwidth:null,baselineShift:null,baseFrequency:null,baseProfile:null,bbox:null,begin:null,bias:x,by:null,calcMode:null,capHeight:x,className:k,clip:null,clipPath:null,clipPathUnits:null,clipRule:null,color:null,colorInterpolation:null,colorInterpolationFilters:null,colorProfile:null,colorRendering:null,content:null,contentScriptType:null,contentStyleType:null,crossOrigin:null,cursor:null,cx:null,cy:null,d:null,dataType:null,defaultAction:null,descent:x,diffuseConstant:x,direction:null,display:null,dur:null,divisor:x,dominantBaseline:null,download:g,dx:null,dy:null,edgeMode:null,editable:null,elevation:x,enableBackground:null,end:null,event:null,exponent:x,externalResourcesRequired:null,fill:null,fillOpacity:x,fillRule:null,filter:null,filterRes:null,filterUnits:null,floodColor:null,floodOpacity:null,focusable:null,focusHighlight:null,fontFamily:null,fontSize:null,fontSizeAdjust:null,fontStretch:null,fontStyle:null,fontVariant:null,fontWeight:null,format:null,fr:null,from:null,fx:null,fy:null,g1:_,g2:_,glyphName:_,glyphOrientationHorizontal:null,glyphOrientationVertical:null,glyphRef:null,gradientTransform:null,gradientUnits:null,handler:null,hanging:x,hatchContentUnits:null,hatchUnits:null,height:null,href:null,hrefLang:null,horizAdvX:x,horizOriginX:x,horizOriginY:x,id:null,ideographic:x,imageRendering:null,initialVisibility:null,in:null,in2:null,intercept:x,k:x,k1:x,k2:x,k3:x,k4:x,kernelMatrix:b,kernelUnitLength:null,keyPoints:null,keySplines:null,keyTimes:null,kerning:null,lang:null,lengthAdjust:null,letterSpacing:null,lightingColor:null,limitingConeAngle:x,local:null,markerEnd:null,markerMid:null,markerStart:null,markerHeight:null,markerUnits:null,markerWidth:null,mask:null,maskContentUnits:null,maskUnits:null,mathematical:null,max:null,media:null,mediaCharacterEncoding:null,mediaContentEncodings:null,mediaSize:x,mediaTime:null,method:null,min:null,mode:null,name:null,navDown:null,navDownLeft:null,navDownRight:null,navLeft:null,navNext:null,navPrev:null,navRight:null,navUp:null,navUpLeft:null,navUpRight:null,numOctaves:null,observer:null,offset:null,onAbort:null,onActivate:null,onAfterPrint:null,onBeforePrint:null,onBegin:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnd:null,onEnded:null,onError:null,onFocus:null,onFocusIn:null,onFocusOut:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadStart:null,onMessage:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onMouseWheel:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRepeat:null,onReset:null,onResize:null,onScroll:null,onSeeked:null,onSeeking:null,onSelect:null,onShow:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnload:null,onVolumeChange:null,onWaiting:null,onZoom:null,opacity:null,operator:null,order:null,orient:null,orientation:null,origin:null,overflow:null,overlay:null,overlinePosition:x,overlineThickness:x,paintOrder:null,panose1:null,path:null,pathLength:x,patternContentUnits:null,patternTransform:null,patternUnits:null,phase:null,ping:k,pitch:null,playbackOrder:null,pointerEvents:null,points:null,pointsAtX:x,pointsAtY:x,pointsAtZ:x,preserveAlpha:null,preserveAspectRatio:null,primitiveUnits:null,propagate:null,property:b,r:null,radius:null,referrerPolicy:null,refX:null,refY:null,rel:b,rev:b,renderingIntent:null,repeatCount:null,repeatDur:null,requiredExtensions:b,requiredFeatures:b,requiredFonts:b,requiredFormats:b,resource:null,restart:null,result:null,rotate:null,rx:null,ry:null,scale:null,seed:null,shapeRendering:null,side:null,slope:null,snapshotTime:null,specularConstant:x,specularExponent:x,spreadMethod:null,spacing:null,startOffset:null,stdDeviation:null,stemh:null,stemv:null,stitchTiles:null,stopColor:null,stopOpacity:null,strikethroughPosition:x,strikethroughThickness:x,string:null,stroke:null,strokeDashArray:b,strokeDashOffset:null,strokeLineCap:null,strokeLineJoin:null,strokeMiterLimit:x,strokeOpacity:x,strokeWidth:null,style:null,surfaceScale:x,syncBehavior:null,syncBehaviorDefault:null,syncMaster:null,syncTolerance:null,syncToleranceDefault:null,systemLanguage:b,tabIndex:x,tableValues:null,target:null,targetX:x,targetY:x,textAnchor:null,textDecoration:null,textRendering:null,textLength:null,timelineBegin:null,title:null,transformBehavior:null,type:null,typeOf:b,to:null,transform:null,transformOrigin:null,u1:null,u2:null,underlinePosition:x,underlineThickness:x,unicode:null,unicodeBidi:null,unicodeRange:null,unitsPerEm:x,values:null,vAlphabetic:x,vMathematical:x,vectorEffect:null,vHanging:x,vIdeographic:x,version:null,vertAdvY:x,vertOriginX:x,vertOriginY:x,viewBox:null,viewTarget:null,visibility:null,width:null,widths:null,wordSpacing:null,writingMode:null,x:null,x1:null,x2:null,xChannelSelector:null,xHeight:x,y:null,y1:null,y2:null,yChannelSelector:null,z:null,zoomAndPan:null},space:"svg",transform:I}),N=C({properties:{xLinkActuate:null,xLinkArcRole:null,xLinkHref:null,xLinkRole:null,xLinkShow:null,xLinkTitle:null,xLinkType:null},space:"xlink",transform:(e,t)=>"xlink:"+t.slice(5).toLowerCase()}),L=C({attributes:{xmlnsxlink:"xmlns:xlink"},properties:{xmlnsXLink:null,xmlns:null},space:"xmlns",transform:A}),D=C({properties:{xmlBase:null,xmlLang:null,xmlSpace:null},space:"xml",transform:(e,t)=>"xml:"+t.slice(3).toLowerCase()}),R=p([E,O,N,L,D],"html"),j=p([E,P,N,L,D],"svg"),M=/[A-Z]/g,z=/-[a-z]/g,F=/^data[-\w.:]+$/i;function Z(e){return"-"+e.toLowerCase()}function B(e){return e.charAt(1).toUpperCase()}let V={classId:"classID",dataType:"datatype",itemId:"itemID",strokeDashArray:"strokeDasharray",strokeDashOffset:"strokeDashoffset",strokeLineCap:"strokeLinecap",strokeLineJoin:"strokeLinejoin",strokeMiterLimit:"strokeMiterlimit",typeOf:"typeof",xLinkActuate:"xlinkActuate",xLinkArcRole:"xlinkArcrole",xLinkHref:"xlinkHref",xLinkRole:"xlinkRole",xLinkShow:"xlinkShow",xLinkTitle:"xlinkTitle",xLinkType:"xlinkType",xmlnsXLink:"xmlnsXlink"};var U=n(8591);let $=q("end"),H=q("start");function q(e){return function(t){let n=t&&t.position&&t.position[e]||{};if("number"==typeof n.line&&n.line>0&&"number"==typeof n.column&&n.column>0)return{line:n.line,column:n.column,offset:"number"==typeof n.offset&&n.offset>-1?n.offset:void 0}}}function W(e){return e&&"object"==typeof e?"position"in e||"type"in e?Y(e.position):"start"in e||"end"in e?Y(e):"line"in e||"column"in e?K(e):"":""}function K(e){return J(e&&e.line)+":"+J(e&&e.column)}function Y(e){return K(e&&e.start)+"-"+K(e&&e.end)}function J(e){return e&&"number"==typeof e?e:1}class Q extends Error{constructor(e,t,n){super(),"string"==typeof t&&(n=t,t=void 0);let r="",i={},a=!1;if(t&&(i="line"in t&&"column"in t?{place:t}:"start"in t&&"end"in t?{place:t}:"type"in t?{ancestors:[t],place:t.position}:{...t}),"string"==typeof e?r=e:!i.cause&&e&&(a=!0,r=e.message,i.cause=e),!i.ruleId&&!i.source&&"string"==typeof n){let e=n.indexOf(":");-1===e?i.ruleId=n:(i.source=n.slice(0,e),i.ruleId=n.slice(e+1))}if(!i.place&&i.ancestors&&i.ancestors){let e=i.ancestors[i.ancestors.length-1];e&&(i.place=e.position)}let l=i.place&&"start"in i.place?i.place.start:i.place;this.ancestors=i.ancestors||void 0,this.cause=i.cause||void 0,this.column=l?l.column:void 0,this.fatal=void 0,this.file,this.message=r,this.line=l?l.line:void 0,this.name=W(i.place)||"1:1",this.place=i.place||void 0,this.reason=this.message,this.ruleId=i.ruleId||void 0,this.source=i.source||void 0,this.stack=a&&i.cause&&"string"==typeof i.cause.stack?i.cause.stack:"",this.actual,this.expected,this.note,this.url}}Q.prototype.file="",Q.prototype.name="",Q.prototype.reason="",Q.prototype.message="",Q.prototype.stack="",Q.prototype.column=void 0,Q.prototype.line=void 0,Q.prototype.ancestors=void 0,Q.prototype.cause=void 0,Q.prototype.fatal=void 0,Q.prototype.place=void 0,Q.prototype.ruleId=void 0,Q.prototype.source=void 0;let X={}.hasOwnProperty,G=new Map,ee=/[A-Z]/g,et=new Set(["table","tbody","thead","tfoot","tr"]),en=new Set(["td","th"]),er="https://github.com/syntax-tree/hast-util-to-jsx-runtime";function ei(e,t,n){return"element"===t.type?function(e,t,n){let r=e.schema,i=r;"svg"===t.tagName.toLowerCase()&&"html"===r.space&&(i=j,e.schema=i),e.ancestors.push(t);let a=es(e,t.tagName,!1),l=function(e,t){let n,r;let i={};for(r in t.properties)if("children"!==r&&X.call(t.properties,r)){let a=function(e,t,n){let r=function(e,t){let n=f(t),r=t,i=h;if(n in e.normal)return e.property[e.normal[n]];if(n.length>4&&"data"===n.slice(0,4)&&F.test(t)){if("-"===t.charAt(4)){let e=t.slice(5).replace(z,B);r="data"+e.charAt(0).toUpperCase()+e.slice(1)}else{let e=t.slice(4);if(!z.test(e)){let n=e.replace(M,Z);"-"!==n.charAt(0)&&(n="-"+n),t="data"+n}}i=T}return new i(r,t)}(e.schema,t);if(!(null==n||"number"==typeof n&&Number.isNaN(n))){if(Array.isArray(n)&&(n=r.commaSeparated?function(e,t){let n={};return(""===e[e.length-1]?[...e,""]:e).join((n.padRight?" ":"")+","+(!1===n.padLeft?"":" ")).trim()}(n):n.join(" ").trim()),"style"===r.property){let t="object"==typeof n?n:function(e,t){try{return U(t,{reactCompat:!0})}catch(n){if(e.ignoreInvalidStyle)return{};let t=new Q("Cannot parse `style` attribute",{ancestors:e.ancestors,cause:n,ruleId:"style",source:"hast-util-to-jsx-runtime"});throw t.file=e.filePath||void 0,t.url=er+"#cannot-parse-style-attribute",t}}(e,String(n));return"css"===e.stylePropertyNameCase&&(t=function(e){let t;let n={};for(t in e)X.call(e,t)&&(n[function(e){let t=e.replace(ee,ec);return"ms-"===t.slice(0,3)&&(t="-"+t),t}(t)]=e[t]);return n}(t)),["style",t]}return["react"===e.elementAttributeNameCase&&r.space?V[r.property]||r.property:r.attribute,n]}}(e,r,t.properties[r]);if(a){let[r,l]=a;e.tableCellAlignToStyle&&"align"===r&&"string"==typeof l&&en.has(t.tagName)?n=l:i[r]=l}}return n&&((i.style||(i.style={}))["css"===e.stylePropertyNameCase?"text-align":"textAlign"]=n),i}(e,t),o=eo(e,t);return et.has(t.tagName)&&(o=o.filter(function(e){return"string"!=typeof e||!("object"==typeof e?"text"===e.type&&c(e.value):c(e))})),ea(e,l,a,t),el(l,o),e.ancestors.pop(),e.schema=r,e.create(t,a,l,n)}(e,t,n):"mdxFlowExpression"===t.type||"mdxTextExpression"===t.type?function(e,t){if(t.data&&t.data.estree&&e.evaluater){let n=t.data.estree.body[0];return n.type,e.evaluater.evaluateExpression(n.expression)}eu(e,t.position)}(e,t):"mdxJsxFlowElement"===t.type||"mdxJsxTextElement"===t.type?function(e,t,n){let r=e.schema,i=r;"svg"===t.name&&"html"===r.space&&(i=j,e.schema=i),e.ancestors.push(t);let a=null===t.name?e.Fragment:es(e,t.name,!0),l=function(e,t){let n={};for(let r of t.attributes)if("mdxJsxExpressionAttribute"===r.type){if(r.data&&r.data.estree&&e.evaluater){let t=r.data.estree.body[0];t.type;let i=t.expression;i.type;let a=i.properties[0];a.type,Object.assign(n,e.evaluater.evaluateExpression(a.argument))}else eu(e,t.position)}else{let i;let a=r.name;if(r.value&&"object"==typeof r.value){if(r.value.data&&r.value.data.estree&&e.evaluater){let t=r.value.data.estree.body[0];t.type,i=e.evaluater.evaluateExpression(t.expression)}else eu(e,t.position)}else i=null===r.value||r.value;n[a]=i}return n}(e,t),o=eo(e,t);return ea(e,l,a,t),el(l,o),e.ancestors.pop(),e.schema=r,e.create(t,a,l,n)}(e,t,n):"mdxjsEsm"===t.type?function(e,t){if(t.data&&t.data.estree&&e.evaluater)return e.evaluater.evaluateProgram(t.data.estree);eu(e,t.position)}(e,t):"root"===t.type?function(e,t,n){let r={};return el(r,eo(e,t)),e.create(t,e.Fragment,r,n)}(e,t,n):"text"===t.type?t.value:void 0}function ea(e,t,n,r){"string"!=typeof n&&n!==e.Fragment&&e.passNode&&(t.node=r)}function el(e,t){if(t.length>0){let n=t.length>1?t:t[0];n&&(e.children=n)}}function eo(e,t){let n=[],r=-1,i=e.passKeys?new Map:G;for(;++r<t.children.length;){let a;let l=t.children[r];if(e.passKeys){let e="element"===l.type?l.tagName:"mdxJsxFlowElement"===l.type||"mdxJsxTextElement"===l.type?l.name:void 0;if(e){let t=i.get(e)||0;a=e+"-"+t,i.set(e,t+1)}}let o=ei(e,l,a);void 0!==o&&n.push(o)}return n}function es(e,t,n){let r;if(n){if(t.includes(".")){let e;let n=t.split("."),i=-1;for(;++i<n.length;){let t=s(n[i])?{type:"Identifier",name:n[i]}:{type:"Literal",value:n[i]};e=e?{type:"MemberExpression",object:e,property:t,computed:!!(i&&"Literal"===t.type),optional:!1}:t}r=e}else r=s(t)&&!/^[a-z]/.test(t)?{type:"Identifier",name:t}:{type:"Literal",value:t}}else r={type:"Literal",value:t};if("Literal"===r.type){let t=r.value;return X.call(e.components,t)?e.components[t]:t}if(e.evaluater)return e.evaluater.evaluateExpression(r);eu(e)}function eu(e,t){let n=new Q("Cannot handle MDX estrees without `createEvaluater`",{ancestors:e.ancestors,place:t,ruleId:"mdx-estree",source:"hast-util-to-jsx-runtime"});throw n.file=e.filePath||void 0,n.url=er+"#cannot-handle-mdx-estrees-without-createevaluater",n}function ec(e){return"-"+e.toLowerCase()}let ed={action:["form"],cite:["blockquote","del","ins","q"],data:["object"],formAction:["button","input"],href:["a","area","base","link"],icon:["menuitem"],itemId:null,manifest:["html"],ping:["a","area"],poster:["video"],src:["audio","embed","iframe","img","input","script","source","track","video"]};var ep=n(5155);n(2115);let ef={};function eh(e,t,n){if(e&&"object"==typeof e){if("value"in e)return"html"!==e.type||n?e.value:"";if(t&&"alt"in e&&e.alt)return e.alt;if("children"in e)return em(e.children,t,n)}return Array.isArray(e)?em(e,t,n):""}function em(e,t,n){let r=[],i=-1;for(;++i<e.length;)r[i]=eh(e[i],t,n);return r.join("")}function eg(e,t,n,r){let i;let a=e.length,l=0;if(t=t<0?-t>a?0:a+t:t>a?a:t,n=n>0?n:0,r.length<1e4)(i=Array.from(r)).unshift(t,n),e.splice(...i);else for(n&&e.splice(t,n);l<r.length;)(i=r.slice(l,l+1e4)).unshift(t,0),e.splice(...i),l+=1e4,t+=1e4}function ey(e,t){return e.length>0?(eg(e,e.length,0,t),e):t}class ev{constructor(e){this.left=e?[...e]:[],this.right=[]}get(e){if(e<0||e>=this.left.length+this.right.length)throw RangeError("Cannot access index `"+e+"` in a splice buffer of size `"+(this.left.length+this.right.length)+"`");return e<this.left.length?this.left[e]:this.right[this.right.length-e+this.left.length-1]}get length(){return this.left.length+this.right.length}shift(){return this.setCursor(0),this.right.pop()}slice(e,t){let n=null==t?Number.POSITIVE_INFINITY:t;return n<this.left.length?this.left.slice(e,n):e>this.left.length?this.right.slice(this.right.length-n+this.left.length,this.right.length-e+this.left.length).reverse():this.left.slice(e).concat(this.right.slice(this.right.length-n+this.left.length).reverse())}splice(e,t,n){this.setCursor(Math.trunc(e));let r=this.right.splice(this.right.length-(t||0),Number.POSITIVE_INFINITY);return n&&ex(this.left,n),r.reverse()}pop(){return this.setCursor(Number.POSITIVE_INFINITY),this.left.pop()}push(e){this.setCursor(Number.POSITIVE_INFINITY),this.left.push(e)}pushMany(e){this.setCursor(Number.POSITIVE_INFINITY),ex(this.left,e)}unshift(e){this.setCursor(0),this.right.push(e)}unshiftMany(e){this.setCursor(0),ex(this.right,e.reverse())}setCursor(e){if(e!==this.left.length&&(!(e>this.left.length)||0!==this.right.length)&&(!(e<0)||0!==this.left.length)){if(e<this.left.length){let t=this.left.splice(e,Number.POSITIVE_INFINITY);ex(this.right,t.reverse())}else{let t=this.right.splice(this.left.length+this.right.length-e,Number.POSITIVE_INFINITY);ex(this.left,t.reverse())}}}}function ex(e,t){let n=0;if(t.length<1e4)e.push(...t);else for(;n<t.length;)e.push(...t.slice(n,n+1e4)),n+=1e4}function ek(e){let t,n,r,i,a,l,o;let s={},u=-1,c=new ev(e);for(;++u<c.length;){for(;u in s;)u=s[u];if(t=c.get(u),u&&"chunkFlow"===t[1].type&&"listItemPrefix"===c.get(u-1)[1].type&&((r=0)<(l=t[1]._tokenizer.events).length&&"lineEndingBlank"===l[r][1].type&&(r+=2),r<l.length&&"content"===l[r][1].type))for(;++r<l.length&&"content"!==l[r][1].type;)"chunkText"===l[r][1].type&&(l[r][1]._isInFirstContentOfListItem=!0,r++);if("enter"===t[0])t[1].contentType&&(Object.assign(s,function(e,t){let n,r;let i=e.get(t)[1],a=e.get(t)[2],l=t-1,o=[],s=i._tokenizer;!s&&(s=a.parser[i.contentType](i.start),i._contentTypeTextTrailing&&(s._contentTypeTextTrailing=!0));let u=s.events,c=[],d={},p=-1,f=i,h=0,m=0,g=[0];for(;f;){for(;e.get(++l)[1]!==f;);o.push(l),!f._tokenizer&&(n=a.sliceStream(f),f.next||n.push(null),r&&s.defineSkip(f.start),f._isInFirstContentOfListItem&&(s._gfmTasklistFirstContentOfListItem=!0),s.write(n),f._isInFirstContentOfListItem&&(s._gfmTasklistFirstContentOfListItem=void 0)),r=f,f=f.next}for(f=i;++p<u.length;)"exit"===u[p][0]&&"enter"===u[p-1][0]&&u[p][1].type===u[p-1][1].type&&u[p][1].start.line!==u[p][1].end.line&&(m=p+1,g.push(m),f._tokenizer=void 0,f.previous=void 0,f=f.next);for(s.events=[],f?(f._tokenizer=void 0,f.previous=void 0):g.pop(),p=g.length;p--;){let t=u.slice(g[p],g[p+1]),n=o.pop();c.push([n,n+t.length-1]),e.splice(n,2,t)}for(c.reverse(),p=-1;++p<c.length;)d[h+c[p][0]]=h+c[p][1],h+=c[p][1]-c[p][0]-1;return d}(c,u)),u=s[u],o=!0);else if(t[1]._container){for(r=u,n=void 0;r--;)if("lineEnding"===(i=c.get(r))[1].type||"lineEndingBlank"===i[1].type)"enter"===i[0]&&(n&&(c.get(n)[1].type="lineEndingBlank"),i[1].type="lineEnding",n=r);else if("linePrefix"===i[1].type||"listItemIndent"===i[1].type);else break;n&&(t[1].end={...c.get(n)[1].start},(a=c.slice(n,u)).unshift(t),c.splice(n,u-n+1,a))}}return eg(e,0,Number.POSITIVE_INFINITY,c.slice(0)),!o}let e_={}.hasOwnProperty,eb=eD(/[A-Za-z]/),ew=eD(/[\dA-Za-z]/),eS=eD(/[#-'*+\--9=?A-Z^-~]/);function eT(e){return null!==e&&(e<32||127===e)}let eC=eD(/\d/),eE=eD(/[\dA-Fa-f]/),eI=eD(/[!-/:-@[-`{-~]/);function eA(e){return null!==e&&e<-2}function eO(e){return null!==e&&(e<0||32===e)}function eP(e){return -2===e||-1===e||32===e}let eN=eD(/\p{P}|\p{S}/u),eL=eD(/\s/);function eD(e){return function(t){return null!==t&&t>-1&&e.test(String.fromCharCode(t))}}function eR(e,t,n,r){let i=r?r-1:Number.POSITIVE_INFINITY,a=0;return function(r){return eP(r)?(e.enter(n),function r(l){return eP(l)&&a++<i?(e.consume(l),r):(e.exit(n),t(l))}(r)):t(r)}}let ej={tokenize:function(e){let t;let n=e.attempt(this.parser.constructs.contentInitial,function(t){if(null===t){e.consume(t);return}return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),eR(e,n,"linePrefix")},function(n){return e.enter("paragraph"),function n(r){let i=e.enter("chunkText",{contentType:"text",previous:t});return t&&(t.next=i),t=i,function t(r){if(null===r){e.exit("chunkText"),e.exit("paragraph"),e.consume(r);return}return eA(r)?(e.consume(r),e.exit("chunkText"),n):(e.consume(r),t)}(r)}(n)});return n}},eM={tokenize:function(e){let t,n,r;let i=this,a=[],l=0;return o;function o(t){if(l<a.length){let n=a[l];return i.containerState=n[1],e.attempt(n[0].continuation,s,u)(t)}return u(t)}function s(e){if(l++,i.containerState._closeFlow){let n;i.containerState._closeFlow=void 0,t&&y();let r=i.events.length,a=r;for(;a--;)if("exit"===i.events[a][0]&&"chunkFlow"===i.events[a][1].type){n=i.events[a][1].end;break}g(l);let o=r;for(;o<i.events.length;)i.events[o][1].end={...n},o++;return eg(i.events,a+1,0,i.events.slice(r)),i.events.length=o,u(e)}return o(e)}function u(n){if(l===a.length){if(!t)return p(n);if(t.currentConstruct&&t.currentConstruct.concrete)return h(n);i.interrupt=!!(t.currentConstruct&&!t._gfmTableDynamicInterruptHack)}return i.containerState={},e.check(ez,c,d)(n)}function c(e){return t&&y(),g(l),p(e)}function d(e){return i.parser.lazy[i.now().line]=l!==a.length,r=i.now().offset,h(e)}function p(t){return i.containerState={},e.attempt(ez,f,h)(t)}function f(e){return l++,a.push([i.currentConstruct,i.containerState]),p(e)}function h(r){if(null===r){t&&y(),g(0),e.consume(r);return}return t=t||i.parser.flow(i.now()),e.enter("chunkFlow",{_tokenizer:t,contentType:"flow",previous:n}),function t(n){if(null===n){m(e.exit("chunkFlow"),!0),g(0),e.consume(n);return}return eA(n)?(e.consume(n),m(e.exit("chunkFlow")),l=0,i.interrupt=void 0,o):(e.consume(n),t)}(r)}function m(e,a){let o=i.sliceStream(e);if(a&&o.push(null),e.previous=n,n&&(n.next=e),n=e,t.defineSkip(e.start),t.write(o),i.parser.lazy[e.start.line]){let e,n,a=t.events.length;for(;a--;)if(t.events[a][1].start.offset<r&&(!t.events[a][1].end||t.events[a][1].end.offset>r))return;let o=i.events.length,s=o;for(;s--;)if("exit"===i.events[s][0]&&"chunkFlow"===i.events[s][1].type){if(e){n=i.events[s][1].end;break}e=!0}for(g(l),a=o;a<i.events.length;)i.events[a][1].end={...n},a++;eg(i.events,s+1,0,i.events.slice(o)),i.events.length=a}}function g(t){let n=a.length;for(;n-- >t;){let t=a[n];i.containerState=t[1],t[0].exit.call(i,e)}a.length=t}function y(){t.write([null]),n=void 0,t=void 0,i.containerState._closeFlow=void 0}}},ez={tokenize:function(e,t,n){return eR(e,e.attempt(this.parser.constructs.document,t,n),"linePrefix",this.parser.constructs.disable.null.includes("codeIndented")?void 0:4)}},eF={partial:!0,tokenize:function(e,t,n){return function(t){return eP(t)?eR(e,r,"linePrefix")(t):r(t)};function r(e){return null===e||eA(e)?t(e):n(e)}}},eZ={resolve:function(e){return ek(e),e},tokenize:function(e,t){let n;return function(t){return e.enter("content"),n=e.enter("chunkContent",{contentType:"content"}),r(t)};function r(t){return null===t?i(t):eA(t)?e.check(eB,a,i)(t):(e.consume(t),r)}function i(n){return e.exit("chunkContent"),e.exit("content"),t(n)}function a(t){return e.consume(t),e.exit("chunkContent"),n.next=e.enter("chunkContent",{contentType:"content",previous:n}),n=n.next,r}}},eB={partial:!0,tokenize:function(e,t,n){let r=this;return function(t){return e.exit("chunkContent"),e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),eR(e,i,"linePrefix")};function i(i){if(null===i||eA(i))return n(i);let a=r.events[r.events.length-1];return!r.parser.constructs.disable.null.includes("codeIndented")&&a&&"linePrefix"===a[1].type&&a[2].sliceSerialize(a[1],!0).length>=4?t(i):e.interrupt(r.parser.constructs.flow,n,t)(i)}}},eV={tokenize:function(e){let t=this,n=e.attempt(eF,function(r){if(null===r){e.consume(r);return}return e.enter("lineEndingBlank"),e.consume(r),e.exit("lineEndingBlank"),t.currentConstruct=void 0,n},e.attempt(this.parser.constructs.flowInitial,r,eR(e,e.attempt(this.parser.constructs.flow,r,e.attempt(eZ,r)),"linePrefix")));return n;function r(r){if(null===r){e.consume(r);return}return e.enter("lineEnding"),e.consume(r),e.exit("lineEnding"),t.currentConstruct=void 0,n}}},eU={resolveAll:eW()},e$=eq("string"),eH=eq("text");function eq(e){return{resolveAll:eW("text"===e?eK:void 0),tokenize:function(t){let n=this,r=this.parser.constructs[e],i=t.attempt(r,a,l);return a;function a(e){return s(e)?i(e):l(e)}function l(e){if(null===e){t.consume(e);return}return t.enter("data"),t.consume(e),o}function o(e){return s(e)?(t.exit("data"),i(e)):(t.consume(e),o)}function s(e){if(null===e)return!0;let t=r[e],i=-1;if(t)for(;++i<t.length;){let e=t[i];if(!e.previous||e.previous.call(n,n.previous))return!0}return!1}}}}function eW(e){return function(t,n){let r,i=-1;for(;++i<=t.length;)void 0===r?t[i]&&"data"===t[i][1].type&&(r=i,i++):t[i]&&"data"===t[i][1].type||(i!==r+2&&(t[r][1].end=t[i-1][1].end,t.splice(r+2,i-r-2),i=r+2),r=void 0);return e?e(t,n):t}}function eK(e,t){let n=0;for(;++n<=e.length;)if((n===e.length||"lineEnding"===e[n][1].type)&&"data"===e[n-1][1].type){let r;let i=e[n-1][1],a=t.sliceStream(i),l=a.length,o=-1,s=0;for(;l--;){let e=a[l];if("string"==typeof e){for(o=e.length;32===e.charCodeAt(o-1);)s++,o--;if(o)break;o=-1}else if(-2===e)r=!0,s++;else if(-1===e);else{l++;break}}if(t._contentTypeTextTrailing&&n===e.length&&(s=0),s){let a={type:n===e.length||r||s<2?"lineSuffix":"hardBreakTrailing",start:{_bufferIndex:l?o:i.start._bufferIndex+o,_index:i.start._index+l,line:i.end.line,column:i.end.column-s,offset:i.end.offset-s},end:{...i.end}};i.end={...a.start},i.start.offset===i.end.offset?Object.assign(i,a):(e.splice(n,0,["enter",a,t],["exit",a,t]),n+=2)}n++}return e}let eY={name:"thematicBreak",tokenize:function(e,t,n){let r,i=0;return function(a){return e.enter("thematicBreak"),r=a,function a(l){return l===r?(e.enter("thematicBreakSequence"),function t(n){return n===r?(e.consume(n),i++,t):(e.exit("thematicBreakSequence"),eP(n)?eR(e,a,"whitespace")(n):a(n))}(l)):i>=3&&(null===l||eA(l))?(e.exit("thematicBreak"),t(l)):n(l)}(a)}}},eJ={continuation:{tokenize:function(e,t,n){let r=this;return r.containerState._closeFlow=void 0,e.check(eF,function(n){return r.containerState.furtherBlankLines=r.containerState.furtherBlankLines||r.containerState.initialBlankLine,eR(e,t,"listItemIndent",r.containerState.size+1)(n)},function(n){return r.containerState.furtherBlankLines||!eP(n)?(r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,i(n)):(r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,e.attempt(eX,t,i)(n))});function i(i){return r.containerState._closeFlow=!0,r.interrupt=void 0,eR(e,e.attempt(eJ,t,n),"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(i)}}},exit:function(e){e.exit(this.containerState.type)},name:"list",tokenize:function(e,t,n){let r=this,i=r.events[r.events.length-1],a=i&&"linePrefix"===i[1].type?i[2].sliceSerialize(i[1],!0).length:0,l=0;return function(t){let i=r.containerState.type||(42===t||43===t||45===t?"listUnordered":"listOrdered");if("listUnordered"===i?!r.containerState.marker||t===r.containerState.marker:eC(t)){if(r.containerState.type||(r.containerState.type=i,e.enter(i,{_container:!0})),"listUnordered"===i)return e.enter("listItemPrefix"),42===t||45===t?e.check(eY,n,o)(t):o(t);if(!r.interrupt||49===t)return e.enter("listItemPrefix"),e.enter("listItemValue"),function t(i){return eC(i)&&++l<10?(e.consume(i),t):(!r.interrupt||l<2)&&(r.containerState.marker?i===r.containerState.marker:41===i||46===i)?(e.exit("listItemValue"),o(i)):n(i)}(t)}return n(t)};function o(t){return e.enter("listItemMarker"),e.consume(t),e.exit("listItemMarker"),r.containerState.marker=r.containerState.marker||t,e.check(eF,r.interrupt?n:s,e.attempt(eQ,c,u))}function s(e){return r.containerState.initialBlankLine=!0,a++,c(e)}function u(t){return eP(t)?(e.enter("listItemPrefixWhitespace"),e.consume(t),e.exit("listItemPrefixWhitespace"),c):n(t)}function c(n){return r.containerState.size=a+r.sliceSerialize(e.exit("listItemPrefix"),!0).length,t(n)}}},eQ={partial:!0,tokenize:function(e,t,n){let r=this;return eR(e,function(e){let i=r.events[r.events.length-1];return!eP(e)&&i&&"listItemPrefixWhitespace"===i[1].type?t(e):n(e)},"listItemPrefixWhitespace",r.parser.constructs.disable.null.includes("codeIndented")?void 0:5)}},eX={partial:!0,tokenize:function(e,t,n){let r=this;return eR(e,function(e){let i=r.events[r.events.length-1];return i&&"listItemIndent"===i[1].type&&i[2].sliceSerialize(i[1],!0).length===r.containerState.size?t(e):n(e)},"listItemIndent",r.containerState.size+1)}},eG={continuation:{tokenize:function(e,t,n){let r=this;return function(t){return eP(t)?eR(e,i,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):i(t)};function i(r){return e.attempt(eG,t,n)(r)}}},exit:function(e){e.exit("blockQuote")},name:"blockQuote",tokenize:function(e,t,n){let r=this;return function(t){if(62===t){let n=r.containerState;return n.open||(e.enter("blockQuote",{_container:!0}),n.open=!0),e.enter("blockQuotePrefix"),e.enter("blockQuoteMarker"),e.consume(t),e.exit("blockQuoteMarker"),i}return n(t)};function i(n){return eP(n)?(e.enter("blockQuotePrefixWhitespace"),e.consume(n),e.exit("blockQuotePrefixWhitespace"),e.exit("blockQuotePrefix"),t):(e.exit("blockQuotePrefix"),t(n))}}};function e0(e,t,n,r,i,a,l,o,s){let u=s||Number.POSITIVE_INFINITY,c=0;return function(t){return 60===t?(e.enter(r),e.enter(i),e.enter(a),e.consume(t),e.exit(a),d):null===t||32===t||41===t||eT(t)?n(t):(e.enter(r),e.enter(l),e.enter(o),e.enter("chunkString",{contentType:"string"}),h(t))};function d(n){return 62===n?(e.enter(a),e.consume(n),e.exit(a),e.exit(i),e.exit(r),t):(e.enter(o),e.enter("chunkString",{contentType:"string"}),p(n))}function p(t){return 62===t?(e.exit("chunkString"),e.exit(o),d(t)):null===t||60===t||eA(t)?n(t):(e.consume(t),92===t?f:p)}function f(t){return 60===t||62===t||92===t?(e.consume(t),p):p(t)}function h(i){return!c&&(null===i||41===i||eO(i))?(e.exit("chunkString"),e.exit(o),e.exit(l),e.exit(r),t(i)):c<u&&40===i?(e.consume(i),c++,h):41===i?(e.consume(i),c--,h):null===i||32===i||40===i||eT(i)?n(i):(e.consume(i),92===i?m:h)}function m(t){return 40===t||41===t||92===t?(e.consume(t),h):h(t)}}function e1(e,t,n,r,i,a){let l;let o=this,s=0;return function(t){return e.enter(r),e.enter(i),e.consume(t),e.exit(i),e.enter(a),u};function u(d){return s>999||null===d||91===d||93===d&&!l||94===d&&!s&&"_hiddenFootnoteSupport"in o.parser.constructs?n(d):93===d?(e.exit(a),e.enter(i),e.consume(d),e.exit(i),e.exit(r),t):eA(d)?(e.enter("lineEnding"),e.consume(d),e.exit("lineEnding"),u):(e.enter("chunkString",{contentType:"string"}),c(d))}function c(t){return null===t||91===t||93===t||eA(t)||s++>999?(e.exit("chunkString"),u(t)):(e.consume(t),l||(l=!eP(t)),92===t?d:c)}function d(t){return 91===t||92===t||93===t?(e.consume(t),s++,c):c(t)}}function e4(e,t,n,r,i,a){let l;return function(t){return 34===t||39===t||40===t?(e.enter(r),e.enter(i),e.consume(t),e.exit(i),l=40===t?41:t,o):n(t)};function o(n){return n===l?(e.enter(i),e.consume(n),e.exit(i),e.exit(r),t):(e.enter(a),s(n))}function s(t){return t===l?(e.exit(a),o(l)):null===t?n(t):eA(t)?(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),eR(e,s,"linePrefix")):(e.enter("chunkString",{contentType:"string"}),u(t))}function u(t){return t===l||null===t||eA(t)?(e.exit("chunkString"),s(t)):(e.consume(t),92===t?c:u)}function c(t){return t===l||92===t?(e.consume(t),u):u(t)}}function e2(e,t){let n;return function r(i){return eA(i)?(e.enter("lineEnding"),e.consume(i),e.exit("lineEnding"),n=!0,r):eP(i)?eR(e,r,n?"linePrefix":"lineSuffix")(i):t(i)}}function e9(e){return e.replace(/[\t\n\r ]+/g," ").replace(/^ | $/g,"").toLowerCase().toUpperCase()}let e5={partial:!0,tokenize:function(e,t,n){return function(t){return eO(t)?e2(e,r)(t):n(t)};function r(t){return e4(e,i,n,"definitionTitle","definitionTitleMarker","definitionTitleString")(t)}function i(t){return eP(t)?eR(e,a,"whitespace")(t):a(t)}function a(e){return null===e||eA(e)?t(e):n(e)}}},e6={name:"codeIndented",tokenize:function(e,t,n){let r=this;return function(t){return e.enter("codeIndented"),eR(e,i,"linePrefix",5)(t)};function i(t){let i=r.events[r.events.length-1];return i&&"linePrefix"===i[1].type&&i[2].sliceSerialize(i[1],!0).length>=4?function t(n){return null===n?a(n):eA(n)?e.attempt(e3,t,a)(n):(e.enter("codeFlowValue"),function n(r){return null===r||eA(r)?(e.exit("codeFlowValue"),t(r)):(e.consume(r),n)}(n))}(t):n(t)}function a(n){return e.exit("codeIndented"),t(n)}}},e3={partial:!0,tokenize:function(e,t,n){let r=this;return i;function i(t){return r.parser.lazy[r.now().line]?n(t):eA(t)?(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),i):eR(e,a,"linePrefix",5)(t)}function a(e){let a=r.events[r.events.length-1];return a&&"linePrefix"===a[1].type&&a[2].sliceSerialize(a[1],!0).length>=4?t(e):eA(e)?i(e):n(e)}}},e7={name:"setextUnderline",resolveTo:function(e,t){let n,r,i,a=e.length;for(;a--;)if("enter"===e[a][0]){if("content"===e[a][1].type){n=a;break}"paragraph"===e[a][1].type&&(r=a)}else"content"===e[a][1].type&&e.splice(a,1),i||"definition"!==e[a][1].type||(i=a);let l={type:"setextHeading",start:{...e[n][1].start},end:{...e[e.length-1][1].end}};return e[r][1].type="setextHeadingText",i?(e.splice(r,0,["enter",l,t]),e.splice(i+1,0,["exit",e[n][1],t]),e[n][1].end={...e[i][1].end}):e[n][1]=l,e.push(["exit",l,t]),e},tokenize:function(e,t,n){let r;let i=this;return function(t){let l,o=i.events.length;for(;o--;)if("lineEnding"!==i.events[o][1].type&&"linePrefix"!==i.events[o][1].type&&"content"!==i.events[o][1].type){l="paragraph"===i.events[o][1].type;break}return!i.parser.lazy[i.now().line]&&(i.interrupt||l)?(e.enter("setextHeadingLine"),r=t,e.enter("setextHeadingLineSequence"),function t(n){return n===r?(e.consume(n),t):(e.exit("setextHeadingLineSequence"),eP(n)?eR(e,a,"lineSuffix")(n):a(n))}(t)):n(t)};function a(r){return null===r||eA(r)?(e.exit("setextHeadingLine"),t(r)):n(r)}}},e8=["address","article","aside","base","basefont","blockquote","body","caption","center","col","colgroup","dd","details","dialog","dir","div","dl","dt","fieldset","figcaption","figure","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hr","html","iframe","legend","li","link","main","menu","menuitem","nav","noframes","ol","optgroup","option","p","param","search","section","summary","table","tbody","td","tfoot","th","thead","title","tr","track","ul"],te=["pre","script","style","textarea"],tt={partial:!0,tokenize:function(e,t,n){return function(r){return e.enter("lineEnding"),e.consume(r),e.exit("lineEnding"),e.attempt(eF,t,n)}}},tn={partial:!0,tokenize:function(e,t,n){let r=this;return function(t){return eA(t)?(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),i):n(t)};function i(e){return r.parser.lazy[r.now().line]?n(e):t(e)}}},tr={partial:!0,tokenize:function(e,t,n){let r=this;return function(t){return null===t?n(t):(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),i)};function i(e){return r.parser.lazy[r.now().line]?n(e):t(e)}}},ti={concrete:!0,name:"codeFenced",tokenize:function(e,t,n){let r;let i=this,a={partial:!0,tokenize:function(e,t,n){let a=0;return function(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),l};function l(t){return e.enter("codeFencedFence"),eP(t)?eR(e,s,"linePrefix",i.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):s(t)}function s(t){return t===r?(e.enter("codeFencedFenceSequence"),function t(i){return i===r?(a++,e.consume(i),t):a>=o?(e.exit("codeFencedFenceSequence"),eP(i)?eR(e,u,"whitespace")(i):u(i)):n(i)}(t)):n(t)}function u(r){return null===r||eA(r)?(e.exit("codeFencedFence"),t(r)):n(r)}}},l=0,o=0;return function(t){return function(t){let a=i.events[i.events.length-1];return l=a&&"linePrefix"===a[1].type?a[2].sliceSerialize(a[1],!0).length:0,r=t,e.enter("codeFenced"),e.enter("codeFencedFence"),e.enter("codeFencedFenceSequence"),function t(i){return i===r?(o++,e.consume(i),t):o<3?n(i):(e.exit("codeFencedFenceSequence"),eP(i)?eR(e,s,"whitespace")(i):s(i))}(t)}(t)};function s(a){return null===a||eA(a)?(e.exit("codeFencedFence"),i.interrupt?t(a):e.check(tr,c,h)(a)):(e.enter("codeFencedFenceInfo"),e.enter("chunkString",{contentType:"string"}),function t(i){return null===i||eA(i)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),s(i)):eP(i)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),eR(e,u,"whitespace")(i)):96===i&&i===r?n(i):(e.consume(i),t)}(a))}function u(t){return null===t||eA(t)?s(t):(e.enter("codeFencedFenceMeta"),e.enter("chunkString",{contentType:"string"}),function t(i){return null===i||eA(i)?(e.exit("chunkString"),e.exit("codeFencedFenceMeta"),s(i)):96===i&&i===r?n(i):(e.consume(i),t)}(t))}function c(t){return e.attempt(a,h,d)(t)}function d(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),p}function p(t){return l>0&&eP(t)?eR(e,f,"linePrefix",l+1)(t):f(t)}function f(t){return null===t||eA(t)?e.check(tr,c,h)(t):(e.enter("codeFlowValue"),function t(n){return null===n||eA(n)?(e.exit("codeFlowValue"),f(n)):(e.consume(n),t)}(t))}function h(n){return e.exit("codeFenced"),t(n)}}},ta=document.createElement("i");function tl(e){let t="&"+e+";";ta.innerHTML=t;let n=ta.textContent;return(59!==n.charCodeAt(n.length-1)||"semi"===e)&&n!==t&&n}let to={name:"characterReference",tokenize:function(e,t,n){let r,i;let a=this,l=0;return function(t){return e.enter("characterReference"),e.enter("characterReferenceMarker"),e.consume(t),e.exit("characterReferenceMarker"),o};function o(t){return 35===t?(e.enter("characterReferenceMarkerNumeric"),e.consume(t),e.exit("characterReferenceMarkerNumeric"),s):(e.enter("characterReferenceValue"),r=31,i=ew,u(t))}function s(t){return 88===t||120===t?(e.enter("characterReferenceMarkerHexadecimal"),e.consume(t),e.exit("characterReferenceMarkerHexadecimal"),e.enter("characterReferenceValue"),r=6,i=eE,u):(e.enter("characterReferenceValue"),r=7,i=eC,u(t))}function u(o){if(59===o&&l){let r=e.exit("characterReferenceValue");return i!==ew||tl(a.sliceSerialize(r))?(e.enter("characterReferenceMarker"),e.consume(o),e.exit("characterReferenceMarker"),e.exit("characterReference"),t):n(o)}return i(o)&&l++<r?(e.consume(o),u):n(o)}}},ts={name:"characterEscape",tokenize:function(e,t,n){return function(t){return e.enter("characterEscape"),e.enter("escapeMarker"),e.consume(t),e.exit("escapeMarker"),r};function r(r){return eI(r)?(e.enter("characterEscapeValue"),e.consume(r),e.exit("characterEscapeValue"),e.exit("characterEscape"),t):n(r)}}},tu={name:"lineEnding",tokenize:function(e,t){return function(n){return e.enter("lineEnding"),e.consume(n),e.exit("lineEnding"),eR(e,t,"linePrefix")}}};function tc(e,t,n){let r=[],i=-1;for(;++i<e.length;){let a=e[i].resolveAll;a&&!r.includes(a)&&(t=a(t,n),r.push(a))}return t}let td={name:"labelEnd",resolveAll:function(e){let t=-1,n=[];for(;++t<e.length;){let r=e[t][1];if(n.push(e[t]),"labelImage"===r.type||"labelLink"===r.type||"labelEnd"===r.type){let e="labelImage"===r.type?4:2;r.type="data",t+=e}}return e.length!==n.length&&eg(e,0,e.length,n),e},resolveTo:function(e,t){let n,r,i,a,l=e.length,o=0;for(;l--;)if(n=e[l][1],r){if("link"===n.type||"labelLink"===n.type&&n._inactive)break;"enter"===e[l][0]&&"labelLink"===n.type&&(n._inactive=!0)}else if(i){if("enter"===e[l][0]&&("labelImage"===n.type||"labelLink"===n.type)&&!n._balanced&&(r=l,"labelLink"!==n.type)){o=2;break}}else"labelEnd"===n.type&&(i=l);let s={type:"labelLink"===e[r][1].type?"link":"image",start:{...e[r][1].start},end:{...e[e.length-1][1].end}},u={type:"label",start:{...e[r][1].start},end:{...e[i][1].end}},c={type:"labelText",start:{...e[r+o+2][1].end},end:{...e[i-2][1].start}};return a=ey(a=[["enter",s,t],["enter",u,t]],e.slice(r+1,r+o+3)),a=ey(a,[["enter",c,t]]),a=ey(a,tc(t.parser.constructs.insideSpan.null,e.slice(r+o+4,i-3),t)),a=ey(a,[["exit",c,t],e[i-2],e[i-1],["exit",u,t]]),a=ey(a,e.slice(i+1)),a=ey(a,[["exit",s,t]]),eg(e,r,e.length,a),e},tokenize:function(e,t,n){let r,i;let a=this,l=a.events.length;for(;l--;)if(("labelImage"===a.events[l][1].type||"labelLink"===a.events[l][1].type)&&!a.events[l][1]._balanced){r=a.events[l][1];break}return function(t){return r?r._inactive?c(t):(i=a.parser.defined.includes(e9(a.sliceSerialize({start:r.end,end:a.now()}))),e.enter("labelEnd"),e.enter("labelMarker"),e.consume(t),e.exit("labelMarker"),e.exit("labelEnd"),o):n(t)};function o(t){return 40===t?e.attempt(tp,u,i?u:c)(t):91===t?e.attempt(tf,u,i?s:c)(t):i?u(t):c(t)}function s(t){return e.attempt(th,u,c)(t)}function u(e){return t(e)}function c(e){return r._balanced=!0,n(e)}}},tp={tokenize:function(e,t,n){return function(t){return e.enter("resource"),e.enter("resourceMarker"),e.consume(t),e.exit("resourceMarker"),r};function r(t){return eO(t)?e2(e,i)(t):i(t)}function i(t){return 41===t?u(t):e0(e,a,l,"resourceDestination","resourceDestinationLiteral","resourceDestinationLiteralMarker","resourceDestinationRaw","resourceDestinationString",32)(t)}function a(t){return eO(t)?e2(e,o)(t):u(t)}function l(e){return n(e)}function o(t){return 34===t||39===t||40===t?e4(e,s,n,"resourceTitle","resourceTitleMarker","resourceTitleString")(t):u(t)}function s(t){return eO(t)?e2(e,u)(t):u(t)}function u(r){return 41===r?(e.enter("resourceMarker"),e.consume(r),e.exit("resourceMarker"),e.exit("resource"),t):n(r)}}},tf={tokenize:function(e,t,n){let r=this;return function(t){return e1.call(r,e,i,a,"reference","referenceMarker","referenceString")(t)};function i(e){return r.parser.defined.includes(e9(r.sliceSerialize(r.events[r.events.length-1][1]).slice(1,-1)))?t(e):n(e)}function a(e){return n(e)}}},th={tokenize:function(e,t,n){return function(t){return e.enter("reference"),e.enter("referenceMarker"),e.consume(t),e.exit("referenceMarker"),r};function r(r){return 93===r?(e.enter("referenceMarker"),e.consume(r),e.exit("referenceMarker"),e.exit("reference"),t):n(r)}}},tm={name:"labelStartImage",resolveAll:td.resolveAll,tokenize:function(e,t,n){let r=this;return function(t){return e.enter("labelImage"),e.enter("labelImageMarker"),e.consume(t),e.exit("labelImageMarker"),i};function i(t){return 91===t?(e.enter("labelMarker"),e.consume(t),e.exit("labelMarker"),e.exit("labelImage"),a):n(t)}function a(e){return 94===e&&"_hiddenFootnoteSupport"in r.parser.constructs?n(e):t(e)}}};function tg(e){return null===e||eO(e)||eL(e)?1:eN(e)?2:void 0}let ty={name:"attention",resolveAll:function(e,t){let n,r,i,a,l,o,s,u,c=-1;for(;++c<e.length;)if("enter"===e[c][0]&&"attentionSequence"===e[c][1].type&&e[c][1]._close){for(n=c;n--;)if("exit"===e[n][0]&&"attentionSequence"===e[n][1].type&&e[n][1]._open&&t.sliceSerialize(e[n][1]).charCodeAt(0)===t.sliceSerialize(e[c][1]).charCodeAt(0)){if((e[n][1]._close||e[c][1]._open)&&(e[c][1].end.offset-e[c][1].start.offset)%3&&!((e[n][1].end.offset-e[n][1].start.offset+e[c][1].end.offset-e[c][1].start.offset)%3))continue;o=e[n][1].end.offset-e[n][1].start.offset>1&&e[c][1].end.offset-e[c][1].start.offset>1?2:1;let d={...e[n][1].end},p={...e[c][1].start};tv(d,-o),tv(p,o),a={type:o>1?"strongSequence":"emphasisSequence",start:d,end:{...e[n][1].end}},l={type:o>1?"strongSequence":"emphasisSequence",start:{...e[c][1].start},end:p},i={type:o>1?"strongText":"emphasisText",start:{...e[n][1].end},end:{...e[c][1].start}},r={type:o>1?"strong":"emphasis",start:{...a.start},end:{...l.end}},e[n][1].end={...a.start},e[c][1].start={...l.end},s=[],e[n][1].end.offset-e[n][1].start.offset&&(s=ey(s,[["enter",e[n][1],t],["exit",e[n][1],t]])),s=ey(s,[["enter",r,t],["enter",a,t],["exit",a,t],["enter",i,t]]),s=ey(s,tc(t.parser.constructs.insideSpan.null,e.slice(n+1,c),t)),s=ey(s,[["exit",i,t],["enter",l,t],["exit",l,t],["exit",r,t]]),e[c][1].end.offset-e[c][1].start.offset?(u=2,s=ey(s,[["enter",e[c][1],t],["exit",e[c][1],t]])):u=0,eg(e,n-1,c-n+3,s),c=n+s.length-u-2;break}}for(c=-1;++c<e.length;)"attentionSequence"===e[c][1].type&&(e[c][1].type="data");return e},tokenize:function(e,t){let n;let r=this.parser.constructs.attentionMarkers.null,i=this.previous,a=tg(i);return function(l){return n=l,e.enter("attentionSequence"),function l(o){if(o===n)return e.consume(o),l;let s=e.exit("attentionSequence"),u=tg(o),c=!u||2===u&&a||r.includes(o),d=!a||2===a&&u||r.includes(i);return s._open=!!(42===n?c:c&&(a||!d)),s._close=!!(42===n?d:d&&(u||!c)),t(o)}(l)}}};function tv(e,t){e.column+=t,e.offset+=t,e._bufferIndex+=t}let tx={name:"labelStartLink",resolveAll:td.resolveAll,tokenize:function(e,t,n){let r=this;return function(t){return e.enter("labelLink"),e.enter("labelMarker"),e.consume(t),e.exit("labelMarker"),e.exit("labelLink"),i};function i(e){return 94===e&&"_hiddenFootnoteSupport"in r.parser.constructs?n(e):t(e)}}},tk={42:eJ,43:eJ,45:eJ,48:eJ,49:eJ,50:eJ,51:eJ,52:eJ,53:eJ,54:eJ,55:eJ,56:eJ,57:eJ,62:eG},t_={91:{name:"definition",tokenize:function(e,t,n){let r;let i=this;return function(t){return e.enter("definition"),e1.call(i,e,a,n,"definitionLabel","definitionLabelMarker","definitionLabelString")(t)};function a(t){return(r=e9(i.sliceSerialize(i.events[i.events.length-1][1]).slice(1,-1)),58===t)?(e.enter("definitionMarker"),e.consume(t),e.exit("definitionMarker"),l):n(t)}function l(t){return eO(t)?e2(e,o)(t):o(t)}function o(t){return e0(e,s,n,"definitionDestination","definitionDestinationLiteral","definitionDestinationLiteralMarker","definitionDestinationRaw","definitionDestinationString")(t)}function s(t){return e.attempt(e5,u,u)(t)}function u(t){return eP(t)?eR(e,c,"whitespace")(t):c(t)}function c(a){return null===a||eA(a)?(e.exit("definition"),i.parser.defined.push(r),t(a)):n(a)}}}},tb={[-2]:e6,[-1]:e6,32:e6},tw={35:{name:"headingAtx",resolve:function(e,t){let n,r,i=e.length-2,a=3;return"whitespace"===e[3][1].type&&(a+=2),i-2>a&&"whitespace"===e[i][1].type&&(i-=2),"atxHeadingSequence"===e[i][1].type&&(a===i-1||i-4>a&&"whitespace"===e[i-2][1].type)&&(i-=a+1===i?2:4),i>a&&(n={type:"atxHeadingText",start:e[a][1].start,end:e[i][1].end},r={type:"chunkText",start:e[a][1].start,end:e[i][1].end,contentType:"text"},eg(e,a,i-a+1,[["enter",n,t],["enter",r,t],["exit",r,t],["exit",n,t]])),e},tokenize:function(e,t,n){let r=0;return function(i){return e.enter("atxHeading"),e.enter("atxHeadingSequence"),function i(a){return 35===a&&r++<6?(e.consume(a),i):null===a||eO(a)?(e.exit("atxHeadingSequence"),function n(r){return 35===r?(e.enter("atxHeadingSequence"),function t(r){return 35===r?(e.consume(r),t):(e.exit("atxHeadingSequence"),n(r))}(r)):null===r||eA(r)?(e.exit("atxHeading"),t(r)):eP(r)?eR(e,n,"whitespace")(r):(e.enter("atxHeadingText"),function t(r){return null===r||35===r||eO(r)?(e.exit("atxHeadingText"),n(r)):(e.consume(r),t)}(r))}(a)):n(a)}(i)}}},42:eY,45:[e7,eY],60:{concrete:!0,name:"htmlFlow",resolveTo:function(e){let t=e.length;for(;t--&&("enter"!==e[t][0]||"htmlFlow"!==e[t][1].type););return t>1&&"linePrefix"===e[t-2][1].type&&(e[t][1].start=e[t-2][1].start,e[t+1][1].start=e[t-2][1].start,e.splice(t-2,2)),e},tokenize:function(e,t,n){let r,i,a,l,o;let s=this;return function(t){return e.enter("htmlFlow"),e.enter("htmlFlowData"),e.consume(t),u};function u(l){return 33===l?(e.consume(l),c):47===l?(e.consume(l),i=!0,f):63===l?(e.consume(l),r=3,s.interrupt?t:N):eb(l)?(e.consume(l),a=String.fromCharCode(l),h):n(l)}function c(i){return 45===i?(e.consume(i),r=2,d):91===i?(e.consume(i),r=5,l=0,p):eb(i)?(e.consume(i),r=4,s.interrupt?t:N):n(i)}function d(r){return 45===r?(e.consume(r),s.interrupt?t:N):n(r)}function p(r){let i="CDATA[";return r===i.charCodeAt(l++)?(e.consume(r),l===i.length)?s.interrupt?t:S:p:n(r)}function f(t){return eb(t)?(e.consume(t),a=String.fromCharCode(t),h):n(t)}function h(l){if(null===l||47===l||62===l||eO(l)){let o=47===l,u=a.toLowerCase();return!o&&!i&&te.includes(u)?(r=1,s.interrupt?t(l):S(l)):e8.includes(a.toLowerCase())?(r=6,o)?(e.consume(l),m):s.interrupt?t(l):S(l):(r=7,s.interrupt&&!s.parser.lazy[s.now().line]?n(l):i?function t(n){return eP(n)?(e.consume(n),t):b(n)}(l):g(l))}return 45===l||ew(l)?(e.consume(l),a+=String.fromCharCode(l),h):n(l)}function m(r){return 62===r?(e.consume(r),s.interrupt?t:S):n(r)}function g(t){return 47===t?(e.consume(t),b):58===t||95===t||eb(t)?(e.consume(t),y):eP(t)?(e.consume(t),g):b(t)}function y(t){return 45===t||46===t||58===t||95===t||ew(t)?(e.consume(t),y):v(t)}function v(t){return 61===t?(e.consume(t),x):eP(t)?(e.consume(t),v):g(t)}function x(t){return null===t||60===t||61===t||62===t||96===t?n(t):34===t||39===t?(e.consume(t),o=t,k):eP(t)?(e.consume(t),x):function t(n){return null===n||34===n||39===n||47===n||60===n||61===n||62===n||96===n||eO(n)?v(n):(e.consume(n),t)}(t)}function k(t){return t===o?(e.consume(t),o=null,_):null===t||eA(t)?n(t):(e.consume(t),k)}function _(e){return 47===e||62===e||eP(e)?g(e):n(e)}function b(t){return 62===t?(e.consume(t),w):n(t)}function w(t){return null===t||eA(t)?S(t):eP(t)?(e.consume(t),w):n(t)}function S(t){return 45===t&&2===r?(e.consume(t),I):60===t&&1===r?(e.consume(t),A):62===t&&4===r?(e.consume(t),L):63===t&&3===r?(e.consume(t),N):93===t&&5===r?(e.consume(t),P):eA(t)&&(6===r||7===r)?(e.exit("htmlFlowData"),e.check(tt,D,T)(t)):null===t||eA(t)?(e.exit("htmlFlowData"),T(t)):(e.consume(t),S)}function T(t){return e.check(tn,C,D)(t)}function C(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),E}function E(t){return null===t||eA(t)?T(t):(e.enter("htmlFlowData"),S(t))}function I(t){return 45===t?(e.consume(t),N):S(t)}function A(t){return 47===t?(e.consume(t),a="",O):S(t)}function O(t){if(62===t){let n=a.toLowerCase();return te.includes(n)?(e.consume(t),L):S(t)}return eb(t)&&a.length<8?(e.consume(t),a+=String.fromCharCode(t),O):S(t)}function P(t){return 93===t?(e.consume(t),N):S(t)}function N(t){return 62===t?(e.consume(t),L):45===t&&2===r?(e.consume(t),N):S(t)}function L(t){return null===t||eA(t)?(e.exit("htmlFlowData"),D(t)):(e.consume(t),L)}function D(n){return e.exit("htmlFlow"),t(n)}}},61:e7,95:eY,96:ti,126:ti},tS={38:to,92:ts},tT={[-5]:tu,[-4]:tu,[-3]:tu,33:tm,38:to,42:ty,60:[{name:"autolink",tokenize:function(e,t,n){let r=0;return function(t){return e.enter("autolink"),e.enter("autolinkMarker"),e.consume(t),e.exit("autolinkMarker"),e.enter("autolinkProtocol"),i};function i(t){return eb(t)?(e.consume(t),a):64===t?n(t):o(t)}function a(t){return 43===t||45===t||46===t||ew(t)?(r=1,function t(n){return 58===n?(e.consume(n),r=0,l):(43===n||45===n||46===n||ew(n))&&r++<32?(e.consume(n),t):(r=0,o(n))}(t)):o(t)}function l(r){return 62===r?(e.exit("autolinkProtocol"),e.enter("autolinkMarker"),e.consume(r),e.exit("autolinkMarker"),e.exit("autolink"),t):null===r||32===r||60===r||eT(r)?n(r):(e.consume(r),l)}function o(t){return 64===t?(e.consume(t),s):eS(t)?(e.consume(t),o):n(t)}function s(i){return ew(i)?function i(a){return 46===a?(e.consume(a),r=0,s):62===a?(e.exit("autolinkProtocol").type="autolinkEmail",e.enter("autolinkMarker"),e.consume(a),e.exit("autolinkMarker"),e.exit("autolink"),t):function t(a){if((45===a||ew(a))&&r++<63){let n=45===a?t:i;return e.consume(a),n}return n(a)}(a)}(i):n(i)}}},{name:"htmlText",tokenize:function(e,t,n){let r,i,a;let l=this;return function(t){return e.enter("htmlText"),e.enter("htmlTextData"),e.consume(t),o};function o(t){return 33===t?(e.consume(t),s):47===t?(e.consume(t),k):63===t?(e.consume(t),v):eb(t)?(e.consume(t),b):n(t)}function s(t){return 45===t?(e.consume(t),u):91===t?(e.consume(t),i=0,f):eb(t)?(e.consume(t),y):n(t)}function u(t){return 45===t?(e.consume(t),p):n(t)}function c(t){return null===t?n(t):45===t?(e.consume(t),d):eA(t)?(a=c,O(t)):(e.consume(t),c)}function d(t){return 45===t?(e.consume(t),p):c(t)}function p(e){return 62===e?A(e):45===e?d(e):c(e)}function f(t){let r="CDATA[";return t===r.charCodeAt(i++)?(e.consume(t),i===r.length?h:f):n(t)}function h(t){return null===t?n(t):93===t?(e.consume(t),m):eA(t)?(a=h,O(t)):(e.consume(t),h)}function m(t){return 93===t?(e.consume(t),g):h(t)}function g(t){return 62===t?A(t):93===t?(e.consume(t),g):h(t)}function y(t){return null===t||62===t?A(t):eA(t)?(a=y,O(t)):(e.consume(t),y)}function v(t){return null===t?n(t):63===t?(e.consume(t),x):eA(t)?(a=v,O(t)):(e.consume(t),v)}function x(e){return 62===e?A(e):v(e)}function k(t){return eb(t)?(e.consume(t),_):n(t)}function _(t){return 45===t||ew(t)?(e.consume(t),_):function t(n){return eA(n)?(a=t,O(n)):eP(n)?(e.consume(n),t):A(n)}(t)}function b(t){return 45===t||ew(t)?(e.consume(t),b):47===t||62===t||eO(t)?w(t):n(t)}function w(t){return 47===t?(e.consume(t),A):58===t||95===t||eb(t)?(e.consume(t),S):eA(t)?(a=w,O(t)):eP(t)?(e.consume(t),w):A(t)}function S(t){return 45===t||46===t||58===t||95===t||ew(t)?(e.consume(t),S):function t(n){return 61===n?(e.consume(n),T):eA(n)?(a=t,O(n)):eP(n)?(e.consume(n),t):w(n)}(t)}function T(t){return null===t||60===t||61===t||62===t||96===t?n(t):34===t||39===t?(e.consume(t),r=t,C):eA(t)?(a=T,O(t)):eP(t)?(e.consume(t),T):(e.consume(t),E)}function C(t){return t===r?(e.consume(t),r=void 0,I):null===t?n(t):eA(t)?(a=C,O(t)):(e.consume(t),C)}function E(t){return null===t||34===t||39===t||60===t||61===t||96===t?n(t):47===t||62===t||eO(t)?w(t):(e.consume(t),E)}function I(e){return 47===e||62===e||eO(e)?w(e):n(e)}function A(r){return 62===r?(e.consume(r),e.exit("htmlTextData"),e.exit("htmlText"),t):n(r)}function O(t){return e.exit("htmlTextData"),e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),P}function P(t){return eP(t)?eR(e,N,"linePrefix",l.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):N(t)}function N(t){return e.enter("htmlTextData"),a(t)}}}],91:tx,92:[{name:"hardBreakEscape",tokenize:function(e,t,n){return function(t){return e.enter("hardBreakEscape"),e.consume(t),r};function r(r){return eA(r)?(e.exit("hardBreakEscape"),t(r)):n(r)}}},ts],93:td,95:ty,96:{name:"codeText",previous:function(e){return 96!==e||"characterEscape"===this.events[this.events.length-1][1].type},resolve:function(e){let t,n,r=e.length-4,i=3;if(("lineEnding"===e[3][1].type||"space"===e[i][1].type)&&("lineEnding"===e[r][1].type||"space"===e[r][1].type)){for(t=i;++t<r;)if("codeTextData"===e[t][1].type){e[i][1].type="codeTextPadding",e[r][1].type="codeTextPadding",i+=2,r-=2;break}}for(t=i-1,r++;++t<=r;)void 0===n?t!==r&&"lineEnding"!==e[t][1].type&&(n=t):(t===r||"lineEnding"===e[t][1].type)&&(e[n][1].type="codeTextData",t!==n+2&&(e[n][1].end=e[t-1][1].end,e.splice(n+2,t-n-2),r-=t-n-2,t=n+2),n=void 0);return e},tokenize:function(e,t,n){let r,i,a=0;return function(t){return e.enter("codeText"),e.enter("codeTextSequence"),function t(n){return 96===n?(e.consume(n),a++,t):(e.exit("codeTextSequence"),l(n))}(t)};function l(s){return null===s?n(s):32===s?(e.enter("space"),e.consume(s),e.exit("space"),l):96===s?(i=e.enter("codeTextSequence"),r=0,function n(l){return 96===l?(e.consume(l),r++,n):r===a?(e.exit("codeTextSequence"),e.exit("codeText"),t(l)):(i.type="codeTextData",o(l))}(s)):eA(s)?(e.enter("lineEnding"),e.consume(s),e.exit("lineEnding"),l):(e.enter("codeTextData"),o(s))}function o(t){return null===t||32===t||96===t||eA(t)?(e.exit("codeTextData"),l(t)):(e.consume(t),o)}}}},tC={null:[ty,eU]},tE={null:[42,95]},tI={null:[]},tA=/[\0\t\n\r]/g;function tO(e,t){let n=Number.parseInt(e,t);return n<9||11===n||n>13&&n<32||n>126&&n<160||n>55295&&n<57344||n>64975&&n<65008||(65535&n)==65535||(65535&n)==65534||n>1114111?"�":String.fromCodePoint(n)}let tP=/\\([!-/:-@[-`{-~])|&(#(?:\d{1,7}|x[\da-f]{1,6})|[\da-z]{1,31});/gi;function tN(e,t,n){if(t)return t;if(35===n.charCodeAt(0)){let e=n.charCodeAt(1),t=120===e||88===e;return tO(n.slice(t?2:1),t?16:10)}return tl(n)||e}let tL={}.hasOwnProperty;function tD(e){return{line:e.line,column:e.column,offset:e.offset}}function tR(e,t){if(e)throw Error("Cannot close `"+e.type+"` ("+W({start:e.start,end:e.end})+"): a different token (`"+t.type+"`, "+W({start:t.start,end:t.end})+") is open");throw Error("Cannot close document, a token (`"+t.type+"`, "+W({start:t.start,end:t.end})+") is still open")}function tj(e){let t=this;t.parser=function(n){var r,a;let l,o,s,u;return"string"!=typeof(r={...t.data("settings"),...e,extensions:t.data("micromarkExtensions")||[],mdastExtensions:t.data("fromMarkdownExtensions")||[]})&&(a=r,r=void 0),(function(e){let t={transforms:[],canContainEols:["emphasis","fragment","heading","paragraph","strong"],enter:{autolink:r(y),autolinkProtocol:u,autolinkEmail:u,atxHeading:r(h),blockQuote:r(function(){return{type:"blockquote",children:[]}}),characterEscape:u,characterReference:u,codeFenced:r(f),codeFencedFenceInfo:i,codeFencedFenceMeta:i,codeIndented:r(f,i),codeText:r(function(){return{type:"inlineCode",value:""}},i),codeTextData:u,data:u,codeFlowValue:u,definition:r(function(){return{type:"definition",identifier:"",label:null,title:null,url:""}}),definitionDestinationString:i,definitionLabelString:i,definitionTitleString:i,emphasis:r(function(){return{type:"emphasis",children:[]}}),hardBreakEscape:r(m),hardBreakTrailing:r(m),htmlFlow:r(g,i),htmlFlowData:u,htmlText:r(g,i),htmlTextData:u,image:r(function(){return{type:"image",title:null,url:"",alt:null}}),label:i,link:r(y),listItem:r(function(e){return{type:"listItem",spread:e._spread,checked:null,children:[]}}),listItemValue:function(e){this.data.expectingFirstListItemValue&&(this.stack[this.stack.length-2].start=Number.parseInt(this.sliceSerialize(e),10),this.data.expectingFirstListItemValue=void 0)},listOrdered:r(v,function(){this.data.expectingFirstListItemValue=!0}),listUnordered:r(v),paragraph:r(function(){return{type:"paragraph",children:[]}}),reference:function(){this.data.referenceType="collapsed"},referenceString:i,resourceDestinationString:i,resourceTitleString:i,setextHeading:r(h),strong:r(function(){return{type:"strong",children:[]}}),thematicBreak:r(function(){return{type:"thematicBreak"}})},exit:{atxHeading:l(),atxHeadingSequence:function(e){let t=this.stack[this.stack.length-1];if(!t.depth){let n=this.sliceSerialize(e).length;t.depth=n}},autolink:l(),autolinkEmail:function(e){c.call(this,e),this.stack[this.stack.length-1].url="mailto:"+this.sliceSerialize(e)},autolinkProtocol:function(e){c.call(this,e),this.stack[this.stack.length-1].url=this.sliceSerialize(e)},blockQuote:l(),characterEscapeValue:c,characterReferenceMarkerHexadecimal:p,characterReferenceMarkerNumeric:p,characterReferenceValue:function(e){let t;let n=this.sliceSerialize(e),r=this.data.characterReferenceType;r?(t=tO(n,"characterReferenceMarkerNumeric"===r?10:16),this.data.characterReferenceType=void 0):t=tl(n);let i=this.stack[this.stack.length-1];i.value+=t},characterReference:function(e){this.stack.pop().position.end=tD(e.end)},codeFenced:l(function(){let e=this.resume();this.stack[this.stack.length-1].value=e.replace(/^(\r?\n|\r)|(\r?\n|\r)$/g,""),this.data.flowCodeInside=void 0}),codeFencedFence:function(){this.data.flowCodeInside||(this.buffer(),this.data.flowCodeInside=!0)},codeFencedFenceInfo:function(){let e=this.resume();this.stack[this.stack.length-1].lang=e},codeFencedFenceMeta:function(){let e=this.resume();this.stack[this.stack.length-1].meta=e},codeFlowValue:c,codeIndented:l(function(){let e=this.resume();this.stack[this.stack.length-1].value=e.replace(/(\r?\n|\r)$/g,"")}),codeText:l(function(){let e=this.resume();this.stack[this.stack.length-1].value=e}),codeTextData:c,data:c,definition:l(),definitionDestinationString:function(){let e=this.resume();this.stack[this.stack.length-1].url=e},definitionLabelString:function(e){let t=this.resume(),n=this.stack[this.stack.length-1];n.label=t,n.identifier=e9(this.sliceSerialize(e)).toLowerCase()},definitionTitleString:function(){let e=this.resume();this.stack[this.stack.length-1].title=e},emphasis:l(),hardBreakEscape:l(d),hardBreakTrailing:l(d),htmlFlow:l(function(){let e=this.resume();this.stack[this.stack.length-1].value=e}),htmlFlowData:c,htmlText:l(function(){let e=this.resume();this.stack[this.stack.length-1].value=e}),htmlTextData:c,image:l(function(){let e=this.stack[this.stack.length-1];if(this.data.inReference){let t=this.data.referenceType||"shortcut";e.type+="Reference",e.referenceType=t,delete e.url,delete e.title}else delete e.identifier,delete e.label;this.data.referenceType=void 0}),label:function(){let e=this.stack[this.stack.length-1],t=this.resume(),n=this.stack[this.stack.length-1];if(this.data.inReference=!0,"link"===n.type){let t=e.children;n.children=t}else n.alt=t},labelText:function(e){let t=this.sliceSerialize(e),n=this.stack[this.stack.length-2];n.label=t.replace(tP,tN),n.identifier=e9(t).toLowerCase()},lineEnding:function(e){let n=this.stack[this.stack.length-1];if(this.data.atHardBreak){n.children[n.children.length-1].position.end=tD(e.end),this.data.atHardBreak=void 0;return}!this.data.setextHeadingSlurpLineEnding&&t.canContainEols.includes(n.type)&&(u.call(this,e),c.call(this,e))},link:l(function(){let e=this.stack[this.stack.length-1];if(this.data.inReference){let t=this.data.referenceType||"shortcut";e.type+="Reference",e.referenceType=t,delete e.url,delete e.title}else delete e.identifier,delete e.label;this.data.referenceType=void 0}),listItem:l(),listOrdered:l(),listUnordered:l(),paragraph:l(),referenceString:function(e){let t=this.resume(),n=this.stack[this.stack.length-1];n.label=t,n.identifier=e9(this.sliceSerialize(e)).toLowerCase(),this.data.referenceType="full"},resourceDestinationString:function(){let e=this.resume();this.stack[this.stack.length-1].url=e},resourceTitleString:function(){let e=this.resume();this.stack[this.stack.length-1].title=e},resource:function(){this.data.inReference=void 0},setextHeading:l(function(){this.data.setextHeadingSlurpLineEnding=void 0}),setextHeadingLineSequence:function(e){this.stack[this.stack.length-1].depth=61===this.sliceSerialize(e).codePointAt(0)?1:2},setextHeadingText:function(){this.data.setextHeadingSlurpLineEnding=!0},strong:l(),thematicBreak:l()}};(function e(t,n){let r=-1;for(;++r<n.length;){let i=n[r];Array.isArray(i)?e(t,i):function(e,t){let n;for(n in t)if(tL.call(t,n))switch(n){case"canContainEols":{let r=t[n];r&&e[n].push(...r);break}case"transforms":{let r=t[n];r&&e[n].push(...r);break}case"enter":case"exit":{let r=t[n];r&&Object.assign(e[n],r)}}}(t,i)}})(t,(e||{}).mdastExtensions||[]);let n={};return function(e){let r={type:"root",children:[]},l={stack:[r],tokenStack:[],config:t,enter:a,exit:o,buffer:i,resume:s,data:n},u=[],c=-1;for(;++c<e.length;)("listOrdered"===e[c][1].type||"listUnordered"===e[c][1].type)&&("enter"===e[c][0]?u.push(c):c=function(e,t,n){let r,i,a,l,o=t-1,s=-1,u=!1;for(;++o<=n;){let t=e[o];switch(t[1].type){case"listUnordered":case"listOrdered":case"blockQuote":"enter"===t[0]?s++:s--,l=void 0;break;case"lineEndingBlank":"enter"===t[0]&&(!r||l||s||a||(a=o),l=void 0);break;case"linePrefix":case"listItemValue":case"listItemMarker":case"listItemPrefix":case"listItemPrefixWhitespace":break;default:l=void 0}if(!s&&"enter"===t[0]&&"listItemPrefix"===t[1].type||-1===s&&"exit"===t[0]&&("listUnordered"===t[1].type||"listOrdered"===t[1].type)){if(r){let l=o;for(i=void 0;l--;){let t=e[l];if("lineEnding"===t[1].type||"lineEndingBlank"===t[1].type){if("exit"===t[0])continue;i&&(e[i][1].type="lineEndingBlank",u=!0),t[1].type="lineEnding",i=l}else if("linePrefix"===t[1].type||"blockQuotePrefix"===t[1].type||"blockQuotePrefixWhitespace"===t[1].type||"blockQuoteMarker"===t[1].type||"listItemIndent"===t[1].type);else break}a&&(!i||a<i)&&(r._spread=!0),r.end=Object.assign({},i?e[i][1].start:t[1].end),e.splice(i||o,0,["exit",r,t[2]]),o++,n++}if("listItemPrefix"===t[1].type){let i={type:"listItem",_spread:!1,start:Object.assign({},t[1].start),end:void 0};r=i,e.splice(o,0,["enter",i,t[2]]),o++,n++,a=void 0,l=!0}}}return e[t][1]._spread=u,n}(e,u.pop(),c));for(c=-1;++c<e.length;){let n=t[e[c][0]];tL.call(n,e[c][1].type)&&n[e[c][1].type].call(Object.assign({sliceSerialize:e[c][2].sliceSerialize},l),e[c][1])}if(l.tokenStack.length>0){let e=l.tokenStack[l.tokenStack.length-1];(e[1]||tR).call(l,void 0,e[0])}for(r.position={start:tD(e.length>0?e[0][1].start:{line:1,column:1,offset:0}),end:tD(e.length>0?e[e.length-2][1].end:{line:1,column:1,offset:0})},c=-1;++c<t.transforms.length;)r=t.transforms[c](r)||r;return r};function r(e,t){return function(n){a.call(this,e(n),n),t&&t.call(this,n)}}function i(){this.stack.push({type:"fragment",children:[]})}function a(e,t,n){this.stack[this.stack.length-1].children.push(e),this.stack.push(e),this.tokenStack.push([t,n||void 0]),e.position={start:tD(t.start),end:void 0}}function l(e){return function(t){e&&e.call(this,t),o.call(this,t)}}function o(e,t){let n=this.stack.pop(),r=this.tokenStack.pop();if(r)r[0].type!==e.type&&(t?t.call(this,e,r[0]):(r[1]||tR).call(this,e,r[0]));else throw Error("Cannot close `"+e.type+"` ("+W({start:e.start,end:e.end})+"): it’s not open");n.position.end=tD(e.end)}function s(){return eh(this.stack.pop(),"boolean"!=typeof ef.includeImageAlt||ef.includeImageAlt,"boolean"!=typeof ef.includeHtml||ef.includeHtml)}function u(e){let t=this.stack[this.stack.length-1].children,n=t[t.length-1];n&&"text"===n.type||((n={type:"text",value:""}).position={start:tD(e.start),end:void 0},t.push(n)),this.stack.push(n)}function c(e){let t=this.stack.pop();t.value+=this.sliceSerialize(e),t.position.end=tD(e.end)}function d(){this.data.atHardBreak=!0}function p(e){this.data.characterReferenceType=e.type}function f(){return{type:"code",lang:null,meta:null,value:""}}function h(){return{type:"heading",depth:0,children:[]}}function m(){return{type:"break"}}function g(){return{type:"html",value:""}}function y(){return{type:"link",title:null,url:"",children:[]}}function v(e){return{type:"list",ordered:"listOrdered"===e.type,start:null,spread:e._spread,children:[]}}})(a)(function(e){for(;!ek(e););return e}((function(e){let t={constructs:function(e){let t={},n=-1;for(;++n<e.length;)(function(e,t){let n;for(n in t){let r;let i=(e_.call(e,n)?e[n]:void 0)||(e[n]={}),a=t[n];if(a)for(r in a){e_.call(i,r)||(i[r]=[]);let e=a[r];(function(e,t){let n=-1,r=[];for(;++n<t.length;)("after"===t[n].add?e:r).push(t[n]);eg(e,0,0,r)})(i[r],Array.isArray(e)?e:e?[e]:[])}}})(t,e[n]);return t}([i,...(e||{}).extensions||[]]),content:n(ej),defined:[],document:n(eM),flow:n(eV),lazy:{},string:n(e$),text:n(eH)};return t;function n(e){return function(n){return function(e,t,n){let r={_bufferIndex:-1,_index:0,line:n&&n.line||1,column:n&&n.column||1,offset:n&&n.offset||0},i={},a=[],l=[],o=[],s={attempt:h(function(e,t){m(e,t.from)}),check:h(f),consume:function(e){eA(e)?(r.line++,r.column=1,r.offset+=-3===e?2:1,g()):-1!==e&&(r.column++,r.offset++),r._bufferIndex<0?r._index++:(r._bufferIndex++,r._bufferIndex===l[r._index].length&&(r._bufferIndex=-1,r._index++)),u.previous=e},enter:function(e,t){let n=t||{};return n.type=e,n.start=p(),u.events.push(["enter",n,u]),o.push(n),n},exit:function(e){let t=o.pop();return t.end=p(),u.events.push(["exit",t,u]),t},interrupt:h(f,{interrupt:!0})},u={code:null,containerState:{},defineSkip:function(e){i[e.line]=e.column,g()},events:[],now:p,parser:e,previous:null,sliceSerialize:function(e,t){return function(e,t){let n,r=-1,i=[];for(;++r<e.length;){let a;let l=e[r];if("string"==typeof l)a=l;else switch(l){case -5:a="\r";break;case -4:a="\n";break;case -3:a="\r\n";break;case -2:a=t?" ":"	";break;case -1:if(!t&&n)continue;a=" ";break;default:a=String.fromCharCode(l)}n=-2===l,i.push(a)}return i.join("")}(d(e),t)},sliceStream:d,write:function(e){return(l=ey(l,e),function(){let e;for(;r._index<l.length;){let n=l[r._index];if("string"==typeof n)for(e=r._index,r._bufferIndex<0&&(r._bufferIndex=0);r._index===e&&r._bufferIndex<n.length;){var t;t=n.charCodeAt(r._bufferIndex),c=c(t)}else c=c(n)}}(),null!==l[l.length-1])?[]:(m(t,0),u.events=tc(a,u.events,u),u.events)}},c=t.tokenize.call(u,s);return t.resolveAll&&a.push(t),u;function d(e){return function(e,t){let n;let r=t.start._index,i=t.start._bufferIndex,a=t.end._index,l=t.end._bufferIndex;if(r===a)n=[e[r].slice(i,l)];else{if(n=e.slice(r,a),i>-1){let e=n[0];"string"==typeof e?n[0]=e.slice(i):n.shift()}l>0&&n.push(e[a].slice(0,l))}return n}(l,e)}function p(){let{_bufferIndex:e,_index:t,line:n,column:i,offset:a}=r;return{_bufferIndex:e,_index:t,line:n,column:i,offset:a}}function f(e,t){t.restore()}function h(e,t){return function(n,i,a){let l,c,d,f;return Array.isArray(n)?h(n):"tokenize"in n?h([n]):function(e){let t=null!==e&&n[e],r=null!==e&&n.null;return h([...Array.isArray(t)?t:t?[t]:[],...Array.isArray(r)?r:r?[r]:[]])(e)};function h(e){return(l=e,c=0,0===e.length)?a:m(e[c])}function m(e){return function(n){return(f=function(){let e=p(),t=u.previous,n=u.currentConstruct,i=u.events.length,a=Array.from(o);return{from:i,restore:function(){r=e,u.previous=t,u.currentConstruct=n,u.events.length=i,o=a,g()}}}(),d=e,e.partial||(u.currentConstruct=e),e.name&&u.parser.constructs.disable.null.includes(e.name))?v(n):e.tokenize.call(t?Object.assign(Object.create(u),t):u,s,y,v)(n)}}function y(t){return e(d,f),i}function v(e){return(f.restore(),++c<l.length)?m(l[c]):a}}}function m(e,t){e.resolveAll&&!a.includes(e)&&a.push(e),e.resolve&&eg(u.events,t,u.events.length-t,e.resolve(u.events.slice(t),u)),e.resolveTo&&(u.events=e.resolveTo(u.events,u))}function g(){r.line in i&&r.column<2&&(r.column=i[r.line],r.offset+=i[r.line]-1)}}(t,e,n)}}})(a).document().write((o=1,s="",u=!0,function(e,t,n){let r,i,a,c,d;let p=[];for(e=s+("string"==typeof e?e.toString():new TextDecoder(t||void 0).decode(e)),a=0,s="",u&&(65279===e.charCodeAt(0)&&a++,u=void 0);a<e.length;){if(tA.lastIndex=a,c=(r=tA.exec(e))&&void 0!==r.index?r.index:e.length,d=e.charCodeAt(c),!r){s=e.slice(a);break}if(10===d&&a===c&&l)p.push(-3),l=void 0;else switch(l&&(p.push(-5),l=void 0),a<c&&(p.push(e.slice(a,c)),o+=c-a),d){case 0:p.push(65533),o++;break;case 9:for(i=4*Math.ceil(o/4),p.push(-2);o++<i;)p.push(-1);break;case 10:p.push(-4),o=1;break;default:l=!0,o=1}a=c+1}return n&&(l&&p.push(-5),s&&p.push(s),p.push(null)),p})(n,r,!0))))}}let tM="object"==typeof self?self:globalThis,tz=(e,t)=>{let n=(t,n)=>(e.set(n,t),t),r=i=>{if(e.has(i))return e.get(i);let[a,l]=t[i];switch(a){case 0:case -1:return n(l,i);case 1:{let e=n([],i);for(let t of l)e.push(r(t));return e}case 2:{let e=n({},i);for(let[t,n]of l)e[r(t)]=r(n);return e}case 3:return n(new Date(l),i);case 4:{let{source:e,flags:t}=l;return n(new RegExp(e,t),i)}case 5:{let e=n(new Map,i);for(let[t,n]of l)e.set(r(t),r(n));return e}case 6:{let e=n(new Set,i);for(let t of l)e.add(r(t));return e}case 7:{let{name:e,message:t}=l;return n(new tM[e](t),i)}case 8:return n(BigInt(l),i);case"BigInt":return n(Object(BigInt(l)),i);case"ArrayBuffer":return n(new Uint8Array(l).buffer,l);case"DataView":{let{buffer:e}=new Uint8Array(l);return n(new DataView(e),l)}}return n(new tM[a](l),i)};return r},tF=e=>tz(new Map,e)(0),{toString:tZ}={},{keys:tB}=Object,tV=e=>{let t=typeof e;if("object"!==t||!e)return[0,t];let n=tZ.call(e).slice(8,-1);switch(n){case"Array":return[1,""];case"Object":return[2,""];case"Date":return[3,""];case"RegExp":return[4,""];case"Map":return[5,""];case"Set":return[6,""];case"DataView":return[1,n]}return n.includes("Array")?[1,n]:n.includes("Error")?[7,n]:[2,n]},tU=([e,t])=>0===e&&("function"===t||"symbol"===t),t$=(e,t,n,r)=>{let i=(e,t)=>{let i=r.push(e)-1;return n.set(t,i),i},a=r=>{if(n.has(r))return n.get(r);let[l,o]=tV(r);switch(l){case 0:{let t=r;switch(o){case"bigint":l=8,t=r.toString();break;case"function":case"symbol":if(e)throw TypeError("unable to serialize "+o);t=null;break;case"undefined":return i([-1],r)}return i([l,t],r)}case 1:{if(o){let e=r;return"DataView"===o?e=new Uint8Array(r.buffer):"ArrayBuffer"===o&&(e=new Uint8Array(r)),i([o,[...e]],r)}let e=[],t=i([l,e],r);for(let t of r)e.push(a(t));return t}case 2:{if(o)switch(o){case"BigInt":return i([o,r.toString()],r);case"Boolean":case"Number":case"String":return i([o,r.valueOf()],r)}if(t&&"toJSON"in r)return a(r.toJSON());let n=[],s=i([l,n],r);for(let t of tB(r))(e||!tU(tV(r[t])))&&n.push([a(t),a(r[t])]);return s}case 3:return i([l,r.toISOString()],r);case 4:{let{source:e,flags:t}=r;return i([l,{source:e,flags:t}],r)}case 5:{let t=[],n=i([l,t],r);for(let[n,i]of r)(e||!(tU(tV(n))||tU(tV(i))))&&t.push([a(n),a(i)]);return n}case 6:{let t=[],n=i([l,t],r);for(let n of r)(e||!tU(tV(n)))&&t.push(a(n));return n}}let{message:s}=r;return i([l,{name:o,message:s}],r)};return a},tH=(e,{json:t,lossy:n}={})=>{let r=[];return t$(!(t||n),!!t,new Map,r)(e),r},tq="function"==typeof structuredClone?(e,t)=>t&&("json"in t||"lossy"in t)?tF(tH(e,t)):structuredClone(e):(e,t)=>tF(tH(e,t));function tW(e){let t=[],n=-1,r=0,i=0;for(;++n<e.length;){let a=e.charCodeAt(n),l="";if(37===a&&ew(e.charCodeAt(n+1))&&ew(e.charCodeAt(n+2)))i=2;else if(a<128)/[!#$&-;=?-Z_a-z~]/.test(String.fromCharCode(a))||(l=String.fromCharCode(a));else if(a>55295&&a<57344){let t=e.charCodeAt(n+1);a<56320&&t>56319&&t<57344?(l=String.fromCharCode(a,t),i=1):l="�"}else l=String.fromCharCode(a);l&&(t.push(e.slice(r,n),encodeURIComponent(l)),r=n+i+1,l=""),i&&(n+=i,i=0)}return t.join("")+e.slice(r)}function tK(e,t){let n=[{type:"text",value:"↩"}];return t>1&&n.push({type:"element",tagName:"sup",properties:{},children:[{type:"text",value:String(t)}]}),n}function tY(e,t){return"Back to reference "+(e+1)+(t>1?"-"+t:"")}let tJ=function(e){if(null==e)return tX;if("function"==typeof e)return tQ(e);if("object"==typeof e)return Array.isArray(e)?function(e){let t=[],n=-1;for(;++n<e.length;)t[n]=tJ(e[n]);return tQ(function(...e){let n=-1;for(;++n<t.length;)if(t[n].apply(this,e))return!0;return!1})}(e):tQ(function(t){let n;for(n in e)if(t[n]!==e[n])return!1;return!0});if("string"==typeof e)return tQ(function(t){return t&&t.type===e});throw Error("Expected function, string, or object as test")};function tQ(e){return function(t,n,r){var i;return!!(null!==(i=t)&&"object"==typeof i&&"type"in i&&e.call(this,t,"number"==typeof n?n:void 0,r||void 0))}}function tX(){return!0}let tG=[];function t0(e,t,n,r){let i,a,l;"function"==typeof t&&"function"!=typeof n?(a=void 0,l=t,i=n):(a=t,l=n,i=r),function(e,t,n,r){let i;"function"==typeof t&&"function"!=typeof n?(r=n,n=t):i=t;let a=tJ(i),l=r?-1:1;(function e(i,o,s){let u=i&&"object"==typeof i?i:{};if("string"==typeof u.type){let e="string"==typeof u.tagName?u.tagName:"string"==typeof u.name?u.name:void 0;Object.defineProperty(c,"name",{value:"node ("+i.type+(e?"<"+e+">":"")+")"})}return c;function c(){var u;let c,d,p,f=tG;if((!t||a(i,o,s[s.length-1]||void 0))&&!1===(f=Array.isArray(u=n(i,s))?u:"number"==typeof u?[!0,u]:null==u?tG:[u])[0])return f;if("children"in i&&i.children&&i.children&&"skip"!==f[0])for(d=(r?i.children.length:-1)+l,p=s.concat(i);d>-1&&d<i.children.length;){if(!1===(c=e(i.children[d],d,p)())[0])return c;d="number"==typeof c[1]?c[1]:d+l}return f}})(e,void 0,[])()}(e,a,function(e,t){let n=t[t.length-1],r=n?n.children.indexOf(e):void 0;return l(e,r,n)},i)}function t1(e,t){let n=t.referenceType,r="]";if("collapsed"===n?r+="[]":"full"===n&&(r+="["+(t.label||t.identifier)+"]"),"imageReference"===t.type)return[{type:"text",value:"!["+t.alt+r}];let i=e.all(t),a=i[0];a&&"text"===a.type?a.value="["+a.value:i.unshift({type:"text",value:"["});let l=i[i.length-1];return l&&"text"===l.type?l.value+=r:i.push({type:"text",value:r}),i}function t4(e){let t=e.spread;return null==t?e.children.length>1:t}function t2(e,t,n){let r=0,i=e.length;if(t){let t=e.codePointAt(r);for(;9===t||32===t;)r++,t=e.codePointAt(r)}if(n){let t=e.codePointAt(i-1);for(;9===t||32===t;)i--,t=e.codePointAt(i-1)}return i>r?e.slice(r,i):""}let t9={blockquote:function(e,t){let n={type:"element",tagName:"blockquote",properties:{},children:e.wrap(e.all(t),!0)};return e.patch(t,n),e.applyData(t,n)},break:function(e,t){let n={type:"element",tagName:"br",properties:{},children:[]};return e.patch(t,n),[e.applyData(t,n),{type:"text",value:"\n"}]},code:function(e,t){let n=t.value?t.value+"\n":"",r={};t.lang&&(r.className=["language-"+t.lang]);let i={type:"element",tagName:"code",properties:r,children:[{type:"text",value:n}]};return t.meta&&(i.data={meta:t.meta}),e.patch(t,i),i={type:"element",tagName:"pre",properties:{},children:[i=e.applyData(t,i)]},e.patch(t,i),i},delete:function(e,t){let n={type:"element",tagName:"del",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},emphasis:function(e,t){let n={type:"element",tagName:"em",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},footnoteReference:function(e,t){let n;let r="string"==typeof e.options.clobberPrefix?e.options.clobberPrefix:"user-content-",i=String(t.identifier).toUpperCase(),a=tW(i.toLowerCase()),l=e.footnoteOrder.indexOf(i),o=e.footnoteCounts.get(i);void 0===o?(o=0,e.footnoteOrder.push(i),n=e.footnoteOrder.length):n=l+1,o+=1,e.footnoteCounts.set(i,o);let s={type:"element",tagName:"a",properties:{href:"#"+r+"fn-"+a,id:r+"fnref-"+a+(o>1?"-"+o:""),dataFootnoteRef:!0,ariaDescribedBy:["footnote-label"]},children:[{type:"text",value:String(n)}]};e.patch(t,s);let u={type:"element",tagName:"sup",properties:{},children:[s]};return e.patch(t,u),e.applyData(t,u)},heading:function(e,t){let n={type:"element",tagName:"h"+t.depth,properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},html:function(e,t){if(e.options.allowDangerousHtml){let n={type:"raw",value:t.value};return e.patch(t,n),e.applyData(t,n)}},imageReference:function(e,t){let n=String(t.identifier).toUpperCase(),r=e.definitionById.get(n);if(!r)return t1(e,t);let i={src:tW(r.url||""),alt:t.alt};null!==r.title&&void 0!==r.title&&(i.title=r.title);let a={type:"element",tagName:"img",properties:i,children:[]};return e.patch(t,a),e.applyData(t,a)},image:function(e,t){let n={src:tW(t.url)};null!==t.alt&&void 0!==t.alt&&(n.alt=t.alt),null!==t.title&&void 0!==t.title&&(n.title=t.title);let r={type:"element",tagName:"img",properties:n,children:[]};return e.patch(t,r),e.applyData(t,r)},inlineCode:function(e,t){let n={type:"text",value:t.value.replace(/\r?\n|\r/g," ")};e.patch(t,n);let r={type:"element",tagName:"code",properties:{},children:[n]};return e.patch(t,r),e.applyData(t,r)},linkReference:function(e,t){let n=String(t.identifier).toUpperCase(),r=e.definitionById.get(n);if(!r)return t1(e,t);let i={href:tW(r.url||"")};null!==r.title&&void 0!==r.title&&(i.title=r.title);let a={type:"element",tagName:"a",properties:i,children:e.all(t)};return e.patch(t,a),e.applyData(t,a)},link:function(e,t){let n={href:tW(t.url)};null!==t.title&&void 0!==t.title&&(n.title=t.title);let r={type:"element",tagName:"a",properties:n,children:e.all(t)};return e.patch(t,r),e.applyData(t,r)},listItem:function(e,t,n){let r=e.all(t),i=n?function(e){let t=!1;if("list"===e.type){t=e.spread||!1;let n=e.children,r=-1;for(;!t&&++r<n.length;)t=t4(n[r])}return t}(n):t4(t),a={},l=[];if("boolean"==typeof t.checked){let e;let n=r[0];n&&"element"===n.type&&"p"===n.tagName?e=n:(e={type:"element",tagName:"p",properties:{},children:[]},r.unshift(e)),e.children.length>0&&e.children.unshift({type:"text",value:" "}),e.children.unshift({type:"element",tagName:"input",properties:{type:"checkbox",checked:t.checked,disabled:!0},children:[]}),a.className=["task-list-item"]}let o=-1;for(;++o<r.length;){let e=r[o];(i||0!==o||"element"!==e.type||"p"!==e.tagName)&&l.push({type:"text",value:"\n"}),"element"!==e.type||"p"!==e.tagName||i?l.push(e):l.push(...e.children)}let s=r[r.length-1];s&&(i||"element"!==s.type||"p"!==s.tagName)&&l.push({type:"text",value:"\n"});let u={type:"element",tagName:"li",properties:a,children:l};return e.patch(t,u),e.applyData(t,u)},list:function(e,t){let n={},r=e.all(t),i=-1;for("number"==typeof t.start&&1!==t.start&&(n.start=t.start);++i<r.length;){let e=r[i];if("element"===e.type&&"li"===e.tagName&&e.properties&&Array.isArray(e.properties.className)&&e.properties.className.includes("task-list-item")){n.className=["contains-task-list"];break}}let a={type:"element",tagName:t.ordered?"ol":"ul",properties:n,children:e.wrap(r,!0)};return e.patch(t,a),e.applyData(t,a)},paragraph:function(e,t){let n={type:"element",tagName:"p",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},root:function(e,t){let n={type:"root",children:e.wrap(e.all(t))};return e.patch(t,n),e.applyData(t,n)},strong:function(e,t){let n={type:"element",tagName:"strong",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},table:function(e,t){let n=e.all(t),r=n.shift(),i=[];if(r){let n={type:"element",tagName:"thead",properties:{},children:e.wrap([r],!0)};e.patch(t.children[0],n),i.push(n)}if(n.length>0){let r={type:"element",tagName:"tbody",properties:{},children:e.wrap(n,!0)},a=H(t.children[1]),l=$(t.children[t.children.length-1]);a&&l&&(r.position={start:a,end:l}),i.push(r)}let a={type:"element",tagName:"table",properties:{},children:e.wrap(i,!0)};return e.patch(t,a),e.applyData(t,a)},tableCell:function(e,t){let n={type:"element",tagName:"td",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},tableRow:function(e,t,n){let r=n?n.children:void 0,i=0===(r?r.indexOf(t):1)?"th":"td",a=n&&"table"===n.type?n.align:void 0,l=a?a.length:t.children.length,o=-1,s=[];for(;++o<l;){let n=t.children[o],r={},l=a?a[o]:void 0;l&&(r.align=l);let u={type:"element",tagName:i,properties:r,children:[]};n&&(u.children=e.all(n),e.patch(n,u),u=e.applyData(n,u)),s.push(u)}let u={type:"element",tagName:"tr",properties:{},children:e.wrap(s,!0)};return e.patch(t,u),e.applyData(t,u)},text:function(e,t){let n={type:"text",value:function(e){let t=String(e),n=/\r?\n|\r/g,r=n.exec(t),i=0,a=[];for(;r;)a.push(t2(t.slice(i,r.index),i>0,!0),r[0]),i=r.index+r[0].length,r=n.exec(t);return a.push(t2(t.slice(i),i>0,!1)),a.join("")}(String(t.value))};return e.patch(t,n),e.applyData(t,n)},thematicBreak:function(e,t){let n={type:"element",tagName:"hr",properties:{},children:[]};return e.patch(t,n),e.applyData(t,n)},toml:t5,yaml:t5,definition:t5,footnoteDefinition:t5};function t5(){}let t6={}.hasOwnProperty,t3={};function t7(e,t){e.position&&(t.position=function(e){let t=H(e),n=$(e);if(t&&n)return{start:t,end:n}}(e))}function t8(e,t){let n=t;if(e&&e.data){let t=e.data.hName,r=e.data.hChildren,i=e.data.hProperties;"string"==typeof t&&("element"===n.type?n.tagName=t:n={type:"element",tagName:t,properties:{},children:"children"in n?n.children:[n]}),"element"===n.type&&i&&Object.assign(n.properties,tq(i)),"children"in n&&n.children&&null!=r&&(n.children=r)}return n}function ne(e,t){let n=[],r=-1;for(t&&n.push({type:"text",value:"\n"});++r<e.length;)r&&n.push({type:"text",value:"\n"}),n.push(e[r]);return t&&e.length>0&&n.push({type:"text",value:"\n"}),n}function nt(e){let t=0,n=e.charCodeAt(t);for(;9===n||32===n;)t++,n=e.charCodeAt(t);return e.slice(t)}function nn(e,t){let n=function(e,t){let n=t||t3,r=new Map,i=new Map,a={all:function(e){let t=[];if("children"in e){let n=e.children,r=-1;for(;++r<n.length;){let i=a.one(n[r],e);if(i){if(r&&"break"===n[r-1].type&&(Array.isArray(i)||"text"!==i.type||(i.value=nt(i.value)),!Array.isArray(i)&&"element"===i.type)){let e=i.children[0];e&&"text"===e.type&&(e.value=nt(e.value))}Array.isArray(i)?t.push(...i):t.push(i)}}}return t},applyData:t8,definitionById:r,footnoteById:i,footnoteCounts:new Map,footnoteOrder:[],handlers:{...t9,...n.handlers},one:function(e,t){let n=e.type,r=a.handlers[n];if(t6.call(a.handlers,n)&&r)return r(a,e,t);if(a.options.passThrough&&a.options.passThrough.includes(n)){if("children"in e){let{children:t,...n}=e,r=tq(n);return r.children=a.all(e),r}return tq(e)}return(a.options.unknownHandler||function(e,t){let n=t.data||{},r="value"in t&&!(t6.call(n,"hProperties")||t6.call(n,"hChildren"))?{type:"text",value:t.value}:{type:"element",tagName:"div",properties:{},children:e.all(t)};return e.patch(t,r),e.applyData(t,r)})(a,e,t)},options:n,patch:t7,wrap:ne};return t0(e,function(e){if("definition"===e.type||"footnoteDefinition"===e.type){let t="definition"===e.type?r:i,n=String(e.identifier).toUpperCase();t.has(n)||t.set(n,e)}}),a}(e,t),r=n.one(e,void 0),i=function(e){let t="string"==typeof e.options.clobberPrefix?e.options.clobberPrefix:"user-content-",n=e.options.footnoteBackContent||tK,r=e.options.footnoteBackLabel||tY,i=e.options.footnoteLabel||"Footnotes",a=e.options.footnoteLabelTagName||"h2",l=e.options.footnoteLabelProperties||{className:["sr-only"]},o=[],s=-1;for(;++s<e.footnoteOrder.length;){let i=e.footnoteById.get(e.footnoteOrder[s]);if(!i)continue;let a=e.all(i),l=String(i.identifier).toUpperCase(),u=tW(l.toLowerCase()),c=0,d=[],p=e.footnoteCounts.get(l);for(;void 0!==p&&++c<=p;){d.length>0&&d.push({type:"text",value:" "});let e="string"==typeof n?n:n(s,c);"string"==typeof e&&(e={type:"text",value:e}),d.push({type:"element",tagName:"a",properties:{href:"#"+t+"fnref-"+u+(c>1?"-"+c:""),dataFootnoteBackref:"",ariaLabel:"string"==typeof r?r:r(s,c),className:["data-footnote-backref"]},children:Array.isArray(e)?e:[e]})}let f=a[a.length-1];if(f&&"element"===f.type&&"p"===f.tagName){let e=f.children[f.children.length-1];e&&"text"===e.type?e.value+=" ":f.children.push({type:"text",value:" "}),f.children.push(...d)}else a.push(...d);let h={type:"element",tagName:"li",properties:{id:t+"fn-"+u},children:e.wrap(a,!0)};e.patch(i,h),o.push(h)}if(0!==o.length)return{type:"element",tagName:"section",properties:{dataFootnotes:!0,className:["footnotes"]},children:[{type:"element",tagName:a,properties:{...tq(l),id:"footnote-label"},children:[{type:"text",value:i}]},{type:"text",value:"\n"},{type:"element",tagName:"ol",properties:{},children:e.wrap(o,!0)},{type:"text",value:"\n"}]}}(n),a=Array.isArray(r)?{type:"root",children:r}:r||{type:"root",children:[]};return i&&a.children.push({type:"text",value:"\n"},i),a}function nr(e,t){return e&&"run"in e?async function(n,r){let i=nn(n,{file:r,...t});await e.run(i,r)}:function(n,r){return nn(n,{file:r,...e||t})}}function ni(e){if(e)throw e}var na=n(9721);function nl(e){if("object"!=typeof e||null===e)return!1;let t=Object.getPrototypeOf(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)}let no={basename:function(e,t){let n;if(void 0!==t&&"string"!=typeof t)throw TypeError('"ext" argument must be a string');ns(e);let r=0,i=-1,a=e.length;if(void 0===t||0===t.length||t.length>e.length){for(;a--;)if(47===e.codePointAt(a)){if(n){r=a+1;break}}else i<0&&(n=!0,i=a+1);return i<0?"":e.slice(r,i)}if(t===e)return"";let l=-1,o=t.length-1;for(;a--;)if(47===e.codePointAt(a)){if(n){r=a+1;break}}else l<0&&(n=!0,l=a+1),o>-1&&(e.codePointAt(a)===t.codePointAt(o--)?o<0&&(i=a):(o=-1,i=l));return r===i?i=l:i<0&&(i=e.length),e.slice(r,i)},dirname:function(e){let t;if(ns(e),0===e.length)return".";let n=-1,r=e.length;for(;--r;)if(47===e.codePointAt(r)){if(t){n=r;break}}else t||(t=!0);return n<0?47===e.codePointAt(0)?"/":".":1===n&&47===e.codePointAt(0)?"//":e.slice(0,n)},extname:function(e){let t;ns(e);let n=e.length,r=-1,i=0,a=-1,l=0;for(;n--;){let o=e.codePointAt(n);if(47===o){if(t){i=n+1;break}continue}r<0&&(t=!0,r=n+1),46===o?a<0?a=n:1!==l&&(l=1):a>-1&&(l=-1)}return a<0||r<0||0===l||1===l&&a===r-1&&a===i+1?"":e.slice(a,r)},join:function(...e){let t,n=-1;for(;++n<e.length;)ns(e[n]),e[n]&&(t=void 0===t?e[n]:t+"/"+e[n]);return void 0===t?".":function(e){ns(e);let t=47===e.codePointAt(0),n=function(e,t){let n,r,i="",a=0,l=-1,o=0,s=-1;for(;++s<=e.length;){if(s<e.length)n=e.codePointAt(s);else if(47===n)break;else n=47;if(47===n){if(l===s-1||1===o);else if(l!==s-1&&2===o){if(i.length<2||2!==a||46!==i.codePointAt(i.length-1)||46!==i.codePointAt(i.length-2)){if(i.length>2){if((r=i.lastIndexOf("/"))!==i.length-1){r<0?(i="",a=0):a=(i=i.slice(0,r)).length-1-i.lastIndexOf("/"),l=s,o=0;continue}}else if(i.length>0){i="",a=0,l=s,o=0;continue}}t&&(i=i.length>0?i+"/..":"..",a=2)}else i.length>0?i+="/"+e.slice(l+1,s):i=e.slice(l+1,s),a=s-l-1;l=s,o=0}else 46===n&&o>-1?o++:o=-1}return i}(e,!t);return 0!==n.length||t||(n="."),n.length>0&&47===e.codePointAt(e.length-1)&&(n+="/"),t?"/"+n:n}(t)},sep:"/"};function ns(e){if("string"!=typeof e)throw TypeError("Path must be a string. Received "+JSON.stringify(e))}let nu={cwd:function(){return"/"}};function nc(e){return!!(null!==e&&"object"==typeof e&&"href"in e&&e.href&&"protocol"in e&&e.protocol&&void 0===e.auth)}let nd=["history","path","basename","stem","extname","dirname"];class np{constructor(e){let t,n;t=e?nc(e)?{path:e}:"string"==typeof e||function(e){return!!(e&&"object"==typeof e&&"byteLength"in e&&"byteOffset"in e)}(e)?{value:e}:e:{},this.cwd="cwd"in t?"":nu.cwd(),this.data={},this.history=[],this.messages=[],this.value,this.map,this.result,this.stored;let r=-1;for(;++r<nd.length;){let e=nd[r];e in t&&void 0!==t[e]&&null!==t[e]&&(this[e]="history"===e?[...t[e]]:t[e])}for(n in t)nd.includes(n)||(this[n]=t[n])}get basename(){return"string"==typeof this.path?no.basename(this.path):void 0}set basename(e){nh(e,"basename"),nf(e,"basename"),this.path=no.join(this.dirname||"",e)}get dirname(){return"string"==typeof this.path?no.dirname(this.path):void 0}set dirname(e){nm(this.basename,"dirname"),this.path=no.join(e||"",this.basename)}get extname(){return"string"==typeof this.path?no.extname(this.path):void 0}set extname(e){if(nf(e,"extname"),nm(this.dirname,"extname"),e){if(46!==e.codePointAt(0))throw Error("`extname` must start with `.`");if(e.includes(".",1))throw Error("`extname` cannot contain multiple dots")}this.path=no.join(this.dirname,this.stem+(e||""))}get path(){return this.history[this.history.length-1]}set path(e){nc(e)&&(e=function(e){if("string"==typeof e)e=new URL(e);else if(!nc(e)){let t=TypeError('The "path" argument must be of type string or an instance of URL. Received `'+e+"`");throw t.code="ERR_INVALID_ARG_TYPE",t}if("file:"!==e.protocol){let e=TypeError("The URL must be of scheme file");throw e.code="ERR_INVALID_URL_SCHEME",e}return function(e){if(""!==e.hostname){let e=TypeError('File URL host must be "localhost" or empty on darwin');throw e.code="ERR_INVALID_FILE_URL_HOST",e}let t=e.pathname,n=-1;for(;++n<t.length;)if(37===t.codePointAt(n)&&50===t.codePointAt(n+1)){let e=t.codePointAt(n+2);if(70===e||102===e){let e=TypeError("File URL path must not include encoded / characters");throw e.code="ERR_INVALID_FILE_URL_PATH",e}}return decodeURIComponent(t)}(e)}(e)),nh(e,"path"),this.path!==e&&this.history.push(e)}get stem(){return"string"==typeof this.path?no.basename(this.path,this.extname):void 0}set stem(e){nh(e,"stem"),nf(e,"stem"),this.path=no.join(this.dirname||"",e+(this.extname||""))}fail(e,t,n){let r=this.message(e,t,n);throw r.fatal=!0,r}info(e,t,n){let r=this.message(e,t,n);return r.fatal=void 0,r}message(e,t,n){let r=new Q(e,t,n);return this.path&&(r.name=this.path+":"+r.name,r.file=this.path),r.fatal=!1,this.messages.push(r),r}toString(e){return void 0===this.value?"":"string"==typeof this.value?this.value:new TextDecoder(e||void 0).decode(this.value)}}function nf(e,t){if(e&&e.includes(no.sep))throw Error("`"+t+"` cannot be a path: did not expect `"+no.sep+"`")}function nh(e,t){if(!e)throw Error("`"+t+"` cannot be empty")}function nm(e,t){if(!e)throw Error("Setting `"+t+"` requires `path` to be set too")}let ng=function(e){let t=this.constructor.prototype,n=t[e],r=function(){return n.apply(r,arguments)};return Object.setPrototypeOf(r,t),r},ny={}.hasOwnProperty;class nv extends ng{constructor(){super("copy"),this.Compiler=void 0,this.Parser=void 0,this.attachers=[],this.compiler=void 0,this.freezeIndex=-1,this.frozen=void 0,this.namespace={},this.parser=void 0,this.transformers=function(){let e=[],t={run:function(...t){let n=-1,r=t.pop();if("function"!=typeof r)throw TypeError("Expected function as last argument, not "+r);(function i(a,...l){let o=e[++n],s=-1;if(a){r(a);return}for(;++s<t.length;)(null===l[s]||void 0===l[s])&&(l[s]=t[s]);t=l,o?(function(e,t){let n;return function(...t){let a;let l=e.length>t.length;l&&t.push(r);try{a=e.apply(this,t)}catch(e){if(l&&n)throw e;return r(e)}l||(a&&a.then&&"function"==typeof a.then?a.then(i,r):a instanceof Error?r(a):i(a))};function r(e,...i){n||(n=!0,t(e,...i))}function i(e){r(null,e)}})(o,i)(...l):r(null,...l)})(null,...t)},use:function(n){if("function"!=typeof n)throw TypeError("Expected `middelware` to be a function, not "+n);return e.push(n),t}};return t}()}copy(){let e=new nv,t=-1;for(;++t<this.attachers.length;){let n=this.attachers[t];e.use(...n)}return e.data(na(!0,{},this.namespace)),e}data(e,t){return"string"==typeof e?2==arguments.length?(nb("data",this.frozen),this.namespace[e]=t,this):ny.call(this.namespace,e)&&this.namespace[e]||void 0:e?(nb("data",this.frozen),this.namespace=e,this):this.namespace}freeze(){if(this.frozen)return this;for(;++this.freezeIndex<this.attachers.length;){let[e,...t]=this.attachers[this.freezeIndex];if(!1===t[0])continue;!0===t[0]&&(t[0]=void 0);let n=e.call(this,...t);"function"==typeof n&&this.transformers.use(n)}return this.frozen=!0,this.freezeIndex=Number.POSITIVE_INFINITY,this}parse(e){this.freeze();let t=nT(e),n=this.parser||this.Parser;return nk("parse",n),n(String(t),t)}process(e,t){let n=this;return this.freeze(),nk("process",this.parser||this.Parser),n_("process",this.compiler||this.Compiler),t?r(void 0,t):new Promise(r);function r(r,i){let a=nT(e),l=n.parse(a);function o(e,n){e||!n?i(e):r?r(n):t(void 0,n)}n.run(l,a,function(e,t,r){if(e||!t||!r)return o(e);let i=n.stringify(t,r);"string"==typeof i||i&&"object"==typeof i&&"byteLength"in i&&"byteOffset"in i?r.value=i:r.result=i,o(e,r)})}}processSync(e){let t,n=!1;return this.freeze(),nk("processSync",this.parser||this.Parser),n_("processSync",this.compiler||this.Compiler),this.process(e,function(e,r){n=!0,ni(e),t=r}),nS("processSync","process",n),t}run(e,t,n){nw(e),this.freeze();let r=this.transformers;return n||"function"!=typeof t||(n=t,t=void 0),n?i(void 0,n):new Promise(i);function i(i,a){let l=nT(t);r.run(e,l,function(t,r,l){let o=r||e;t?a(t):i?i(o):n(void 0,o,l)})}}runSync(e,t){let n,r=!1;return this.run(e,t,function(e,t){ni(e),n=t,r=!0}),nS("runSync","run",r),n}stringify(e,t){this.freeze();let n=nT(t),r=this.compiler||this.Compiler;return n_("stringify",r),nw(e),r(e,n)}use(e,...t){let n=this.attachers,r=this.namespace;if(nb("use",this.frozen),null==e);else if("function"==typeof e)l(e,t);else if("object"==typeof e)Array.isArray(e)?a(e):i(e);else throw TypeError("Expected usable value, not `"+e+"`");return this;function i(e){if(!("plugins"in e)&&!("settings"in e))throw Error("Expected usable value but received an empty preset, which is probably a mistake: presets typically come with `plugins` and sometimes with `settings`, but this has neither");a(e.plugins),e.settings&&(r.settings=na(!0,r.settings,e.settings))}function a(e){let t=-1;if(null==e);else if(Array.isArray(e))for(;++t<e.length;)!function(e){if("function"==typeof e)l(e,[]);else if("object"==typeof e){if(Array.isArray(e)){let[t,...n]=e;l(t,n)}else i(e)}else throw TypeError("Expected usable value, not `"+e+"`")}(e[t]);else throw TypeError("Expected a list of plugins, not `"+e+"`")}function l(e,t){let r=-1,i=-1;for(;++r<n.length;)if(n[r][0]===e){i=r;break}if(-1===i)n.push([e,...t]);else if(t.length>0){let[r,...a]=t,l=n[i][1];nl(l)&&nl(r)&&(r=na(!0,l,r)),n[i]=[e,r,...a]}}}}let nx=new nv().freeze();function nk(e,t){if("function"!=typeof t)throw TypeError("Cannot `"+e+"` without `parser`")}function n_(e,t){if("function"!=typeof t)throw TypeError("Cannot `"+e+"` without `compiler`")}function nb(e,t){if(t)throw Error("Cannot call `"+e+"` on a frozen processor.\nCreate a new processor first, by calling it: use `processor()` instead of `processor`.")}function nw(e){if(!nl(e)||"string"!=typeof e.type)throw TypeError("Expected node, got `"+e+"`")}function nS(e,t,n){if(!n)throw Error("`"+e+"` finished async. Use `"+t+"` instead")}function nT(e){return e&&"object"==typeof e&&"message"in e&&"messages"in e?e:new np(e)}let nC=[],nE={allowDangerousHtml:!0},nI=/^(https?|ircs?|mailto|xmpp)$/i,nA=[{from:"astPlugins",id:"remove-buggy-html-in-markdown-parser"},{from:"allowDangerousHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"allowNode",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowElement"},{from:"allowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowedElements"},{from:"className",id:"remove-classname"},{from:"disallowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"disallowedElements"},{from:"escapeHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"includeElementIndex",id:"#remove-includeelementindex"},{from:"includeNodeIndex",id:"change-includenodeindex-to-includeelementindex"},{from:"linkTarget",id:"remove-linktarget"},{from:"plugins",id:"change-plugins-to-remarkplugins",to:"remarkPlugins"},{from:"rawSourcePos",id:"#remove-rawsourcepos"},{from:"renderers",id:"change-renderers-to-components",to:"components"},{from:"source",id:"change-source-to-children",to:"children"},{from:"sourcePos",id:"#remove-sourcepos"},{from:"transformImageUri",id:"#add-urltransform",to:"urlTransform"},{from:"transformLinkUri",id:"#add-urltransform",to:"urlTransform"}];function nO(e){let t=function(e){let t=e.rehypePlugins||nC,n=e.remarkPlugins||nC,r=e.remarkRehypeOptions?{...e.remarkRehypeOptions,...nE}:nE;return nx().use(tj).use(n).use(nr,r).use(t)}(e),n=function(e){let t=e.children||"",n=new np;return"string"==typeof t&&(n.value=t),n}(e);return function(e,t){let n=t.allowedElements,r=t.allowElement,i=t.components,a=t.disallowedElements,l=t.skipHtml,o=t.unwrapDisallowed,s=t.urlTransform||nP;for(let e of nA)Object.hasOwn(t,e.from)&&(e.from,e.to&&e.to,e.id);return t0(e,function(e,t,i){if("raw"===e.type&&i&&"number"==typeof t)return l?i.children.splice(t,1):i.children[t]={type:"text",value:e.value},t;if("element"===e.type){let t;for(t in ed)if(Object.hasOwn(ed,t)&&Object.hasOwn(e.properties,t)){let n=e.properties[t],r=ed[t];(null===r||r.includes(e.tagName))&&(e.properties[t]=s(String(n||""),t,e))}}if("element"===e.type){let l=n?!n.includes(e.tagName):!!a&&a.includes(e.tagName);if(!l&&r&&"number"==typeof t&&(l=!r(e,t,i)),l&&i&&"number"==typeof t)return o&&e.children?i.children.splice(t,1,...e.children):i.children.splice(t,1),t}}),function(e,t){var n,r,i;let a;if(!t||void 0===t.Fragment)throw TypeError("Expected `Fragment` in options");let l=t.filePath||void 0;if(t.development){if("function"!=typeof t.jsxDEV)throw TypeError("Expected `jsxDEV` in options when `development: true`");n=t.jsxDEV,a=function(e,t,r,i){let a=Array.isArray(r.children),o=H(e);return n(t,r,i,a,{columnNumber:o?o.column-1:void 0,fileName:l,lineNumber:o?o.line:void 0},void 0)}}else{if("function"!=typeof t.jsx)throw TypeError("Expected `jsx` in production options");if("function"!=typeof t.jsxs)throw TypeError("Expected `jsxs` in production options");r=t.jsx,i=t.jsxs,a=function(e,t,n,a){let l=Array.isArray(n.children)?i:r;return a?l(t,n,a):l(t,n)}}let o={Fragment:t.Fragment,ancestors:[],components:t.components||{},create:a,elementAttributeNameCase:t.elementAttributeNameCase||"react",evaluater:t.createEvaluater?t.createEvaluater():void 0,filePath:l,ignoreInvalidStyle:t.ignoreInvalidStyle||!1,passKeys:!1!==t.passKeys,passNode:t.passNode||!1,schema:"svg"===t.space?j:R,stylePropertyNameCase:t.stylePropertyNameCase||"dom",tableCellAlignToStyle:!1!==t.tableCellAlignToStyle},s=ei(o,e,void 0);return s&&"string"!=typeof s?s:o.create(e,o.Fragment,{children:s||void 0},void 0)}(e,{Fragment:ep.Fragment,components:i,ignoreInvalidStyle:!0,jsx:ep.jsx,jsxs:ep.jsxs,passKeys:!0,passNode:!0})}(t.runSync(t.parse(n),n),e)}function nP(e){let t=e.indexOf(":"),n=e.indexOf("?"),r=e.indexOf("#"),i=e.indexOf("/");return -1===t||-1!==i&&t>i||-1!==n&&t>n||-1!==r&&t>r||nI.test(e.slice(0,t))?e:""}},842:(e,t,n)=>{"use strict";let r;n.d(t,{z:()=>c});var i,a,l,o,s,u,c={};n.r(c),n.d(c,{BRAND:()=>ej,DIRTY:()=>T,EMPTY_PATH:()=>_,INVALID:()=>S,NEVER:()=>tv,OK:()=>C,ParseStatus:()=>w,Schema:()=>j,ZodAny:()=>eu,ZodArray:()=>ef,ZodBigInt:()=>er,ZodBoolean:()=>ei,ZodBranded:()=>eM,ZodCatch:()=>eD,ZodDate:()=>ea,ZodDefault:()=>eL,ZodDiscriminatedUnion:()=>ey,ZodEffects:()=>eO,ZodEnum:()=>eE,ZodError:()=>m,ZodFirstPartyTypeKind:()=>u,ZodFunction:()=>ew,ZodIntersection:()=>ev,ZodIssueCode:()=>f,ZodLazy:()=>eS,ZodLiteral:()=>eT,ZodMap:()=>e_,ZodNaN:()=>eR,ZodNativeEnum:()=>eI,ZodNever:()=>ed,ZodNull:()=>es,ZodNullable:()=>eN,ZodNumber:()=>en,ZodObject:()=>eh,ZodOptional:()=>eP,ZodParsedType:()=>d,ZodPipeline:()=>ez,ZodPromise:()=>eA,ZodReadonly:()=>eF,ZodRecord:()=>ek,ZodSchema:()=>j,ZodSet:()=>eb,ZodString:()=>et,ZodSymbol:()=>el,ZodTransformer:()=>eO,ZodTuple:()=>ex,ZodType:()=>j,ZodUndefined:()=>eo,ZodUnion:()=>em,ZodUnknown:()=>ec,ZodVoid:()=>ep,addIssueToContext:()=>b,any:()=>eG,array:()=>e2,bigint:()=>eW,boolean:()=>eK,coerce:()=>ty,custom:()=>eB,date:()=>eY,datetimeRegex:()=>ee,defaultErrorMap:()=>g,discriminatedUnion:()=>e3,effect:()=>tu,enum:()=>tl,function:()=>tr,getErrorMap:()=>x,getParsedType:()=>p,instanceof:()=>eU,intersection:()=>e7,isAborted:()=>E,isAsync:()=>O,isDirty:()=>I,isValid:()=>A,late:()=>eV,lazy:()=>ti,literal:()=>ta,makeIssue:()=>k,map:()=>tt,nan:()=>eq,nativeEnum:()=>to,never:()=>e1,null:()=>eX,nullable:()=>td,number:()=>eH,object:()=>e9,objectUtil:()=>a,oboolean:()=>tg,onumber:()=>tm,optional:()=>tc,ostring:()=>th,pipeline:()=>tf,preprocess:()=>tp,promise:()=>ts,quotelessJson:()=>h,record:()=>te,set:()=>tn,setErrorMap:()=>v,strictObject:()=>e5,string:()=>e$,symbol:()=>eJ,transformer:()=>tu,tuple:()=>e8,undefined:()=>eQ,union:()=>e6,unknown:()=>e0,util:()=>i,void:()=>e4}),function(e){e.assertEqual=e=>{},e.assertIs=function(e){},e.assertNever=function(e){throw Error()},e.arrayToEnum=e=>{let t={};for(let n of e)t[n]=n;return t},e.getValidEnumValues=t=>{let n=e.objectKeys(t).filter(e=>"number"!=typeof t[t[e]]),r={};for(let e of n)r[e]=t[e];return e.objectValues(r)},e.objectValues=t=>e.objectKeys(t).map(function(e){return t[e]}),e.objectKeys="function"==typeof Object.keys?e=>Object.keys(e):e=>{let t=[];for(let n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.push(n);return t},e.find=(e,t)=>{for(let n of e)if(t(n))return n},e.isInteger="function"==typeof Number.isInteger?e=>Number.isInteger(e):e=>"number"==typeof e&&Number.isFinite(e)&&Math.floor(e)===e,e.joinValues=function(e,t=" | "){return e.map(e=>"string"==typeof e?`'${e}'`:e).join(t)},e.jsonStringifyReplacer=(e,t)=>"bigint"==typeof t?t.toString():t}(i||(i={})),(a||(a={})).mergeShapes=(e,t)=>({...e,...t});let d=i.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),p=e=>{switch(typeof e){case"undefined":return d.undefined;case"string":return d.string;case"number":return Number.isNaN(e)?d.nan:d.number;case"boolean":return d.boolean;case"function":return d.function;case"bigint":return d.bigint;case"symbol":return d.symbol;case"object":if(Array.isArray(e))return d.array;if(null===e)return d.null;if(e.then&&"function"==typeof e.then&&e.catch&&"function"==typeof e.catch)return d.promise;if("undefined"!=typeof Map&&e instanceof Map)return d.map;if("undefined"!=typeof Set&&e instanceof Set)return d.set;if("undefined"!=typeof Date&&e instanceof Date)return d.date;return d.object;default:return d.unknown}},f=i.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]),h=e=>JSON.stringify(e,null,2).replace(/"([^"]+)":/g,"$1:");class m extends Error{get errors(){return this.issues}constructor(e){super(),this.issues=[],this.addIssue=e=>{this.issues=[...this.issues,e]},this.addIssues=(e=[])=>{this.issues=[...this.issues,...e]};let t=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,t):this.__proto__=t,this.name="ZodError",this.issues=e}format(e){let t=e||function(e){return e.message},n={_errors:[]},r=e=>{for(let i of e.issues)if("invalid_union"===i.code)i.unionErrors.map(r);else if("invalid_return_type"===i.code)r(i.returnTypeError);else if("invalid_arguments"===i.code)r(i.argumentsError);else if(0===i.path.length)n._errors.push(t(i));else{let e=n,r=0;for(;r<i.path.length;){let n=i.path[r];r===i.path.length-1?(e[n]=e[n]||{_errors:[]},e[n]._errors.push(t(i))):e[n]=e[n]||{_errors:[]},e=e[n],r++}}};return r(this),n}static assert(e){if(!(e instanceof m))throw Error(`Not a ZodError: ${e}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,i.jsonStringifyReplacer,2)}get isEmpty(){return 0===this.issues.length}flatten(e=e=>e.message){let t={},n=[];for(let r of this.issues)r.path.length>0?(t[r.path[0]]=t[r.path[0]]||[],t[r.path[0]].push(e(r))):n.push(e(r));return{formErrors:n,fieldErrors:t}}get formErrors(){return this.flatten()}}m.create=e=>new m(e);let g=(e,t)=>{let n;switch(e.code){case f.invalid_type:n=e.received===d.undefined?"Required":`Expected ${e.expected}, received ${e.received}`;break;case f.invalid_literal:n=`Invalid literal value, expected ${JSON.stringify(e.expected,i.jsonStringifyReplacer)}`;break;case f.unrecognized_keys:n=`Unrecognized key(s) in object: ${i.joinValues(e.keys,", ")}`;break;case f.invalid_union:n="Invalid input";break;case f.invalid_union_discriminator:n=`Invalid discriminator value. Expected ${i.joinValues(e.options)}`;break;case f.invalid_enum_value:n=`Invalid enum value. Expected ${i.joinValues(e.options)}, received '${e.received}'`;break;case f.invalid_arguments:n="Invalid function arguments";break;case f.invalid_return_type:n="Invalid function return type";break;case f.invalid_date:n="Invalid date";break;case f.invalid_string:"object"==typeof e.validation?"includes"in e.validation?(n=`Invalid input: must include "${e.validation.includes}"`,"number"==typeof e.validation.position&&(n=`${n} at one or more positions greater than or equal to ${e.validation.position}`)):"startsWith"in e.validation?n=`Invalid input: must start with "${e.validation.startsWith}"`:"endsWith"in e.validation?n=`Invalid input: must end with "${e.validation.endsWith}"`:i.assertNever(e.validation):n="regex"!==e.validation?`Invalid ${e.validation}`:"Invalid";break;case f.too_small:n="array"===e.type?`Array must contain ${e.exact?"exactly":e.inclusive?"at least":"more than"} ${e.minimum} element(s)`:"string"===e.type?`String must contain ${e.exact?"exactly":e.inclusive?"at least":"over"} ${e.minimum} character(s)`:"number"===e.type?`Number must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${e.minimum}`:"date"===e.type?`Date must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(e.minimum))}`:"Invalid input";break;case f.too_big:n="array"===e.type?`Array must contain ${e.exact?"exactly":e.inclusive?"at most":"less than"} ${e.maximum} element(s)`:"string"===e.type?`String must contain ${e.exact?"exactly":e.inclusive?"at most":"under"} ${e.maximum} character(s)`:"number"===e.type?`Number must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:"bigint"===e.type?`BigInt must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:"date"===e.type?`Date must be ${e.exact?"exactly":e.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(e.maximum))}`:"Invalid input";break;case f.custom:n="Invalid input";break;case f.invalid_intersection_types:n="Intersection results could not be merged";break;case f.not_multiple_of:n=`Number must be a multiple of ${e.multipleOf}`;break;case f.not_finite:n="Number must be finite";break;default:n=t.defaultError,i.assertNever(e)}return{message:n}},y=g;function v(e){y=e}function x(){return y}let k=e=>{let{data:t,path:n,errorMaps:r,issueData:i}=e,a=[...n,...i.path||[]],l={...i,path:a};if(void 0!==i.message)return{...i,path:a,message:i.message};let o="";for(let e of r.filter(e=>!!e).slice().reverse())o=e(l,{data:t,defaultError:o}).message;return{...i,path:a,message:o}},_=[];function b(e,t){let n=y,r=k({issueData:t,data:e.data,path:e.path,errorMaps:[e.common.contextualErrorMap,e.schemaErrorMap,n,n===g?void 0:g].filter(e=>!!e)});e.common.issues.push(r)}class w{constructor(){this.value="valid"}dirty(){"valid"===this.value&&(this.value="dirty")}abort(){"aborted"!==this.value&&(this.value="aborted")}static mergeArray(e,t){let n=[];for(let r of t){if("aborted"===r.status)return S;"dirty"===r.status&&e.dirty(),n.push(r.value)}return{status:e.value,value:n}}static async mergeObjectAsync(e,t){let n=[];for(let e of t){let t=await e.key,r=await e.value;n.push({key:t,value:r})}return w.mergeObjectSync(e,n)}static mergeObjectSync(e,t){let n={};for(let r of t){let{key:t,value:i}=r;if("aborted"===t.status||"aborted"===i.status)return S;"dirty"===t.status&&e.dirty(),"dirty"===i.status&&e.dirty(),"__proto__"!==t.value&&(void 0!==i.value||r.alwaysSet)&&(n[t.value]=i.value)}return{status:e.value,value:n}}}let S=Object.freeze({status:"aborted"}),T=e=>({status:"dirty",value:e}),C=e=>({status:"valid",value:e}),E=e=>"aborted"===e.status,I=e=>"dirty"===e.status,A=e=>"valid"===e.status,O=e=>"undefined"!=typeof Promise&&e instanceof Promise;!function(e){e.errToObj=e=>"string"==typeof e?{message:e}:e||{},e.toString=e=>"string"==typeof e?e:e?.message}(l||(l={}));var P=function(e,t,n,r){if("a"===n&&!r)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!r:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===n?r:"a"===n?r.call(e):r?r.value:t.get(e)},N=function(e,t,n,r,i){if("m"===r)throw TypeError("Private method is not writable");if("a"===r&&!i)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!i:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===r?i.call(e,n):i?i.value=n:t.set(e,n),n};class L{constructor(e,t,n,r){this._cachedPath=[],this.parent=e,this.data=t,this._path=n,this._key=r}get path(){return this._cachedPath.length||(Array.isArray(this._key)?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}let D=(e,t)=>{if(A(t))return{success:!0,data:t.value};if(!e.common.issues.length)throw Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;let t=new m(e.common.issues);return this._error=t,this._error}}};function R(e){if(!e)return{};let{errorMap:t,invalid_type_error:n,required_error:r,description:i}=e;if(t&&(n||r))throw Error('Can\'t use "invalid_type_error" or "required_error" in conjunction with custom error map.');return t?{errorMap:t,description:i}:{errorMap:(t,i)=>{let{message:a}=e;return"invalid_enum_value"===t.code?{message:a??i.defaultError}:void 0===i.data?{message:a??r??i.defaultError}:"invalid_type"!==t.code?{message:i.defaultError}:{message:a??n??i.defaultError}},description:i}}class j{get description(){return this._def.description}_getType(e){return p(e.data)}_getOrReturnCtx(e,t){return t||{common:e.parent.common,data:e.data,parsedType:p(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}_processInputParams(e){return{status:new w,ctx:{common:e.parent.common,data:e.data,parsedType:p(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}}_parseSync(e){let t=this._parse(e);if(O(t))throw Error("Synchronous parse encountered promise.");return t}_parseAsync(e){return Promise.resolve(this._parse(e))}parse(e,t){let n=this.safeParse(e,t);if(n.success)return n.data;throw n.error}safeParse(e,t){let n={common:{issues:[],async:t?.async??!1,contextualErrorMap:t?.errorMap},path:t?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:p(e)},r=this._parseSync({data:e,path:n.path,parent:n});return D(n,r)}"~validate"(e){let t={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:p(e)};if(!this["~standard"].async)try{let n=this._parseSync({data:e,path:[],parent:t});return A(n)?{value:n.value}:{issues:t.common.issues}}catch(e){e?.message?.toLowerCase()?.includes("encountered")&&(this["~standard"].async=!0),t.common={issues:[],async:!0}}return this._parseAsync({data:e,path:[],parent:t}).then(e=>A(e)?{value:e.value}:{issues:t.common.issues})}async parseAsync(e,t){let n=await this.safeParseAsync(e,t);if(n.success)return n.data;throw n.error}async safeParseAsync(e,t){let n={common:{issues:[],contextualErrorMap:t?.errorMap,async:!0},path:t?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:p(e)},r=this._parse({data:e,path:n.path,parent:n});return D(n,await (O(r)?r:Promise.resolve(r)))}refine(e,t){let n=e=>"string"==typeof t||void 0===t?{message:t}:"function"==typeof t?t(e):t;return this._refinement((t,r)=>{let i=e(t),a=()=>r.addIssue({code:f.custom,...n(t)});return"undefined"!=typeof Promise&&i instanceof Promise?i.then(e=>!!e||(a(),!1)):!!i||(a(),!1)})}refinement(e,t){return this._refinement((n,r)=>!!e(n)||(r.addIssue("function"==typeof t?t(n,r):t),!1))}_refinement(e){return new eO({schema:this,typeName:u.ZodEffects,effect:{type:"refinement",refinement:e}})}superRefine(e){return this._refinement(e)}constructor(e){this.spa=this.safeParseAsync,this._def=e,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:e=>this["~validate"](e)}}optional(){return eP.create(this,this._def)}nullable(){return eN.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return ef.create(this)}promise(){return eA.create(this,this._def)}or(e){return em.create([this,e],this._def)}and(e){return ev.create(this,e,this._def)}transform(e){return new eO({...R(this._def),schema:this,typeName:u.ZodEffects,effect:{type:"transform",transform:e}})}default(e){return new eL({...R(this._def),innerType:this,defaultValue:"function"==typeof e?e:()=>e,typeName:u.ZodDefault})}brand(){return new eM({typeName:u.ZodBranded,type:this,...R(this._def)})}catch(e){return new eD({...R(this._def),innerType:this,catchValue:"function"==typeof e?e:()=>e,typeName:u.ZodCatch})}describe(e){return new this.constructor({...this._def,description:e})}pipe(e){return ez.create(this,e)}readonly(){return eF.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}let M=/^c[^\s-]{8,}$/i,z=/^[0-9a-z]+$/,F=/^[0-9A-HJKMNP-TV-Z]{26}$/i,Z=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,B=/^[a-z0-9_-]{21}$/i,V=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,U=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,$=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i,H=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,q=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,W=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,K=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,Y=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,J=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,Q="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",X=RegExp(`^${Q}$`);function G(e){let t="[0-5]\\d";e.precision?t=`${t}\\.\\d{${e.precision}}`:null==e.precision&&(t=`${t}(\\.\\d+)?`);let n=e.precision?"+":"?";return`([01]\\d|2[0-3]):[0-5]\\d(:${t})${n}`}function ee(e){let t=`${Q}T${G(e)}`,n=[];return n.push(e.local?"Z?":"Z"),e.offset&&n.push("([+-]\\d{2}:?\\d{2})"),t=`${t}(${n.join("|")})`,RegExp(`^${t}$`)}class et extends j{_parse(e){var t,n,a,l;let o;if(this._def.coerce&&(e.data=String(e.data)),this._getType(e)!==d.string){let t=this._getOrReturnCtx(e);return b(t,{code:f.invalid_type,expected:d.string,received:t.parsedType}),S}let s=new w;for(let u of this._def.checks)if("min"===u.kind)e.data.length<u.value&&(b(o=this._getOrReturnCtx(e,o),{code:f.too_small,minimum:u.value,type:"string",inclusive:!0,exact:!1,message:u.message}),s.dirty());else if("max"===u.kind)e.data.length>u.value&&(b(o=this._getOrReturnCtx(e,o),{code:f.too_big,maximum:u.value,type:"string",inclusive:!0,exact:!1,message:u.message}),s.dirty());else if("length"===u.kind){let t=e.data.length>u.value,n=e.data.length<u.value;(t||n)&&(o=this._getOrReturnCtx(e,o),t?b(o,{code:f.too_big,maximum:u.value,type:"string",inclusive:!0,exact:!0,message:u.message}):n&&b(o,{code:f.too_small,minimum:u.value,type:"string",inclusive:!0,exact:!0,message:u.message}),s.dirty())}else if("email"===u.kind)$.test(e.data)||(b(o=this._getOrReturnCtx(e,o),{validation:"email",code:f.invalid_string,message:u.message}),s.dirty());else if("emoji"===u.kind)r||(r=RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),r.test(e.data)||(b(o=this._getOrReturnCtx(e,o),{validation:"emoji",code:f.invalid_string,message:u.message}),s.dirty());else if("uuid"===u.kind)Z.test(e.data)||(b(o=this._getOrReturnCtx(e,o),{validation:"uuid",code:f.invalid_string,message:u.message}),s.dirty());else if("nanoid"===u.kind)B.test(e.data)||(b(o=this._getOrReturnCtx(e,o),{validation:"nanoid",code:f.invalid_string,message:u.message}),s.dirty());else if("cuid"===u.kind)M.test(e.data)||(b(o=this._getOrReturnCtx(e,o),{validation:"cuid",code:f.invalid_string,message:u.message}),s.dirty());else if("cuid2"===u.kind)z.test(e.data)||(b(o=this._getOrReturnCtx(e,o),{validation:"cuid2",code:f.invalid_string,message:u.message}),s.dirty());else if("ulid"===u.kind)F.test(e.data)||(b(o=this._getOrReturnCtx(e,o),{validation:"ulid",code:f.invalid_string,message:u.message}),s.dirty());else if("url"===u.kind)try{new URL(e.data)}catch{b(o=this._getOrReturnCtx(e,o),{validation:"url",code:f.invalid_string,message:u.message}),s.dirty()}else"regex"===u.kind?(u.regex.lastIndex=0,u.regex.test(e.data)||(b(o=this._getOrReturnCtx(e,o),{validation:"regex",code:f.invalid_string,message:u.message}),s.dirty())):"trim"===u.kind?e.data=e.data.trim():"includes"===u.kind?e.data.includes(u.value,u.position)||(b(o=this._getOrReturnCtx(e,o),{code:f.invalid_string,validation:{includes:u.value,position:u.position},message:u.message}),s.dirty()):"toLowerCase"===u.kind?e.data=e.data.toLowerCase():"toUpperCase"===u.kind?e.data=e.data.toUpperCase():"startsWith"===u.kind?e.data.startsWith(u.value)||(b(o=this._getOrReturnCtx(e,o),{code:f.invalid_string,validation:{startsWith:u.value},message:u.message}),s.dirty()):"endsWith"===u.kind?e.data.endsWith(u.value)||(b(o=this._getOrReturnCtx(e,o),{code:f.invalid_string,validation:{endsWith:u.value},message:u.message}),s.dirty()):"datetime"===u.kind?ee(u).test(e.data)||(b(o=this._getOrReturnCtx(e,o),{code:f.invalid_string,validation:"datetime",message:u.message}),s.dirty()):"date"===u.kind?X.test(e.data)||(b(o=this._getOrReturnCtx(e,o),{code:f.invalid_string,validation:"date",message:u.message}),s.dirty()):"time"===u.kind?RegExp(`^${G(u)}$`).test(e.data)||(b(o=this._getOrReturnCtx(e,o),{code:f.invalid_string,validation:"time",message:u.message}),s.dirty()):"duration"===u.kind?U.test(e.data)||(b(o=this._getOrReturnCtx(e,o),{validation:"duration",code:f.invalid_string,message:u.message}),s.dirty()):"ip"===u.kind?(t=e.data,("v4"===(n=u.version)||!n)&&H.test(t)||("v6"===n||!n)&&W.test(t)||(b(o=this._getOrReturnCtx(e,o),{validation:"ip",code:f.invalid_string,message:u.message}),s.dirty())):"jwt"===u.kind?!function(e,t){if(!V.test(e))return!1;try{let[n]=e.split("."),r=n.replace(/-/g,"+").replace(/_/g,"/").padEnd(n.length+(4-n.length%4)%4,"="),i=JSON.parse(atob(r));if("object"!=typeof i||null===i||"typ"in i&&i?.typ!=="JWT"||!i.alg||t&&i.alg!==t)return!1;return!0}catch{return!1}}(e.data,u.alg)&&(b(o=this._getOrReturnCtx(e,o),{validation:"jwt",code:f.invalid_string,message:u.message}),s.dirty()):"cidr"===u.kind?(a=e.data,("v4"===(l=u.version)||!l)&&q.test(a)||("v6"===l||!l)&&K.test(a)||(b(o=this._getOrReturnCtx(e,o),{validation:"cidr",code:f.invalid_string,message:u.message}),s.dirty())):"base64"===u.kind?Y.test(e.data)||(b(o=this._getOrReturnCtx(e,o),{validation:"base64",code:f.invalid_string,message:u.message}),s.dirty()):"base64url"===u.kind?J.test(e.data)||(b(o=this._getOrReturnCtx(e,o),{validation:"base64url",code:f.invalid_string,message:u.message}),s.dirty()):i.assertNever(u);return{status:s.value,value:e.data}}_regex(e,t,n){return this.refinement(t=>e.test(t),{validation:t,code:f.invalid_string,...l.errToObj(n)})}_addCheck(e){return new et({...this._def,checks:[...this._def.checks,e]})}email(e){return this._addCheck({kind:"email",...l.errToObj(e)})}url(e){return this._addCheck({kind:"url",...l.errToObj(e)})}emoji(e){return this._addCheck({kind:"emoji",...l.errToObj(e)})}uuid(e){return this._addCheck({kind:"uuid",...l.errToObj(e)})}nanoid(e){return this._addCheck({kind:"nanoid",...l.errToObj(e)})}cuid(e){return this._addCheck({kind:"cuid",...l.errToObj(e)})}cuid2(e){return this._addCheck({kind:"cuid2",...l.errToObj(e)})}ulid(e){return this._addCheck({kind:"ulid",...l.errToObj(e)})}base64(e){return this._addCheck({kind:"base64",...l.errToObj(e)})}base64url(e){return this._addCheck({kind:"base64url",...l.errToObj(e)})}jwt(e){return this._addCheck({kind:"jwt",...l.errToObj(e)})}ip(e){return this._addCheck({kind:"ip",...l.errToObj(e)})}cidr(e){return this._addCheck({kind:"cidr",...l.errToObj(e)})}datetime(e){return"string"==typeof e?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:e}):this._addCheck({kind:"datetime",precision:void 0===e?.precision?null:e?.precision,offset:e?.offset??!1,local:e?.local??!1,...l.errToObj(e?.message)})}date(e){return this._addCheck({kind:"date",message:e})}time(e){return"string"==typeof e?this._addCheck({kind:"time",precision:null,message:e}):this._addCheck({kind:"time",precision:void 0===e?.precision?null:e?.precision,...l.errToObj(e?.message)})}duration(e){return this._addCheck({kind:"duration",...l.errToObj(e)})}regex(e,t){return this._addCheck({kind:"regex",regex:e,...l.errToObj(t)})}includes(e,t){return this._addCheck({kind:"includes",value:e,position:t?.position,...l.errToObj(t?.message)})}startsWith(e,t){return this._addCheck({kind:"startsWith",value:e,...l.errToObj(t)})}endsWith(e,t){return this._addCheck({kind:"endsWith",value:e,...l.errToObj(t)})}min(e,t){return this._addCheck({kind:"min",value:e,...l.errToObj(t)})}max(e,t){return this._addCheck({kind:"max",value:e,...l.errToObj(t)})}length(e,t){return this._addCheck({kind:"length",value:e,...l.errToObj(t)})}nonempty(e){return this.min(1,l.errToObj(e))}trim(){return new et({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new et({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new et({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(e=>"datetime"===e.kind)}get isDate(){return!!this._def.checks.find(e=>"date"===e.kind)}get isTime(){return!!this._def.checks.find(e=>"time"===e.kind)}get isDuration(){return!!this._def.checks.find(e=>"duration"===e.kind)}get isEmail(){return!!this._def.checks.find(e=>"email"===e.kind)}get isURL(){return!!this._def.checks.find(e=>"url"===e.kind)}get isEmoji(){return!!this._def.checks.find(e=>"emoji"===e.kind)}get isUUID(){return!!this._def.checks.find(e=>"uuid"===e.kind)}get isNANOID(){return!!this._def.checks.find(e=>"nanoid"===e.kind)}get isCUID(){return!!this._def.checks.find(e=>"cuid"===e.kind)}get isCUID2(){return!!this._def.checks.find(e=>"cuid2"===e.kind)}get isULID(){return!!this._def.checks.find(e=>"ulid"===e.kind)}get isIP(){return!!this._def.checks.find(e=>"ip"===e.kind)}get isCIDR(){return!!this._def.checks.find(e=>"cidr"===e.kind)}get isBase64(){return!!this._def.checks.find(e=>"base64"===e.kind)}get isBase64url(){return!!this._def.checks.find(e=>"base64url"===e.kind)}get minLength(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxLength(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}}et.create=e=>new et({checks:[],typeName:u.ZodString,coerce:e?.coerce??!1,...R(e)});class en extends j{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(e){let t;if(this._def.coerce&&(e.data=Number(e.data)),this._getType(e)!==d.number){let t=this._getOrReturnCtx(e);return b(t,{code:f.invalid_type,expected:d.number,received:t.parsedType}),S}let n=new w;for(let r of this._def.checks)"int"===r.kind?i.isInteger(e.data)||(b(t=this._getOrReturnCtx(e,t),{code:f.invalid_type,expected:"integer",received:"float",message:r.message}),n.dirty()):"min"===r.kind?(r.inclusive?e.data<r.value:e.data<=r.value)&&(b(t=this._getOrReturnCtx(e,t),{code:f.too_small,minimum:r.value,type:"number",inclusive:r.inclusive,exact:!1,message:r.message}),n.dirty()):"max"===r.kind?(r.inclusive?e.data>r.value:e.data>=r.value)&&(b(t=this._getOrReturnCtx(e,t),{code:f.too_big,maximum:r.value,type:"number",inclusive:r.inclusive,exact:!1,message:r.message}),n.dirty()):"multipleOf"===r.kind?0!==function(e,t){let n=(e.toString().split(".")[1]||"").length,r=(t.toString().split(".")[1]||"").length,i=n>r?n:r;return Number.parseInt(e.toFixed(i).replace(".",""))%Number.parseInt(t.toFixed(i).replace(".",""))/10**i}(e.data,r.value)&&(b(t=this._getOrReturnCtx(e,t),{code:f.not_multiple_of,multipleOf:r.value,message:r.message}),n.dirty()):"finite"===r.kind?Number.isFinite(e.data)||(b(t=this._getOrReturnCtx(e,t),{code:f.not_finite,message:r.message}),n.dirty()):i.assertNever(r);return{status:n.value,value:e.data}}gte(e,t){return this.setLimit("min",e,!0,l.toString(t))}gt(e,t){return this.setLimit("min",e,!1,l.toString(t))}lte(e,t){return this.setLimit("max",e,!0,l.toString(t))}lt(e,t){return this.setLimit("max",e,!1,l.toString(t))}setLimit(e,t,n,r){return new en({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:n,message:l.toString(r)}]})}_addCheck(e){return new en({...this._def,checks:[...this._def.checks,e]})}int(e){return this._addCheck({kind:"int",message:l.toString(e)})}positive(e){return this._addCheck({kind:"min",value:0,inclusive:!1,message:l.toString(e)})}negative(e){return this._addCheck({kind:"max",value:0,inclusive:!1,message:l.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:0,inclusive:!0,message:l.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:0,inclusive:!0,message:l.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:l.toString(t)})}finite(e){return this._addCheck({kind:"finite",message:l.toString(e)})}safe(e){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:l.toString(e)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:l.toString(e)})}get minValue(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}get isInt(){return!!this._def.checks.find(e=>"int"===e.kind||"multipleOf"===e.kind&&i.isInteger(e.value))}get isFinite(){let e=null,t=null;for(let n of this._def.checks){if("finite"===n.kind||"int"===n.kind||"multipleOf"===n.kind)return!0;"min"===n.kind?(null===t||n.value>t)&&(t=n.value):"max"===n.kind&&(null===e||n.value<e)&&(e=n.value)}return Number.isFinite(t)&&Number.isFinite(e)}}en.create=e=>new en({checks:[],typeName:u.ZodNumber,coerce:e?.coerce||!1,...R(e)});class er extends j{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(e){let t;if(this._def.coerce)try{e.data=BigInt(e.data)}catch{return this._getInvalidInput(e)}if(this._getType(e)!==d.bigint)return this._getInvalidInput(e);let n=new w;for(let r of this._def.checks)"min"===r.kind?(r.inclusive?e.data<r.value:e.data<=r.value)&&(b(t=this._getOrReturnCtx(e,t),{code:f.too_small,type:"bigint",minimum:r.value,inclusive:r.inclusive,message:r.message}),n.dirty()):"max"===r.kind?(r.inclusive?e.data>r.value:e.data>=r.value)&&(b(t=this._getOrReturnCtx(e,t),{code:f.too_big,type:"bigint",maximum:r.value,inclusive:r.inclusive,message:r.message}),n.dirty()):"multipleOf"===r.kind?e.data%r.value!==BigInt(0)&&(b(t=this._getOrReturnCtx(e,t),{code:f.not_multiple_of,multipleOf:r.value,message:r.message}),n.dirty()):i.assertNever(r);return{status:n.value,value:e.data}}_getInvalidInput(e){let t=this._getOrReturnCtx(e);return b(t,{code:f.invalid_type,expected:d.bigint,received:t.parsedType}),S}gte(e,t){return this.setLimit("min",e,!0,l.toString(t))}gt(e,t){return this.setLimit("min",e,!1,l.toString(t))}lte(e,t){return this.setLimit("max",e,!0,l.toString(t))}lt(e,t){return this.setLimit("max",e,!1,l.toString(t))}setLimit(e,t,n,r){return new er({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:n,message:l.toString(r)}]})}_addCheck(e){return new er({...this._def,checks:[...this._def.checks,e]})}positive(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:l.toString(e)})}negative(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:l.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:l.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:l.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:l.toString(t)})}get minValue(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}}er.create=e=>new er({checks:[],typeName:u.ZodBigInt,coerce:e?.coerce??!1,...R(e)});class ei extends j{_parse(e){if(this._def.coerce&&(e.data=!!e.data),this._getType(e)!==d.boolean){let t=this._getOrReturnCtx(e);return b(t,{code:f.invalid_type,expected:d.boolean,received:t.parsedType}),S}return C(e.data)}}ei.create=e=>new ei({typeName:u.ZodBoolean,coerce:e?.coerce||!1,...R(e)});class ea extends j{_parse(e){let t;if(this._def.coerce&&(e.data=new Date(e.data)),this._getType(e)!==d.date){let t=this._getOrReturnCtx(e);return b(t,{code:f.invalid_type,expected:d.date,received:t.parsedType}),S}if(Number.isNaN(e.data.getTime()))return b(this._getOrReturnCtx(e),{code:f.invalid_date}),S;let n=new w;for(let r of this._def.checks)"min"===r.kind?e.data.getTime()<r.value&&(b(t=this._getOrReturnCtx(e,t),{code:f.too_small,message:r.message,inclusive:!0,exact:!1,minimum:r.value,type:"date"}),n.dirty()):"max"===r.kind?e.data.getTime()>r.value&&(b(t=this._getOrReturnCtx(e,t),{code:f.too_big,message:r.message,inclusive:!0,exact:!1,maximum:r.value,type:"date"}),n.dirty()):i.assertNever(r);return{status:n.value,value:new Date(e.data.getTime())}}_addCheck(e){return new ea({...this._def,checks:[...this._def.checks,e]})}min(e,t){return this._addCheck({kind:"min",value:e.getTime(),message:l.toString(t)})}max(e,t){return this._addCheck({kind:"max",value:e.getTime(),message:l.toString(t)})}get minDate(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return null!=e?new Date(e):null}get maxDate(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return null!=e?new Date(e):null}}ea.create=e=>new ea({checks:[],coerce:e?.coerce||!1,typeName:u.ZodDate,...R(e)});class el extends j{_parse(e){if(this._getType(e)!==d.symbol){let t=this._getOrReturnCtx(e);return b(t,{code:f.invalid_type,expected:d.symbol,received:t.parsedType}),S}return C(e.data)}}el.create=e=>new el({typeName:u.ZodSymbol,...R(e)});class eo extends j{_parse(e){if(this._getType(e)!==d.undefined){let t=this._getOrReturnCtx(e);return b(t,{code:f.invalid_type,expected:d.undefined,received:t.parsedType}),S}return C(e.data)}}eo.create=e=>new eo({typeName:u.ZodUndefined,...R(e)});class es extends j{_parse(e){if(this._getType(e)!==d.null){let t=this._getOrReturnCtx(e);return b(t,{code:f.invalid_type,expected:d.null,received:t.parsedType}),S}return C(e.data)}}es.create=e=>new es({typeName:u.ZodNull,...R(e)});class eu extends j{constructor(){super(...arguments),this._any=!0}_parse(e){return C(e.data)}}eu.create=e=>new eu({typeName:u.ZodAny,...R(e)});class ec extends j{constructor(){super(...arguments),this._unknown=!0}_parse(e){return C(e.data)}}ec.create=e=>new ec({typeName:u.ZodUnknown,...R(e)});class ed extends j{_parse(e){let t=this._getOrReturnCtx(e);return b(t,{code:f.invalid_type,expected:d.never,received:t.parsedType}),S}}ed.create=e=>new ed({typeName:u.ZodNever,...R(e)});class ep extends j{_parse(e){if(this._getType(e)!==d.undefined){let t=this._getOrReturnCtx(e);return b(t,{code:f.invalid_type,expected:d.void,received:t.parsedType}),S}return C(e.data)}}ep.create=e=>new ep({typeName:u.ZodVoid,...R(e)});class ef extends j{_parse(e){let{ctx:t,status:n}=this._processInputParams(e),r=this._def;if(t.parsedType!==d.array)return b(t,{code:f.invalid_type,expected:d.array,received:t.parsedType}),S;if(null!==r.exactLength){let e=t.data.length>r.exactLength.value,i=t.data.length<r.exactLength.value;(e||i)&&(b(t,{code:e?f.too_big:f.too_small,minimum:i?r.exactLength.value:void 0,maximum:e?r.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:r.exactLength.message}),n.dirty())}if(null!==r.minLength&&t.data.length<r.minLength.value&&(b(t,{code:f.too_small,minimum:r.minLength.value,type:"array",inclusive:!0,exact:!1,message:r.minLength.message}),n.dirty()),null!==r.maxLength&&t.data.length>r.maxLength.value&&(b(t,{code:f.too_big,maximum:r.maxLength.value,type:"array",inclusive:!0,exact:!1,message:r.maxLength.message}),n.dirty()),t.common.async)return Promise.all([...t.data].map((e,n)=>r.type._parseAsync(new L(t,e,t.path,n)))).then(e=>w.mergeArray(n,e));let i=[...t.data].map((e,n)=>r.type._parseSync(new L(t,e,t.path,n)));return w.mergeArray(n,i)}get element(){return this._def.type}min(e,t){return new ef({...this._def,minLength:{value:e,message:l.toString(t)}})}max(e,t){return new ef({...this._def,maxLength:{value:e,message:l.toString(t)}})}length(e,t){return new ef({...this._def,exactLength:{value:e,message:l.toString(t)}})}nonempty(e){return this.min(1,e)}}ef.create=(e,t)=>new ef({type:e,minLength:null,maxLength:null,exactLength:null,typeName:u.ZodArray,...R(t)});class eh extends j{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(null!==this._cached)return this._cached;let e=this._def.shape(),t=i.objectKeys(e);return this._cached={shape:e,keys:t},this._cached}_parse(e){if(this._getType(e)!==d.object){let t=this._getOrReturnCtx(e);return b(t,{code:f.invalid_type,expected:d.object,received:t.parsedType}),S}let{status:t,ctx:n}=this._processInputParams(e),{shape:r,keys:i}=this._getCached(),a=[];if(!(this._def.catchall instanceof ed&&"strip"===this._def.unknownKeys))for(let e in n.data)i.includes(e)||a.push(e);let l=[];for(let e of i){let t=r[e],i=n.data[e];l.push({key:{status:"valid",value:e},value:t._parse(new L(n,i,n.path,e)),alwaysSet:e in n.data})}if(this._def.catchall instanceof ed){let e=this._def.unknownKeys;if("passthrough"===e)for(let e of a)l.push({key:{status:"valid",value:e},value:{status:"valid",value:n.data[e]}});else if("strict"===e)a.length>0&&(b(n,{code:f.unrecognized_keys,keys:a}),t.dirty());else if("strip"===e);else throw Error("Internal ZodObject error: invalid unknownKeys value.")}else{let e=this._def.catchall;for(let t of a){let r=n.data[t];l.push({key:{status:"valid",value:t},value:e._parse(new L(n,r,n.path,t)),alwaysSet:t in n.data})}}return n.common.async?Promise.resolve().then(async()=>{let e=[];for(let t of l){let n=await t.key,r=await t.value;e.push({key:n,value:r,alwaysSet:t.alwaysSet})}return e}).then(e=>w.mergeObjectSync(t,e)):w.mergeObjectSync(t,l)}get shape(){return this._def.shape()}strict(e){return l.errToObj,new eh({...this._def,unknownKeys:"strict",...void 0!==e?{errorMap:(t,n)=>{let r=this._def.errorMap?.(t,n).message??n.defaultError;return"unrecognized_keys"===t.code?{message:l.errToObj(e).message??r}:{message:r}}}:{}})}strip(){return new eh({...this._def,unknownKeys:"strip"})}passthrough(){return new eh({...this._def,unknownKeys:"passthrough"})}extend(e){return new eh({...this._def,shape:()=>({...this._def.shape(),...e})})}merge(e){return new eh({unknownKeys:e._def.unknownKeys,catchall:e._def.catchall,shape:()=>({...this._def.shape(),...e._def.shape()}),typeName:u.ZodObject})}setKey(e,t){return this.augment({[e]:t})}catchall(e){return new eh({...this._def,catchall:e})}pick(e){let t={};for(let n of i.objectKeys(e))e[n]&&this.shape[n]&&(t[n]=this.shape[n]);return new eh({...this._def,shape:()=>t})}omit(e){let t={};for(let n of i.objectKeys(this.shape))e[n]||(t[n]=this.shape[n]);return new eh({...this._def,shape:()=>t})}deepPartial(){return function e(t){if(t instanceof eh){let n={};for(let r in t.shape){let i=t.shape[r];n[r]=eP.create(e(i))}return new eh({...t._def,shape:()=>n})}return t instanceof ef?new ef({...t._def,type:e(t.element)}):t instanceof eP?eP.create(e(t.unwrap())):t instanceof eN?eN.create(e(t.unwrap())):t instanceof ex?ex.create(t.items.map(t=>e(t))):t}(this)}partial(e){let t={};for(let n of i.objectKeys(this.shape)){let r=this.shape[n];e&&!e[n]?t[n]=r:t[n]=r.optional()}return new eh({...this._def,shape:()=>t})}required(e){let t={};for(let n of i.objectKeys(this.shape))if(e&&!e[n])t[n]=this.shape[n];else{let e=this.shape[n];for(;e instanceof eP;)e=e._def.innerType;t[n]=e}return new eh({...this._def,shape:()=>t})}keyof(){return eC(i.objectKeys(this.shape))}}eh.create=(e,t)=>new eh({shape:()=>e,unknownKeys:"strip",catchall:ed.create(),typeName:u.ZodObject,...R(t)}),eh.strictCreate=(e,t)=>new eh({shape:()=>e,unknownKeys:"strict",catchall:ed.create(),typeName:u.ZodObject,...R(t)}),eh.lazycreate=(e,t)=>new eh({shape:e,unknownKeys:"strip",catchall:ed.create(),typeName:u.ZodObject,...R(t)});class em extends j{_parse(e){let{ctx:t}=this._processInputParams(e),n=this._def.options;if(t.common.async)return Promise.all(n.map(async e=>{let n={...t,common:{...t.common,issues:[]},parent:null};return{result:await e._parseAsync({data:t.data,path:t.path,parent:n}),ctx:n}})).then(function(e){for(let t of e)if("valid"===t.result.status)return t.result;for(let n of e)if("dirty"===n.result.status)return t.common.issues.push(...n.ctx.common.issues),n.result;let n=e.map(e=>new m(e.ctx.common.issues));return b(t,{code:f.invalid_union,unionErrors:n}),S});{let e;let r=[];for(let i of n){let n={...t,common:{...t.common,issues:[]},parent:null},a=i._parseSync({data:t.data,path:t.path,parent:n});if("valid"===a.status)return a;"dirty"!==a.status||e||(e={result:a,ctx:n}),n.common.issues.length&&r.push(n.common.issues)}if(e)return t.common.issues.push(...e.ctx.common.issues),e.result;let i=r.map(e=>new m(e));return b(t,{code:f.invalid_union,unionErrors:i}),S}}get options(){return this._def.options}}em.create=(e,t)=>new em({options:e,typeName:u.ZodUnion,...R(t)});let eg=e=>{if(e instanceof eS)return eg(e.schema);if(e instanceof eO)return eg(e.innerType());if(e instanceof eT)return[e.value];if(e instanceof eE)return e.options;if(e instanceof eI)return i.objectValues(e.enum);if(e instanceof eL)return eg(e._def.innerType);if(e instanceof eo)return[void 0];else if(e instanceof es)return[null];else if(e instanceof eP)return[void 0,...eg(e.unwrap())];else if(e instanceof eN)return[null,...eg(e.unwrap())];else if(e instanceof eM)return eg(e.unwrap());else if(e instanceof eF)return eg(e.unwrap());else if(e instanceof eD)return eg(e._def.innerType);else return[]};class ey extends j{_parse(e){let{ctx:t}=this._processInputParams(e);if(t.parsedType!==d.object)return b(t,{code:f.invalid_type,expected:d.object,received:t.parsedType}),S;let n=this.discriminator,r=t.data[n],i=this.optionsMap.get(r);return i?t.common.async?i._parseAsync({data:t.data,path:t.path,parent:t}):i._parseSync({data:t.data,path:t.path,parent:t}):(b(t,{code:f.invalid_union_discriminator,options:Array.from(this.optionsMap.keys()),path:[n]}),S)}get discriminator(){return this._def.discriminator}get options(){return this._def.options}get optionsMap(){return this._def.optionsMap}static create(e,t,n){let r=new Map;for(let n of t){let t=eg(n.shape[e]);if(!t.length)throw Error(`A discriminator value for key \`${e}\` could not be extracted from all schema options`);for(let i of t){if(r.has(i))throw Error(`Discriminator property ${String(e)} has duplicate value ${String(i)}`);r.set(i,n)}}return new ey({typeName:u.ZodDiscriminatedUnion,discriminator:e,options:t,optionsMap:r,...R(n)})}}class ev extends j{_parse(e){let{status:t,ctx:n}=this._processInputParams(e),r=(e,r)=>{if(E(e)||E(r))return S;let a=function e(t,n){let r=p(t),a=p(n);if(t===n)return{valid:!0,data:t};if(r===d.object&&a===d.object){let r=i.objectKeys(n),a=i.objectKeys(t).filter(e=>-1!==r.indexOf(e)),l={...t,...n};for(let r of a){let i=e(t[r],n[r]);if(!i.valid)return{valid:!1};l[r]=i.data}return{valid:!0,data:l}}if(r===d.array&&a===d.array){if(t.length!==n.length)return{valid:!1};let r=[];for(let i=0;i<t.length;i++){let a=e(t[i],n[i]);if(!a.valid)return{valid:!1};r.push(a.data)}return{valid:!0,data:r}}return r===d.date&&a===d.date&&+t==+n?{valid:!0,data:t}:{valid:!1}}(e.value,r.value);return a.valid?((I(e)||I(r))&&t.dirty(),{status:t.value,value:a.data}):(b(n,{code:f.invalid_intersection_types}),S)};return n.common.async?Promise.all([this._def.left._parseAsync({data:n.data,path:n.path,parent:n}),this._def.right._parseAsync({data:n.data,path:n.path,parent:n})]).then(([e,t])=>r(e,t)):r(this._def.left._parseSync({data:n.data,path:n.path,parent:n}),this._def.right._parseSync({data:n.data,path:n.path,parent:n}))}}ev.create=(e,t,n)=>new ev({left:e,right:t,typeName:u.ZodIntersection,...R(n)});class ex extends j{_parse(e){let{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==d.array)return b(n,{code:f.invalid_type,expected:d.array,received:n.parsedType}),S;if(n.data.length<this._def.items.length)return b(n,{code:f.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),S;!this._def.rest&&n.data.length>this._def.items.length&&(b(n,{code:f.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),t.dirty());let r=[...n.data].map((e,t)=>{let r=this._def.items[t]||this._def.rest;return r?r._parse(new L(n,e,n.path,t)):null}).filter(e=>!!e);return n.common.async?Promise.all(r).then(e=>w.mergeArray(t,e)):w.mergeArray(t,r)}get items(){return this._def.items}rest(e){return new ex({...this._def,rest:e})}}ex.create=(e,t)=>{if(!Array.isArray(e))throw Error("You must pass an array of schemas to z.tuple([ ... ])");return new ex({items:e,typeName:u.ZodTuple,rest:null,...R(t)})};class ek extends j{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){let{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==d.object)return b(n,{code:f.invalid_type,expected:d.object,received:n.parsedType}),S;let r=[],i=this._def.keyType,a=this._def.valueType;for(let e in n.data)r.push({key:i._parse(new L(n,e,n.path,e)),value:a._parse(new L(n,n.data[e],n.path,e)),alwaysSet:e in n.data});return n.common.async?w.mergeObjectAsync(t,r):w.mergeObjectSync(t,r)}get element(){return this._def.valueType}static create(e,t,n){return new ek(t instanceof j?{keyType:e,valueType:t,typeName:u.ZodRecord,...R(n)}:{keyType:et.create(),valueType:e,typeName:u.ZodRecord,...R(t)})}}class e_ extends j{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){let{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==d.map)return b(n,{code:f.invalid_type,expected:d.map,received:n.parsedType}),S;let r=this._def.keyType,i=this._def.valueType,a=[...n.data.entries()].map(([e,t],a)=>({key:r._parse(new L(n,e,n.path,[a,"key"])),value:i._parse(new L(n,t,n.path,[a,"value"]))}));if(n.common.async){let e=new Map;return Promise.resolve().then(async()=>{for(let n of a){let r=await n.key,i=await n.value;if("aborted"===r.status||"aborted"===i.status)return S;("dirty"===r.status||"dirty"===i.status)&&t.dirty(),e.set(r.value,i.value)}return{status:t.value,value:e}})}{let e=new Map;for(let n of a){let r=n.key,i=n.value;if("aborted"===r.status||"aborted"===i.status)return S;("dirty"===r.status||"dirty"===i.status)&&t.dirty(),e.set(r.value,i.value)}return{status:t.value,value:e}}}}e_.create=(e,t,n)=>new e_({valueType:t,keyType:e,typeName:u.ZodMap,...R(n)});class eb extends j{_parse(e){let{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==d.set)return b(n,{code:f.invalid_type,expected:d.set,received:n.parsedType}),S;let r=this._def;null!==r.minSize&&n.data.size<r.minSize.value&&(b(n,{code:f.too_small,minimum:r.minSize.value,type:"set",inclusive:!0,exact:!1,message:r.minSize.message}),t.dirty()),null!==r.maxSize&&n.data.size>r.maxSize.value&&(b(n,{code:f.too_big,maximum:r.maxSize.value,type:"set",inclusive:!0,exact:!1,message:r.maxSize.message}),t.dirty());let i=this._def.valueType;function a(e){let n=new Set;for(let r of e){if("aborted"===r.status)return S;"dirty"===r.status&&t.dirty(),n.add(r.value)}return{status:t.value,value:n}}let l=[...n.data.values()].map((e,t)=>i._parse(new L(n,e,n.path,t)));return n.common.async?Promise.all(l).then(e=>a(e)):a(l)}min(e,t){return new eb({...this._def,minSize:{value:e,message:l.toString(t)}})}max(e,t){return new eb({...this._def,maxSize:{value:e,message:l.toString(t)}})}size(e,t){return this.min(e,t).max(e,t)}nonempty(e){return this.min(1,e)}}eb.create=(e,t)=>new eb({valueType:e,minSize:null,maxSize:null,typeName:u.ZodSet,...R(t)});class ew extends j{constructor(){super(...arguments),this.validate=this.implement}_parse(e){let{ctx:t}=this._processInputParams(e);if(t.parsedType!==d.function)return b(t,{code:f.invalid_type,expected:d.function,received:t.parsedType}),S;function n(e,n){return k({data:e,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,y,g].filter(e=>!!e),issueData:{code:f.invalid_arguments,argumentsError:n}})}function r(e,n){return k({data:e,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,y,g].filter(e=>!!e),issueData:{code:f.invalid_return_type,returnTypeError:n}})}let i={errorMap:t.common.contextualErrorMap},a=t.data;if(this._def.returns instanceof eA){let e=this;return C(async function(...t){let l=new m([]),o=await e._def.args.parseAsync(t,i).catch(e=>{throw l.addIssue(n(t,e)),l}),s=await Reflect.apply(a,this,o);return await e._def.returns._def.type.parseAsync(s,i).catch(e=>{throw l.addIssue(r(s,e)),l})})}{let e=this;return C(function(...t){let l=e._def.args.safeParse(t,i);if(!l.success)throw new m([n(t,l.error)]);let o=Reflect.apply(a,this,l.data),s=e._def.returns.safeParse(o,i);if(!s.success)throw new m([r(o,s.error)]);return s.data})}}parameters(){return this._def.args}returnType(){return this._def.returns}args(...e){return new ew({...this._def,args:ex.create(e).rest(ec.create())})}returns(e){return new ew({...this._def,returns:e})}implement(e){return this.parse(e)}strictImplement(e){return this.parse(e)}static create(e,t,n){return new ew({args:e||ex.create([]).rest(ec.create()),returns:t||ec.create(),typeName:u.ZodFunction,...R(n)})}}class eS extends j{get schema(){return this._def.getter()}_parse(e){let{ctx:t}=this._processInputParams(e);return this._def.getter()._parse({data:t.data,path:t.path,parent:t})}}eS.create=(e,t)=>new eS({getter:e,typeName:u.ZodLazy,...R(t)});class eT extends j{_parse(e){if(e.data!==this._def.value){let t=this._getOrReturnCtx(e);return b(t,{received:t.data,code:f.invalid_literal,expected:this._def.value}),S}return{status:"valid",value:e.data}}get value(){return this._def.value}}function eC(e,t){return new eE({values:e,typeName:u.ZodEnum,...R(t)})}eT.create=(e,t)=>new eT({value:e,typeName:u.ZodLiteral,...R(t)});class eE extends j{constructor(){super(...arguments),o.set(this,void 0)}_parse(e){if("string"!=typeof e.data){let t=this._getOrReturnCtx(e),n=this._def.values;return b(t,{expected:i.joinValues(n),received:t.parsedType,code:f.invalid_type}),S}if(P(this,o,"f")||N(this,o,new Set(this._def.values),"f"),!P(this,o,"f").has(e.data)){let t=this._getOrReturnCtx(e),n=this._def.values;return b(t,{received:t.data,code:f.invalid_enum_value,options:n}),S}return C(e.data)}get options(){return this._def.values}get enum(){let e={};for(let t of this._def.values)e[t]=t;return e}get Values(){let e={};for(let t of this._def.values)e[t]=t;return e}get Enum(){let e={};for(let t of this._def.values)e[t]=t;return e}extract(e,t=this._def){return eE.create(e,{...this._def,...t})}exclude(e,t=this._def){return eE.create(this.options.filter(t=>!e.includes(t)),{...this._def,...t})}}o=new WeakMap,eE.create=eC;class eI extends j{constructor(){super(...arguments),s.set(this,void 0)}_parse(e){let t=i.getValidEnumValues(this._def.values),n=this._getOrReturnCtx(e);if(n.parsedType!==d.string&&n.parsedType!==d.number){let e=i.objectValues(t);return b(n,{expected:i.joinValues(e),received:n.parsedType,code:f.invalid_type}),S}if(P(this,s,"f")||N(this,s,new Set(i.getValidEnumValues(this._def.values)),"f"),!P(this,s,"f").has(e.data)){let e=i.objectValues(t);return b(n,{received:n.data,code:f.invalid_enum_value,options:e}),S}return C(e.data)}get enum(){return this._def.values}}s=new WeakMap,eI.create=(e,t)=>new eI({values:e,typeName:u.ZodNativeEnum,...R(t)});class eA extends j{unwrap(){return this._def.type}_parse(e){let{ctx:t}=this._processInputParams(e);return t.parsedType!==d.promise&&!1===t.common.async?(b(t,{code:f.invalid_type,expected:d.promise,received:t.parsedType}),S):C((t.parsedType===d.promise?t.data:Promise.resolve(t.data)).then(e=>this._def.type.parseAsync(e,{path:t.path,errorMap:t.common.contextualErrorMap})))}}eA.create=(e,t)=>new eA({type:e,typeName:u.ZodPromise,...R(t)});class eO extends j{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===u.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(e){let{status:t,ctx:n}=this._processInputParams(e),r=this._def.effect||null,a={addIssue:e=>{b(n,e),e.fatal?t.abort():t.dirty()},get path(){return n.path}};if(a.addIssue=a.addIssue.bind(a),"preprocess"===r.type){let e=r.transform(n.data,a);if(n.common.async)return Promise.resolve(e).then(async e=>{if("aborted"===t.value)return S;let r=await this._def.schema._parseAsync({data:e,path:n.path,parent:n});return"aborted"===r.status?S:"dirty"===r.status||"dirty"===t.value?T(r.value):r});{if("aborted"===t.value)return S;let r=this._def.schema._parseSync({data:e,path:n.path,parent:n});return"aborted"===r.status?S:"dirty"===r.status||"dirty"===t.value?T(r.value):r}}if("refinement"===r.type){let e=e=>{let t=r.refinement(e,a);if(n.common.async)return Promise.resolve(t);if(t instanceof Promise)throw Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return e};if(!1!==n.common.async)return this._def.schema._parseAsync({data:n.data,path:n.path,parent:n}).then(n=>"aborted"===n.status?S:("dirty"===n.status&&t.dirty(),e(n.value).then(()=>({status:t.value,value:n.value}))));{let r=this._def.schema._parseSync({data:n.data,path:n.path,parent:n});return"aborted"===r.status?S:("dirty"===r.status&&t.dirty(),e(r.value),{status:t.value,value:r.value})}}if("transform"===r.type){if(!1!==n.common.async)return this._def.schema._parseAsync({data:n.data,path:n.path,parent:n}).then(e=>A(e)?Promise.resolve(r.transform(e.value,a)).then(e=>({status:t.value,value:e})):e);{let e=this._def.schema._parseSync({data:n.data,path:n.path,parent:n});if(!A(e))return e;let i=r.transform(e.value,a);if(i instanceof Promise)throw Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:t.value,value:i}}}i.assertNever(r)}}eO.create=(e,t,n)=>new eO({schema:e,typeName:u.ZodEffects,effect:t,...R(n)}),eO.createWithPreprocess=(e,t,n)=>new eO({schema:t,effect:{type:"preprocess",transform:e},typeName:u.ZodEffects,...R(n)});class eP extends j{_parse(e){return this._getType(e)===d.undefined?C(void 0):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}eP.create=(e,t)=>new eP({innerType:e,typeName:u.ZodOptional,...R(t)});class eN extends j{_parse(e){return this._getType(e)===d.null?C(null):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}eN.create=(e,t)=>new eN({innerType:e,typeName:u.ZodNullable,...R(t)});class eL extends j{_parse(e){let{ctx:t}=this._processInputParams(e),n=t.data;return t.parsedType===d.undefined&&(n=this._def.defaultValue()),this._def.innerType._parse({data:n,path:t.path,parent:t})}removeDefault(){return this._def.innerType}}eL.create=(e,t)=>new eL({innerType:e,typeName:u.ZodDefault,defaultValue:"function"==typeof t.default?t.default:()=>t.default,...R(t)});class eD extends j{_parse(e){let{ctx:t}=this._processInputParams(e),n={...t,common:{...t.common,issues:[]}},r=this._def.innerType._parse({data:n.data,path:n.path,parent:{...n}});return O(r)?r.then(e=>({status:"valid",value:"valid"===e.status?e.value:this._def.catchValue({get error(){return new m(n.common.issues)},input:n.data})})):{status:"valid",value:"valid"===r.status?r.value:this._def.catchValue({get error(){return new m(n.common.issues)},input:n.data})}}removeCatch(){return this._def.innerType}}eD.create=(e,t)=>new eD({innerType:e,typeName:u.ZodCatch,catchValue:"function"==typeof t.catch?t.catch:()=>t.catch,...R(t)});class eR extends j{_parse(e){if(this._getType(e)!==d.nan){let t=this._getOrReturnCtx(e);return b(t,{code:f.invalid_type,expected:d.nan,received:t.parsedType}),S}return{status:"valid",value:e.data}}}eR.create=e=>new eR({typeName:u.ZodNaN,...R(e)});let ej=Symbol("zod_brand");class eM extends j{_parse(e){let{ctx:t}=this._processInputParams(e),n=t.data;return this._def.type._parse({data:n,path:t.path,parent:t})}unwrap(){return this._def.type}}class ez extends j{_parse(e){let{status:t,ctx:n}=this._processInputParams(e);if(n.common.async)return(async()=>{let e=await this._def.in._parseAsync({data:n.data,path:n.path,parent:n});return"aborted"===e.status?S:"dirty"===e.status?(t.dirty(),T(e.value)):this._def.out._parseAsync({data:e.value,path:n.path,parent:n})})();{let e=this._def.in._parseSync({data:n.data,path:n.path,parent:n});return"aborted"===e.status?S:"dirty"===e.status?(t.dirty(),{status:"dirty",value:e.value}):this._def.out._parseSync({data:e.value,path:n.path,parent:n})}}static create(e,t){return new ez({in:e,out:t,typeName:u.ZodPipeline})}}class eF extends j{_parse(e){let t=this._def.innerType._parse(e),n=e=>(A(e)&&(e.value=Object.freeze(e.value)),e);return O(t)?t.then(e=>n(e)):n(t)}unwrap(){return this._def.innerType}}function eZ(e,t){let n="function"==typeof e?e(t):"string"==typeof e?{message:e}:e;return"string"==typeof n?{message:n}:n}function eB(e,t={},n){return e?eu.create().superRefine((r,i)=>{let a=e(r);if(a instanceof Promise)return a.then(e=>{if(!e){let e=eZ(t,r),a=e.fatal??n??!0;i.addIssue({code:"custom",...e,fatal:a})}});if(!a){let e=eZ(t,r),a=e.fatal??n??!0;i.addIssue({code:"custom",...e,fatal:a})}}):eu.create()}eF.create=(e,t)=>new eF({innerType:e,typeName:u.ZodReadonly,...R(t)});let eV={object:eh.lazycreate};!function(e){e.ZodString="ZodString",e.ZodNumber="ZodNumber",e.ZodNaN="ZodNaN",e.ZodBigInt="ZodBigInt",e.ZodBoolean="ZodBoolean",e.ZodDate="ZodDate",e.ZodSymbol="ZodSymbol",e.ZodUndefined="ZodUndefined",e.ZodNull="ZodNull",e.ZodAny="ZodAny",e.ZodUnknown="ZodUnknown",e.ZodNever="ZodNever",e.ZodVoid="ZodVoid",e.ZodArray="ZodArray",e.ZodObject="ZodObject",e.ZodUnion="ZodUnion",e.ZodDiscriminatedUnion="ZodDiscriminatedUnion",e.ZodIntersection="ZodIntersection",e.ZodTuple="ZodTuple",e.ZodRecord="ZodRecord",e.ZodMap="ZodMap",e.ZodSet="ZodSet",e.ZodFunction="ZodFunction",e.ZodLazy="ZodLazy",e.ZodLiteral="ZodLiteral",e.ZodEnum="ZodEnum",e.ZodEffects="ZodEffects",e.ZodNativeEnum="ZodNativeEnum",e.ZodOptional="ZodOptional",e.ZodNullable="ZodNullable",e.ZodDefault="ZodDefault",e.ZodCatch="ZodCatch",e.ZodPromise="ZodPromise",e.ZodBranded="ZodBranded",e.ZodPipeline="ZodPipeline",e.ZodReadonly="ZodReadonly"}(u||(u={}));let eU=(e,t={message:`Input not instance of ${e.name}`})=>eB(t=>t instanceof e,t),e$=et.create,eH=en.create,eq=eR.create,eW=er.create,eK=ei.create,eY=ea.create,eJ=el.create,eQ=eo.create,eX=es.create,eG=eu.create,e0=ec.create,e1=ed.create,e4=ep.create,e2=ef.create,e9=eh.create,e5=eh.strictCreate,e6=em.create,e3=ey.create,e7=ev.create,e8=ex.create,te=ek.create,tt=e_.create,tn=eb.create,tr=ew.create,ti=eS.create,ta=eT.create,tl=eE.create,to=eI.create,ts=eA.create,tu=eO.create,tc=eP.create,td=eN.create,tp=eO.createWithPreprocess,tf=ez.create,th=()=>e$().optional(),tm=()=>eH().optional(),tg=()=>eK().optional(),ty={string:e=>et.create({...e,coerce:!0}),number:e=>en.create({...e,coerce:!0}),boolean:e=>ei.create({...e,coerce:!0}),bigint:e=>er.create({...e,coerce:!0}),date:e=>ea.create({...e,coerce:!0})},tv=S}}]);