{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/hacking-quiz/src/lib/utils/index.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\";\n\n/**\n * Combines class names using clsx and tailwind-merge\n */\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\n/**\n * Generates a random UUID\n */\nexport function generateUUID(): string {\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {\n    const r = Math.random() * 16 | 0;\n    const v = c === 'x' ? r : (r & 0x3 | 0x8);\n    return v.toString(16);\n  });\n}\n\n/**\n * Formats a date to a readable string\n */\nexport function formatDate(date: Date | string): string {\n  const d = typeof date === 'string' ? new Date(date) : date;\n  return d.toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  });\n}\n\n/**\n * Formats time in minutes to a readable string (e.g., \"1 hour 30 minutes\")\n */\nexport function formatTimeLimit(minutes: number): string {\n  if (minutes < 60) {\n    return `${minutes} minute${minutes !== 1 ? 's' : ''}`;\n  }\n  \n  const hours = Math.floor(minutes / 60);\n  const remainingMinutes = minutes % 60;\n  \n  if (remainingMinutes === 0) {\n    return `${hours} hour${hours !== 1 ? 's' : ''}`;\n  }\n  \n  return `${hours} hour${hours !== 1 ? 's' : ''} ${remainingMinutes} minute${remainingMinutes !== 1 ? 's' : ''}`;\n}\n\n/**\n * Truncates text to a specified length with ellipsis\n */\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text;\n  return text.slice(0, maxLength) + '...';\n}\n\n/**\n * Debounces a function\n */\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout | null = null;\n  \n  return function(...args: Parameters<T>): void {\n    const later = () => {\n      timeout = null;\n      func(...args);\n    };\n    \n    if (timeout !== null) {\n      clearTimeout(timeout);\n    }\n    \n    timeout = setTimeout(later, wait);\n  };\n}\n\n/**\n * Calculates the percentage of a value out of a total\n */\nexport function calculatePercentage(value: number, total: number): number {\n  if (total === 0) return 0;\n  return Math.round((value / total) * 100);\n}\n\n/**\n * Shuffles an array using Fisher-Yates algorithm\n */\nexport function shuffleArray<T>(array: T[]): T[] {\n  const newArray = [...array];\n  for (let i = newArray.length - 1; i > 0; i--) {\n    const j = Math.floor(Math.random() * (i + 1));\n    [newArray[i], newArray[j]] = [newArray[j], newArray[i]];\n  }\n  return newArray;\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;;;AAKO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAKO,SAAS;IACd,OAAO,uCAAuC,OAAO,CAAC,SAAS,SAAS,CAAC;QACvE,MAAM,IAAI,KAAK,MAAM,KAAK,KAAK;QAC/B,MAAM,IAAI,MAAM,MAAM,IAAK,IAAI,MAAM;QACrC,OAAO,EAAE,QAAQ,CAAC;IACpB;AACF;AAKO,SAAS,WAAW,IAAmB;IAC5C,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IACtD,OAAO,EAAE,kBAAkB,CAAC,SAAS;QACnC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAKO,SAAS,gBAAgB,OAAe;IAC7C,IAAI,UAAU,IAAI;QAChB,OAAO,GAAG,QAAQ,OAAO,EAAE,YAAY,IAAI,MAAM,IAAI;IACvD;IAEA,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU;IACnC,MAAM,mBAAmB,UAAU;IAEnC,IAAI,qBAAqB,GAAG;QAC1B,OAAO,GAAG,MAAM,KAAK,EAAE,UAAU,IAAI,MAAM,IAAI;IACjD;IAEA,OAAO,GAAG,MAAM,KAAK,EAAE,UAAU,IAAI,MAAM,GAAG,CAAC,EAAE,iBAAiB,OAAO,EAAE,qBAAqB,IAAI,MAAM,IAAI;AAChH;AAKO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,aAAa;AACpC;AAKO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI,UAAiC;IAErC,OAAO,SAAS,GAAG,IAAmB;QACpC,MAAM,QAAQ;YACZ,UAAU;YACV,QAAQ;QACV;QAEA,IAAI,YAAY,MAAM;YACpB,aAAa;QACf;QAEA,UAAU,WAAW,OAAO;IAC9B;AACF;AAKO,SAAS,oBAAoB,KAAa,EAAE,KAAa;IAC9D,IAAI,UAAU,GAAG,OAAO;IACxB,OAAO,KAAK,KAAK,CAAC,AAAC,QAAQ,QAAS;AACtC;AAKO,SAAS,aAAgB,KAAU;IACxC,MAAM,WAAW;WAAI;KAAM;IAC3B,IAAK,IAAI,IAAI,SAAS,MAAM,GAAG,GAAG,IAAI,GAAG,IAAK;QAC5C,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,CAAC,IAAI,CAAC;QAC3C,CAAC,QAAQ,CAAC,EAAE,EAAE,QAAQ,CAAC,EAAE,CAAC,GAAG;YAAC,QAAQ,CAAC,EAAE;YAAE,QAAQ,CAAC,EAAE;SAAC;IACzD;IACA,OAAO;AACT"}}, {"offset": {"line": 84, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 90, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/hacking-quiz/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport { Slot } from \"@radix-ui/react-slot\";\nimport { cva, type VariantProps } from \"class-variance-authority\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n);\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\";\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    );\n  }\n);\nButton.displayName = \"Button\";\n\nexport { Button, buttonVariants };\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;AAEA;AAHA;;;;;;AAKA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,sMAAM,UAAU,CAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG"}}, {"offset": {"line": 144, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 238, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/hacking-quiz/src/lib/db.ts"], "sourcesContent": ["import { PrismaClient } from \"@prisma/client\";\n\ndeclare global {\n  var prisma: PrismaClient | undefined;\n}\n\nexport const db = globalThis.prisma || new PrismaClient();\n\nif (process.env.NODE_ENV !== \"production\") {\n  globalThis.prisma = db;\n}\n"], "names": [], "mappings": ";;;AAAA;;AAMO,MAAM,KAAK,WAAW,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEvD,wCAA2C;IACzC,WAAW,MAAM,GAAG;AACtB"}}, {"offset": {"line": 247, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 253, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/hacking-quiz/src/lib/auth.ts"], "sourcesContent": ["import { PrismaAdapter } from \"@auth/prisma-adapter\";\nimport { NextAuthOptions } from \"next-auth\";\nimport CredentialsProvider from \"next-auth/providers/credentials\";\nimport GoogleProvider from \"next-auth/providers/google\";\nimport GitHubProvider from \"next-auth/providers/github\";\nimport bcrypt from \"bcrypt\";\n\nimport { db } from \"@/lib/db\";\n\nexport const authOptions: NextAuthOptions = {\n  adapter: PrismaAdapter(db),\n  session: {\n    strategy: \"jwt\",\n  },\n  pages: {\n    signIn: \"/auth/login\",\n    signOut: \"/auth/logout\",\n    error: \"/auth/error\",\n  },\n  providers: [\n    GoogleProvider({\n      clientId: process.env.GOOGLE_CLIENT_ID || \"\",\n      clientSecret: process.env.GOOGLE_CLIENT_SECRET || \"\",\n    }),\n    GitHubProvider({\n      clientId: process.env.GITHUB_CLIENT_ID || \"\",\n      clientSecret: process.env.GITHUB_CLIENT_SECRET || \"\",\n    }),\n    CredentialsProvider({\n      name: \"credentials\",\n      credentials: {\n        email: { label: \"Email\", type: \"email\" },\n        password: { label: \"Password\", type: \"password\" },\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.password) {\n          return null;\n        }\n\n        const user = await db.user.findUnique({\n          where: {\n            email: credentials.email,\n          },\n        });\n\n        if (!user || !user.password) {\n          return null;\n        }\n\n        const passwordMatch = await bcrypt.compare(\n          credentials.password,\n          user.password\n        );\n\n        if (!passwordMatch) {\n          return null;\n        }\n\n        return {\n          id: user.id,\n          name: user.name,\n          email: user.email,\n          image: user.image,\n          role: user.role,\n        };\n      },\n    }),\n  ],\n  callbacks: {\n    async session({ session, token }) {\n      if (token) {\n        session.user.id = token.id as string;\n        session.user.name = token.name;\n        session.user.email = token.email;\n        session.user.image = token.picture;\n        session.user.role = token.role as string;\n      }\n      return session;\n    },\n    async jwt({ token, user }) {\n      const dbUser = await db.user.findFirst({\n        where: {\n          email: token.email,\n        },\n      });\n\n      if (!dbUser) {\n        if (user) {\n          token.id = user.id;\n        }\n        return token;\n      }\n\n      return {\n        id: dbUser.id,\n        name: dbUser.name,\n        email: dbUser.email,\n        picture: dbUser.image,\n        role: dbUser.role,\n      };\n    },\n  },\n  secret: process.env.NEXTAUTH_SECRET,\n};\n"], "names": [], "mappings": ";;;AAAA;AAEA;AACA;AACA;AACA;AAEA;;;;;;;AAEO,MAAM,cAA+B;IAC1C,SAAS,CAAA,GAAA,oJAAA,CAAA,gBAAa,AAAD,EAAE,gHAAA,CAAA,KAAE;IACzB,SAAS;QACP,UAAU;IACZ;IACA,OAAO;QACL,QAAQ;QACR,SAAS;QACT,OAAO;IACT;IACA,WAAW;QACT,CAAA,GAAA,mJAAA,CAAA,UAAc,AAAD,EAAE;YACb,UAAU,QAAQ,GAAG,CAAC,gBAAgB,IAAI;YAC1C,cAAc,QAAQ,GAAG,CAAC,oBAAoB,IAAI;QACpD;QACA,CAAA,GAAA,mJAAA,CAAA,UAAc,AAAD,EAAE;YACb,UAAU,QAAQ,GAAG,CAAC,gBAAgB,IAAI;YAC1C,cAAc,QAAQ,GAAG,CAAC,oBAAoB,IAAI;QACpD;QACA,CAAA,GAAA,wJAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,OAAO;gBACT;gBAEA,MAAM,OAAO,MAAM,gHAAA,CAAA,KAAE,CAAC,IAAI,CAAC,UAAU,CAAC;oBACpC,OAAO;wBACL,OAAO,YAAY,KAAK;oBAC1B;gBACF;gBAEA,IAAI,CAAC,QAAQ,CAAC,KAAK,QAAQ,EAAE;oBAC3B,OAAO;gBACT;gBAEA,MAAM,gBAAgB,MAAM,qGAAA,CAAA,UAAM,CAAC,OAAO,CACxC,YAAY,QAAQ,EACpB,KAAK,QAAQ;gBAGf,IAAI,CAAC,eAAe;oBAClB,OAAO;gBACT;gBAEA,OAAO;oBACL,IAAI,KAAK,EAAE;oBACX,MAAM,KAAK,IAAI;oBACf,OAAO,KAAK,KAAK;oBACjB,OAAO,KAAK,KAAK;oBACjB,MAAM,KAAK,IAAI;gBACjB;YACF;QACF;KACD;IACD,WAAW;QACT,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,EAAE;gBAC1B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,IAAI,CAAC,KAAK,GAAG,MAAM,KAAK;gBAChC,QAAQ,IAAI,CAAC,KAAK,GAAG,MAAM,OAAO;gBAClC,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;YAChC;YACA,OAAO;QACT;QACA,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,MAAM,SAAS,MAAM,gHAAA,CAAA,KAAE,CAAC,IAAI,CAAC,SAAS,CAAC;gBACrC,OAAO;oBACL,OAAO,MAAM,KAAK;gBACpB;YACF;YAEA,IAAI,CAAC,QAAQ;gBACX,IAAI,MAAM;oBACR,MAAM,EAAE,GAAG,KAAK,EAAE;gBACpB;gBACA,OAAO;YACT;YAEA,OAAO;gBACL,IAAI,OAAO,EAAE;gBACb,MAAM,OAAO,IAAI;gBACjB,OAAO,OAAO,KAAK;gBACnB,SAAS,OAAO,KAAK;gBACrB,MAAM,OAAO,IAAI;YACnB;QACF;IACF;IACA,QAAQ,QAAQ,GAAG,CAAC,eAAe;AACrC"}}, {"offset": {"line": 359, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 365, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/hacking-quiz/src/app/page.tsx"], "sourcesContent": ["import Link from \"next/link\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { getServerSession } from \"next-auth\";\nimport { authOptions } from \"@/lib/auth\";\n\nexport default async function Home() {\n  const session = await getServerSession(authOptions);\n\n  return (\n    <div className=\"flex flex-col min-h-screen\">\n      {/* Navigation */}\n      <header className=\"border-b\">\n        <div className=\"container mx-auto px-4 flex h-16 items-center justify-between\">\n          <div className=\"flex items-center gap-6\">\n            <Link href=\"/\" className=\"text-xl font-bold\">\n              QuizFlow\n            </Link>\n            <nav className=\"hidden md:flex gap-6\">\n              <Link\n                href=\"/features\"\n                className=\"text-sm font-medium text-muted-foreground transition-colors hover:text-primary\"\n              >\n                Features\n              </Link>\n              <Link\n                href=\"/explore\"\n                className=\"text-sm font-medium text-muted-foreground transition-colors hover:text-primary\"\n              >\n                Explore\n              </Link>\n              <Link\n                href=\"/docs\"\n                className=\"text-sm font-medium text-muted-foreground transition-colors hover:text-primary\"\n              >\n                Documentation\n              </Link>\n            </nav>\n          </div>\n          <div className=\"flex items-center gap-4\">\n            {session ? (\n              <Button asChild>\n                <Link href=\"/dashboard\">Dashboard</Link>\n              </Button>\n            ) : (\n              <div className=\"flex items-center gap-4\">\n                <Button variant=\"outline\" asChild>\n                  <Link href=\"/auth/login\">Sign In</Link>\n                </Button>\n                <Button asChild>\n                  <Link href=\"/auth/register\">Sign Up</Link>\n                </Button>\n              </div>\n            )}\n          </div>\n        </div>\n      </header>\n\n      {/* Hero Section */}\n      <section className=\"py-20 md:py-28\">\n        <div className=\"container mx-auto px-4 text-center\">\n          <h1 className=\"text-4xl md:text-6xl font-bold mb-6\">\n            Create, Share, and Take Interactive Quizzes\n          </h1>\n          <p className=\"text-xl md:text-2xl text-muted-foreground mb-10 max-w-3xl mx-auto\">\n            QuizFlow is a standardized, flexible, and interactive quiz ecosystem for creating and sharing knowledge assessments.\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <Button size=\"lg\" asChild>\n              <Link href={session ? \"/dashboard/quizzes/create\" : \"/auth/register\"}>\n                {session ? \"Create a Quiz\" : \"Get Started\"}\n              </Link>\n            </Button>\n            <Button size=\"lg\" variant=\"outline\" asChild>\n              <Link href=\"/explore\">Explore Quizzes</Link>\n            </Button>\n          </div>\n        </div>\n      </section>\n\n      {/* Features Section */}\n      <section className=\"py-16 bg-muted/50\">\n        <div className=\"container mx-auto px-4\">\n          <h2 className=\"text-3xl font-bold text-center mb-12\">Key Features</h2>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n            <div className=\"bg-background p-6 rounded-lg shadow-sm\">\n              <h3 className=\"text-xl font-bold mb-3\">Standardized Format</h3>\n              <p className=\"text-muted-foreground\">\n                Create quizzes using our well-documented, extensible, and universally understandable JSON format.\n              </p>\n            </div>\n            <div className=\"bg-background p-6 rounded-lg shadow-sm\">\n              <h3 className=\"text-xl font-bold mb-3\">Rich Interactivity</h3>\n              <p className=\"text-muted-foreground\">\n                Support for multiple question types, media integration, and dynamic content for engaging learning experiences.\n              </p>\n            </div>\n            <div className=\"bg-background p-6 rounded-lg shadow-sm\">\n              <h3 className=\"text-xl font-bold mb-3\">Open Ecosystem</h3>\n              <p className=\"text-muted-foreground\">\n                An open-source foundation that encourages community involvement and integration with other systems.\n              </p>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"py-20 bg-primary text-primary-foreground\">\n        <div className=\"container mx-auto px-4 text-center\">\n          <h2 className=\"text-3xl font-bold mb-6\">Ready to create your first quiz?</h2>\n          <p className=\"text-xl mb-8 max-w-2xl mx-auto opacity-90\">\n            Join QuizFlow today and start creating interactive quizzes for education, training, or fun.\n          </p>\n          <Button size=\"lg\" variant=\"secondary\" asChild>\n            <Link href={session ? \"/dashboard\" : \"/auth/register\"}>\n              {session ? \"Go to Dashboard\" : \"Sign Up for Free\"}\n            </Link>\n          </Button>\n        </div>\n      </section>\n\n      {/* Footer */}\n      <footer className=\"py-12 border-t\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n            <div>\n              <h3 className=\"text-lg font-bold mb-4\">QuizFlow</h3>\n              <p className=\"text-muted-foreground\">\n                A standardized, flexible, and interactive quiz ecosystem.\n              </p>\n            </div>\n            <div>\n              <h4 className=\"font-medium mb-4\">Product</h4>\n              <ul className=\"space-y-2\">\n                <li>\n                  <Link href=\"/features\" className=\"text-muted-foreground hover:text-foreground\">\n                    Features\n                  </Link>\n                </li>\n                <li>\n                  <Link href=\"/explore\" className=\"text-muted-foreground hover:text-foreground\">\n                    Explore\n                  </Link>\n                </li>\n                <li>\n                  <Link href=\"/pricing\" className=\"text-muted-foreground hover:text-foreground\">\n                    Pricing\n                  </Link>\n                </li>\n              </ul>\n            </div>\n            <div>\n              <h4 className=\"font-medium mb-4\">Resources</h4>\n              <ul className=\"space-y-2\">\n                <li>\n                  <Link href=\"/docs\" className=\"text-muted-foreground hover:text-foreground\">\n                    Documentation\n                  </Link>\n                </li>\n                <li>\n                  <Link href=\"/guides\" className=\"text-muted-foreground hover:text-foreground\">\n                    Guides\n                  </Link>\n                </li>\n                <li>\n                  <Link href=\"/api\" className=\"text-muted-foreground hover:text-foreground\">\n                    API\n                  </Link>\n                </li>\n              </ul>\n            </div>\n            <div>\n              <h4 className=\"font-medium mb-4\">Company</h4>\n              <ul className=\"space-y-2\">\n                <li>\n                  <Link href=\"/about\" className=\"text-muted-foreground hover:text-foreground\">\n                    About\n                  </Link>\n                </li>\n                <li>\n                  <Link href=\"/blog\" className=\"text-muted-foreground hover:text-foreground\">\n                    Blog\n                  </Link>\n                </li>\n                <li>\n                  <Link href=\"/contact\" className=\"text-muted-foreground hover:text-foreground\">\n                    Contact\n                  </Link>\n                </li>\n              </ul>\n            </div>\n          </div>\n          <div className=\"mt-12 pt-8 border-t text-center text-muted-foreground\">\n            <p>&copy; {new Date().getFullYear()} QuizFlow. All rights reserved.</p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;;AAEe,eAAe;IAC5B,MAAM,UAAU,MAAM,CAAA,GAAA,qIAAA,CAAA,mBAAgB,AAAD,EAAE,kHAAA,CAAA,cAAW;IAElD,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAoB;;;;;;8CAG7C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;;;;;;;sCAKL,8OAAC;4BAAI,WAAU;sCACZ,wBACC,8OAAC,kIAAA,CAAA,SAAM;gCAAC,OAAO;0CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;8CAAa;;;;;;;;;;qDAG1B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,OAAO;kDAC/B,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;sDAAc;;;;;;;;;;;kDAE3B,8OAAC,kIAAA,CAAA,SAAM;wCAAC,OAAO;kDACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;sDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASxC,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAsC;;;;;;sCAGpD,8OAAC;4BAAE,WAAU;sCAAoE;;;;;;sCAGjF,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAK,OAAO;8CACvB,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAM,UAAU,8BAA8B;kDACjD,UAAU,kBAAkB;;;;;;;;;;;8CAGjC,8OAAC,kIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAK,SAAQ;oCAAU,OAAO;8CACzC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO9B,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAuC;;;;;;sCACrD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAyB;;;;;;sDACvC,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAIvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAyB;;;;;;sDACvC,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAIvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAyB;;;;;;sDACvC,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS7C,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA0B;;;;;;sCACxC,8OAAC;4BAAE,WAAU;sCAA4C;;;;;;sCAGzD,8OAAC,kIAAA,CAAA,SAAM;4BAAC,MAAK;4BAAK,SAAQ;4BAAY,OAAO;sCAC3C,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAM,UAAU,eAAe;0CAClC,UAAU,oBAAoB;;;;;;;;;;;;;;;;;;;;;;0BAOvC,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAyB;;;;;;sDACvC,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAIvC,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAmB;;;;;;sDACjC,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAY,WAAU;kEAA8C;;;;;;;;;;;8DAIjF,8OAAC;8DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAW,WAAU;kEAA8C;;;;;;;;;;;8DAIhF,8OAAC;8DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAW,WAAU;kEAA8C;;;;;;;;;;;;;;;;;;;;;;;8CAMpF,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAmB;;;;;;sDACjC,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAQ,WAAU;kEAA8C;;;;;;;;;;;8DAI7E,8OAAC;8DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAU,WAAU;kEAA8C;;;;;;;;;;;8DAI/E,8OAAC;8DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAO,WAAU;kEAA8C;;;;;;;;;;;;;;;;;;;;;;;8CAMhF,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAmB;;;;;;sDACjC,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAS,WAAU;kEAA8C;;;;;;;;;;;8DAI9E,8OAAC;8DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAQ,WAAU;kEAA8C;;;;;;;;;;;8DAI7E,8OAAC;8DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAW,WAAU;kEAA8C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOtF,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;;oCAAE;oCAAQ,IAAI,OAAO,WAAW;oCAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMhD"}}, {"offset": {"line": 1029, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1040, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 1040, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}