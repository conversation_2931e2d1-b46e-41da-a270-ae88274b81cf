(()=>{var e={};e.id=983,e.ids=[983],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},92516:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>c,pages:()=>u,routeModule:()=>p,tree:()=>l});var n=r(70260),s=r(28203),a=r(25155),o=r.n(a),i=r(67292),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);r.d(t,d);let l=["",{children:["auth",{children:["register",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,49956)),"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/app/auth/register/page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,71354)),"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],u=["/Users/<USER>/Documents/augment-projects/hacking-quiz/src/app/auth/register/page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},p=new n.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/auth/register/page",pathname:"/auth/register",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},29214:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,13219,23)),Promise.resolve().then(r.t.bind(r,34863,23)),Promise.resolve().then(r.t.bind(r,25155,23)),Promise.resolve().then(r.t.bind(r,40802,23)),Promise.resolve().then(r.t.bind(r,9350,23)),Promise.resolve().then(r.t.bind(r,48530,23)),Promise.resolve().then(r.t.bind(r,88921,23))},69382:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,66959,23)),Promise.resolve().then(r.t.bind(r,33875,23)),Promise.resolve().then(r.t.bind(r,88903,23)),Promise.resolve().then(r.t.bind(r,57174,23)),Promise.resolve().then(r.t.bind(r,84178,23)),Promise.resolve().then(r.t.bind(r,87190,23)),Promise.resolve().then(r.t.bind(r,61365,23))},41303:(e,t,r)=>{Promise.resolve().then(r.bind(r,28445))},69327:(e,t,r)=>{Promise.resolve().then(r.bind(r,82649))},60462:(e,t,r)=>{Promise.resolve().then(r.bind(r,49956))},23510:(e,t,r)=>{Promise.resolve().then(r.bind(r,75616))},75616:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var n=r(45512),s=r(58009),a=r(79334),o=r(28531),i=r.n(o),d=r(39400),l=r(64590);function u(){let e=(0,a.useRouter)(),[t,r]=(0,s.useState)(""),[o,u]=(0,s.useState)(""),[c,p]=(0,s.useState)(""),[m,f]=(0,s.useState)(""),[x,h]=(0,s.useState)(null),[v,g]=(0,s.useState)(!1),b=async r=>{if(r.preventDefault(),g(!0),h(null),c!==m){h("Passwords do not match"),g(!1);return}try{let r=await fetch("/api/auth/register",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:t,email:o,password:c})}),n=await r.json();if(!r.ok)throw Error(n.message||"Something went wrong");e.push("/auth/login?registered=true")}catch(e){e instanceof Error?h(e.message):h("An error occurred. Please try again."),g(!1)}};return(0,n.jsx)("div",{className:"flex min-h-screen items-center justify-center p-4",children:(0,n.jsxs)(l.Zp,{className:"w-full max-w-md",children:[(0,n.jsxs)(l.aR,{className:"space-y-1",children:[(0,n.jsx)(l.ZB,{className:"text-2xl font-bold",children:"Create an account"}),(0,n.jsx)(l.BT,{children:"Enter your information to create an account"})]}),(0,n.jsx)(l.Wu,{children:(0,n.jsxs)("form",{onSubmit:b,className:"space-y-4",children:[x&&(0,n.jsx)("div",{className:"p-3 text-sm bg-red-50 text-red-500 rounded-md",children:x}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)("label",{htmlFor:"name",className:"text-sm font-medium leading-none",children:"Name"}),(0,n.jsx)("input",{id:"name",type:"text",value:t,onChange:e=>r(e.target.value),className:"w-full p-2 border rounded-md",required:!0})]}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)("label",{htmlFor:"email",className:"text-sm font-medium leading-none",children:"Email"}),(0,n.jsx)("input",{id:"email",type:"email",value:o,onChange:e=>u(e.target.value),className:"w-full p-2 border rounded-md",required:!0})]}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)("label",{htmlFor:"password",className:"text-sm font-medium leading-none",children:"Password"}),(0,n.jsx)("input",{id:"password",type:"password",value:c,onChange:e=>p(e.target.value),className:"w-full p-2 border rounded-md",required:!0})]}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)("label",{htmlFor:"confirmPassword",className:"text-sm font-medium leading-none",children:"Confirm Password"}),(0,n.jsx)("input",{id:"confirmPassword",type:"password",value:m,onChange:e=>f(e.target.value),className:"w-full p-2 border rounded-md",required:!0})]}),(0,n.jsx)(d.$,{type:"submit",className:"w-full",disabled:v,children:v?"Creating account...":"Register"})]})}),(0,n.jsx)(l.wL,{className:"flex justify-center",children:(0,n.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Already have an account?"," ",(0,n.jsx)(i(),{href:"/auth/login",className:"text-primary hover:underline",children:"Sign in"})]})})]})})}},82649:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});var n=r(45512),s=r(90993);function a({children:e}){return(0,n.jsx)(s.SessionProvider,{children:e})}},39400:(e,t,r)=>{"use strict";r.d(t,{$:()=>l});var n=r(45512),s=r(58009),a=r(89383),o=r(21643),i=r(96720);let d=(0,o.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),l=s.forwardRef(({className:e,variant:t,size:r,asChild:s=!1,...o},l)=>{let u=s?a.DX:"button";return(0,n.jsx)(u,{className:(0,i.cn)(d({variant:t,size:r,className:e})),ref:l,...o})});l.displayName="Button"},64590:(e,t,r)=>{"use strict";r.d(t,{BT:()=>l,Wu:()=>u,ZB:()=>d,Zp:()=>o,aR:()=>i,wL:()=>c});var n=r(45512),s=r(58009),a=r(96720);let o=s.forwardRef(({className:e,...t},r)=>(0,n.jsx)("div",{ref:r,className:(0,a.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));o.displayName="Card";let i=s.forwardRef(({className:e,...t},r)=>(0,n.jsx)("div",{ref:r,className:(0,a.cn)("flex flex-col space-y-1.5 p-6",e),...t}));i.displayName="CardHeader";let d=s.forwardRef(({className:e,...t},r)=>(0,n.jsx)("h3",{ref:r,className:(0,a.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));d.displayName="CardTitle";let l=s.forwardRef(({className:e,...t},r)=>(0,n.jsx)("p",{ref:r,className:(0,a.cn)("text-sm text-muted-foreground",e),...t}));l.displayName="CardDescription";let u=s.forwardRef(({className:e,...t},r)=>(0,n.jsx)("div",{ref:r,className:(0,a.cn)("p-6 pt-0",e),...t}));u.displayName="CardContent";let c=s.forwardRef(({className:e,...t},r)=>(0,n.jsx)("div",{ref:r,className:(0,a.cn)("flex items-center p-6 pt-0",e),...t}));c.displayName="CardFooter"},96720:(e,t,r)=>{"use strict";r.d(t,{Yq:()=>i,cn:()=>a,lk:()=>o});var n=r(82281),s=r(94805);function a(...e){return(0,s.QP)((0,n.$)(e))}function o(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){let t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)})}function i(e){return("string"==typeof e?new Date(e):e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})}},37301:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createDedupedByCallsiteServerErrorLoggerDev",{enumerable:!0,get:function(){return d}});let n=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=s(void 0);if(r&&r.has(e))return r.get(e);var n={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&Object.prototype.hasOwnProperty.call(e,o)){var i=a?Object.getOwnPropertyDescriptor(e,o):null;i&&(i.get||i.set)?Object.defineProperty(n,o,i):n[o]=e[o]}return n.default=e,r&&r.set(e,n),n}(r(76301));function s(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(s=function(e){return e?r:t})(e)}let a={current:null},o="function"==typeof n.cache?n.cache:e=>e,i=console.warn;function d(e){return function(...t){i(e(...t))}}o(e=>{try{i(a.current)}finally{a.current=null}})},49956:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/app/auth/register/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/app/auth/register/page.tsx","default")},71354:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u,metadata:()=>l});var n=r(62740),s=r(2202),a=r.n(s),o=r(64988),i=r.n(o);r(61135);var d=r(28445);let l={title:"QuizFlow - Interactive Quiz Ecosystem",description:"A standardized, flexible, and interactive quiz ecosystem for creating and taking quizzes"};function u({children:e}){return(0,n.jsx)("html",{lang:"en",children:(0,n.jsx)("body",{className:`${a().variable} ${i().variable} antialiased min-h-screen bg-background text-foreground`,children:(0,n.jsx)(d.default,{children:e})})})}},28445:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});let n=(0,r(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/components/auth/SessionProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/components/auth/SessionProvider.tsx","default")},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var n=r(88077);let s=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,n.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},61135:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[638,3,367,920],()=>r(92516));module.exports=n})();