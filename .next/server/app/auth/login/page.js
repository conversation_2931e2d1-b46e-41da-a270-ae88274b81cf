(()=>{var e={};e.id=859,e.ids=[859],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},28294:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var n=r(70260),s=r(28203),a=r(25155),i=r.n(a),o=r(67292),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d=["",{children:["auth",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,29392)),"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/app/auth/login/page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,71354)),"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["/Users/<USER>/Documents/augment-projects/hacking-quiz/src/app/auth/login/page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new n.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/auth/login/page",pathname:"/auth/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},29214:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,13219,23)),Promise.resolve().then(r.t.bind(r,34863,23)),Promise.resolve().then(r.t.bind(r,25155,23)),Promise.resolve().then(r.t.bind(r,40802,23)),Promise.resolve().then(r.t.bind(r,9350,23)),Promise.resolve().then(r.t.bind(r,48530,23)),Promise.resolve().then(r.t.bind(r,88921,23))},69382:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,66959,23)),Promise.resolve().then(r.t.bind(r,33875,23)),Promise.resolve().then(r.t.bind(r,88903,23)),Promise.resolve().then(r.t.bind(r,57174,23)),Promise.resolve().then(r.t.bind(r,84178,23)),Promise.resolve().then(r.t.bind(r,87190,23)),Promise.resolve().then(r.t.bind(r,61365,23))},41303:(e,t,r)=>{Promise.resolve().then(r.bind(r,28445))},69327:(e,t,r)=>{Promise.resolve().then(r.bind(r,82649))},83192:(e,t,r)=>{Promise.resolve().then(r.bind(r,29392))},92920:(e,t,r)=>{Promise.resolve().then(r.bind(r,70564))},70564:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var n=r(45512),s=r(58009),a=r(90993),i=r(79334),o=r(28531),l=r.n(o),d=r(39400),c=r(64590);function u(){let e=(0,i.useRouter)(),[t,r]=(0,s.useState)(""),[o,u]=(0,s.useState)(""),[p,m]=(0,s.useState)(null),[f,x]=(0,s.useState)(!1),h=async r=>{r.preventDefault(),x(!0),m(null);try{let r=await (0,a.signIn)("credentials",{email:t,password:o,redirect:!1});if(r?.error){m("Invalid email or password"),x(!1);return}e.push("/dashboard"),e.refresh()}catch(e){m("An error occurred. Please try again."),x(!1)}};return(0,n.jsx)("div",{className:"flex min-h-screen items-center justify-center p-4",children:(0,n.jsxs)(c.Zp,{className:"w-full max-w-md",children:[(0,n.jsxs)(c.aR,{className:"space-y-1",children:[(0,n.jsx)(c.ZB,{className:"text-2xl font-bold",children:"Login"}),(0,n.jsx)(c.BT,{children:"Enter your credentials to access your account"})]}),(0,n.jsxs)(c.Wu,{children:[(0,n.jsxs)("form",{onSubmit:h,className:"space-y-4",children:[p&&(0,n.jsx)("div",{className:"p-3 text-sm bg-red-50 text-red-500 rounded-md",children:p}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)("label",{htmlFor:"email",className:"text-sm font-medium leading-none",children:"Email"}),(0,n.jsx)("input",{id:"email",type:"email",value:t,onChange:e=>r(e.target.value),className:"w-full p-2 border rounded-md",required:!0})]}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsx)("label",{htmlFor:"password",className:"text-sm font-medium leading-none",children:"Password"}),(0,n.jsx)(l(),{href:"/auth/forgot-password",className:"text-sm text-primary hover:underline",children:"Forgot password?"})]}),(0,n.jsx)("input",{id:"password",type:"password",value:o,onChange:e=>u(e.target.value),className:"w-full p-2 border rounded-md",required:!0})]}),(0,n.jsx)(d.$,{type:"submit",className:"w-full",disabled:f,children:f?"Logging in...":"Login"})]}),(0,n.jsxs)("div",{className:"relative my-4",children:[(0,n.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,n.jsx)("div",{className:"w-full border-t"})}),(0,n.jsx)("div",{className:"relative flex justify-center text-xs uppercase",children:(0,n.jsx)("span",{className:"bg-background px-2 text-muted-foreground",children:"Or continue with"})})]}),(0,n.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,n.jsx)(d.$,{variant:"outline",onClick:()=>(0,a.signIn)("google",{callbackUrl:"/dashboard"}),disabled:f,children:"Google"}),(0,n.jsx)(d.$,{variant:"outline",onClick:()=>(0,a.signIn)("github",{callbackUrl:"/dashboard"}),disabled:f,children:"GitHub"})]})]}),(0,n.jsx)(c.wL,{className:"flex justify-center",children:(0,n.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Don't have an account?"," ",(0,n.jsx)(l(),{href:"/auth/register",className:"text-primary hover:underline",children:"Sign up"})]})})]})})}},82649:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});var n=r(45512),s=r(90993);function a({children:e}){return(0,n.jsx)(s.SessionProvider,{children:e})}},39400:(e,t,r)=>{"use strict";r.d(t,{$:()=>d});var n=r(45512),s=r(58009),a=r(89383),i=r(21643),o=r(96720);let l=(0,i.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=s.forwardRef(({className:e,variant:t,size:r,asChild:s=!1,...i},d)=>{let c=s?a.DX:"button";return(0,n.jsx)(c,{className:(0,o.cn)(l({variant:t,size:r,className:e})),ref:d,...i})});d.displayName="Button"},64590:(e,t,r)=>{"use strict";r.d(t,{BT:()=>d,Wu:()=>c,ZB:()=>l,Zp:()=>i,aR:()=>o,wL:()=>u});var n=r(45512),s=r(58009),a=r(96720);let i=s.forwardRef(({className:e,...t},r)=>(0,n.jsx)("div",{ref:r,className:(0,a.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));i.displayName="Card";let o=s.forwardRef(({className:e,...t},r)=>(0,n.jsx)("div",{ref:r,className:(0,a.cn)("flex flex-col space-y-1.5 p-6",e),...t}));o.displayName="CardHeader";let l=s.forwardRef(({className:e,...t},r)=>(0,n.jsx)("h3",{ref:r,className:(0,a.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));l.displayName="CardTitle";let d=s.forwardRef(({className:e,...t},r)=>(0,n.jsx)("p",{ref:r,className:(0,a.cn)("text-sm text-muted-foreground",e),...t}));d.displayName="CardDescription";let c=s.forwardRef(({className:e,...t},r)=>(0,n.jsx)("div",{ref:r,className:(0,a.cn)("p-6 pt-0",e),...t}));c.displayName="CardContent";let u=s.forwardRef(({className:e,...t},r)=>(0,n.jsx)("div",{ref:r,className:(0,a.cn)("flex items-center p-6 pt-0",e),...t}));u.displayName="CardFooter"},96720:(e,t,r)=>{"use strict";r.d(t,{Yq:()=>o,cn:()=>a,lk:()=>i});var n=r(82281),s=r(94805);function a(...e){return(0,s.QP)((0,n.$)(e))}function i(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){let t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)})}function o(e){return("string"==typeof e?new Date(e):e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})}},37301:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createDedupedByCallsiteServerErrorLoggerDev",{enumerable:!0,get:function(){return l}});let n=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=s(void 0);if(r&&r.has(e))return r.get(e);var n={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&Object.prototype.hasOwnProperty.call(e,i)){var o=a?Object.getOwnPropertyDescriptor(e,i):null;o&&(o.get||o.set)?Object.defineProperty(n,i,o):n[i]=e[i]}return n.default=e,r&&r.set(e,n),n}(r(76301));function s(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(s=function(e){return e?r:t})(e)}let a={current:null},i="function"==typeof n.cache?n.cache:e=>e,o=console.warn;function l(e){return function(...t){o(e(...t))}}i(e=>{try{o(a.current)}finally{a.current=null}})},29392:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/app/auth/login/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/app/auth/login/page.tsx","default")},71354:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c,metadata:()=>d});var n=r(62740),s=r(2202),a=r.n(s),i=r(64988),o=r.n(i);r(61135);var l=r(28445);let d={title:"QuizFlow - Interactive Quiz Ecosystem",description:"A standardized, flexible, and interactive quiz ecosystem for creating and taking quizzes"};function c({children:e}){return(0,n.jsx)("html",{lang:"en",children:(0,n.jsx)("body",{className:`${a().variable} ${o().variable} antialiased min-h-screen bg-background text-foreground`,children:(0,n.jsx)(l.default,{children:e})})})}},28445:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});let n=(0,r(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/components/auth/SessionProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/components/auth/SessionProvider.tsx","default")},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var n=r(88077);let s=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,n.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},61135:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[638,3,367,920],()=>r(28294));module.exports=n})();