(()=>{var e={};e.id=200,e.ids=[200],e.modules={96330:e=>{"use strict";e.exports=require("@prisma/client")},5486:e=>{"use strict";e.exports=require("bcrypt")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},12412:e=>{"use strict";e.exports=require("assert")},79428:e=>{"use strict";e.exports=require("buffer")},55511:e=>{"use strict";e.exports=require("crypto")},94735:e=>{"use strict";e.exports=require("events")},81630:e=>{"use strict";e.exports=require("http")},55591:e=>{"use strict";e.exports=require("https")},33873:e=>{"use strict";e.exports=require("path")},11723:e=>{"use strict";e.exports=require("querystring")},79551:e=>{"use strict";e.exports=require("url")},28354:e=>{"use strict";e.exports=require("util")},74075:e=>{"use strict";e.exports=require("zlib")},8218:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>c,pages:()=>d,routeModule:()=>p,tree:()=>u});var n=r(70260),i=r(28203),s=r(25155),o=r.n(s),l=r(67292),a={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(a[e]=()=>l[e]);r.d(t,a);let u=["",{children:["quiz",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,4927)),"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/app/quiz/[id]/page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,71354)),"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["/Users/<USER>/Documents/augment-projects/hacking-quiz/src/app/quiz/[id]/page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},p=new n.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/quiz/[id]/page",pathname:"/quiz/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},14435:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,59607,23)),Promise.resolve().then(r.bind(r,29353))},28507:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,28531,23)),Promise.resolve().then(r.bind(r,32137))},86256:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return i}});let n=r(25488)._(r(40568));function i(e,t){var r;let i={};"function"==typeof e&&(i.loader=e);let s={...i,...t};return(0,n.default)({...s,modules:null==(r=s.loadableGenerated)?void 0:r.modules})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3400:(e,t)=>{"use strict";function r(e){return e.split("/").map(e=>encodeURIComponent(e)).join("/")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"encodeURIPath",{enumerable:!0,get:function(){return r}})},65771:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"BailoutToCSR",{enumerable:!0,get:function(){return i}});let n=r(54639);function i(e){let{reason:t,children:r}=e;throw new n.BailoutToCSRError(t)}},40568:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return u}});let n=r(45512),i=r(58009),s=r(65771),o=r(86054);function l(e){return{default:e&&"default"in e?e.default:e}}let a={loader:()=>Promise.resolve(l(()=>null)),loading:null,ssr:!0},u=function(e){let t={...a,...e},r=(0,i.lazy)(()=>t.loader().then(l)),u=t.loading;function d(e){let l=u?(0,n.jsx)(u,{isLoading:!0,pastDelay:!0,error:null}):null,a=!t.ssr||!!t.loading,d=a?i.Suspense:i.Fragment,c=t.ssr?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(o.PreloadChunks,{moduleIds:t.modules}),(0,n.jsx)(r,{...e})]}):(0,n.jsx)(s.BailoutToCSR,{reason:"next/dynamic",children:(0,n.jsx)(r,{...e})});return(0,n.jsx)(d,{...a?{fallback:l}:{},children:c})}return d.displayName="LoadableComponent",d}},86054:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PreloadChunks",{enumerable:!0,get:function(){return l}});let n=r(45512),i=r(55740),s=r(29294),o=r(3400);function l(e){let{moduleIds:t}=e,r=s.workAsyncStorage.getStore();if(void 0===r)return null;let l=[];if(r.reactLoadableManifest&&t){let e=r.reactLoadableManifest;for(let r of t){if(!e[r])continue;let t=e[r].files;l.push(...t)}}return 0===l.length?null:(0,n.jsx)(n.Fragment,{children:l.map(e=>{let t=r.assetPrefix+"/_next/"+(0,o.encodeURIPath)(e);return e.endsWith(".css")?(0,n.jsx)("link",{precedence:"dynamic",href:t,rel:"stylesheet",as:"style"},e):((0,i.preload)(t,{as:"script",fetchPriority:"low"}),null)})})}},32137:(e,t,r)=>{"use strict";r.d(t,{default:()=>o});var n=r(45512);r(58009);var i=r(86256);let s=r.n(i)()(async()=>{},{loadableGenerated:{modules:["components/quiz/QuizClient.tsx -> ./QuizRenderer"]},ssr:!1}),o=({quiz:e})=>(0,n.jsx)(s,{quiz:e})},59607:(e,t,r)=>{let{createProxy:n}=r(73439);e.exports=n("/Users/<USER>/Documents/augment-projects/hacking-quiz/node_modules/next/dist/client/app-dir/link.js")},4927:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p});var n=r(62740),i=r(31831),s=r(59607),o=r.n(s),l=r(51825),a=r(37702),u=r(27914),d=r(13797),c=r(29353);async function p({params:e}){let t=await (0,l.getServerSession)(a.N),r=await u.db.quiz.findUnique({where:{id:e.id},include:{creator:{select:{name:!0}},questions:!0,questionPools:{include:{questions:!0}},selectionRules:!0}});r&&(r.isPublished||r.creatorId===t?.user.id)||(0,i.notFound)();let s={quiz:{$schema:"https://quizflow.org/schemas/quizflow_schema_v1.1.json",metadata:{format_version:r.formatVersion,quiz_id:r.quizId,title:r.title,description:r.description||void 0,author:r.creator?.name||r.author||void 0,creation_date:r.creationDate.toISOString(),tags:r.tags,passing_score_percentage:r.passingScore||void 0,time_limit_minutes:r.timeLimit||void 0,markup_format:r.markupFormat,locale:r.locale},questions:r.questions.map(e=>({...e,id:void 0,quizId:void 0,createdAt:void 0,updatedAt:void 0,questionPoolId:void 0})),question_pools:r.questionPools.length>0?r.questionPools.map(e=>({pool_id:e.poolId,title:e.title||void 0,description:e.description||void 0,questions:e.questions.map(e=>({...e,id:void 0,quizId:void 0,createdAt:void 0,updatedAt:void 0,questionPoolId:void 0}))})):void 0,selection_rules:r.selectionRules.length>0?r.selectionRules.map(e=>({pool_id:e.poolId,select_count:e.selectCount,randomize:e.randomize,shuffle_order:e.shuffleOrder})):void 0}};return(0,n.jsxs)("div",{className:"flex flex-col min-h-screen",children:[(0,n.jsx)("header",{className:"border-b",children:(0,n.jsxs)("div",{className:"container mx-auto px-4 flex h-16 items-center justify-between",children:[(0,n.jsx)("div",{className:"flex items-center gap-6",children:(0,n.jsx)(o(),{href:"/",className:"text-xl font-bold",children:"QuizFlow"})}),(0,n.jsx)("div",{className:"flex items-center gap-4",children:t?(0,n.jsx)(d.$,{asChild:!0,children:(0,n.jsx)(o(),{href:"/dashboard",children:"Dashboard"})}):(0,n.jsxs)("div",{className:"flex items-center gap-4",children:[(0,n.jsx)(d.$,{variant:"outline",asChild:!0,children:(0,n.jsx)(o(),{href:"/auth/login",children:"Sign In"})}),(0,n.jsx)(d.$,{asChild:!0,children:(0,n.jsx)(o(),{href:"/auth/register",children:"Sign Up"})})]})})]})}),(0,n.jsx)("main",{className:"flex-1 py-8",children:(0,n.jsxs)("div",{className:"container mx-auto px-4",children:[(0,n.jsx)("div",{className:"mb-8",children:(0,n.jsxs)(o(),{href:"/explore",className:"text-primary hover:underline flex items-center",children:[(0,n.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 19l-7-7m0 0l7-7m-7 7h18"})}),"Back to Explore"]})}),(0,n.jsx)(c.default,{quiz:s})]})}),(0,n.jsx)("footer",{className:"py-8 border-t",children:(0,n.jsx)("div",{className:"container mx-auto px-4 text-center",children:(0,n.jsxs)("p",{className:"text-muted-foreground",children:["\xa9 ",new Date().getFullYear()," QuizFlow. All rights reserved."]})})})]})}},29353:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});let n=(0,r(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/components/quiz/QuizClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/components/quiz/QuizClient.tsx","default")},13797:(e,t,r)=>{"use strict";r.d(t,{$:()=>m});var n=r(62740),i=r(76301);function s(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}var o=function(e){let t=function(e){let t=i.forwardRef((e,t)=>{let{children:r,...n}=e;if(i.isValidElement(r)){let e,o;let l=(e=Object.getOwnPropertyDescriptor(r.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.ref:(e=Object.getOwnPropertyDescriptor(r,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.props.ref:r.props.ref||r.ref,a=function(e,t){let r={...t};for(let n in t){let i=e[n],s=t[n];/^on[A-Z]/.test(n)?i&&s?r[n]=(...e)=>{let t=s(...e);return i(...e),t}:i&&(r[n]=i):"style"===n?r[n]={...i,...s}:"className"===n&&(r[n]=[i,s].filter(Boolean).join(" "))}return{...e,...r}}(n,r.props);return r.type!==i.Fragment&&(a.ref=t?function(...e){return t=>{let r=!1,n=e.map(e=>{let n=s(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():s(e[t],null)}}}}(t,l):l),i.cloneElement(r,a)}return i.Children.count(r)>1?i.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=i.forwardRef((e,r)=>{let{children:s,...o}=e,l=i.Children.toArray(s),u=l.find(a);if(u){let e=u.props.children,s=l.map(t=>t!==u?t:i.Children.count(e)>1?i.Children.only(null):i.isValidElement(e)?e.props.children:null);return(0,n.jsx)(t,{...o,ref:r,children:i.isValidElement(e)?i.cloneElement(e,void 0,s):null})}return(0,n.jsx)(t,{...o,ref:r,children:s})});return r.displayName=`${e}.Slot`,r}("Slot"),l=Symbol("radix.slottable");function a(e){return i.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===l}var u=r(13673);let d=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,c=u.$;var p=r(73300);let f=((e,t)=>r=>{var n;if((null==t?void 0:t.variants)==null)return c(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:i,defaultVariants:s}=t,o=Object.keys(i).map(e=>{let t=null==r?void 0:r[e],n=null==s?void 0:s[e];if(null===t)return null;let o=d(t)||d(n);return i[e][o]}),l=r&&Object.entries(r).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return c(e,o,null==t?void 0:null===(n=t.compoundVariants)||void 0===n?void 0:n.reduce((e,t)=>{let{class:r,className:n,...i}=t;return Object.entries(i).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...s,...l}[t]):({...s,...l})[t]===r})?[...e,r,n]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)})("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),m=i.forwardRef(({className:e,variant:t,size:r,asChild:i=!1,...s},l)=>{let a=i?o:"button";return(0,n.jsx)(a,{className:(0,p.cn)(f({variant:t,size:r,className:e})),ref:l,...s})});m.displayName="Button"},73300:(e,t,r)=>{"use strict";r.d(t,{Yq:()=>o,cn:()=>s});var n=r(13673),i=r(47317);function s(...e){return(0,i.QP)((0,n.$)(e))}function o(e){return("string"==typeof e?new Date(e):e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[638,263,3,367,85,30],()=>r(8218));module.exports=n})();