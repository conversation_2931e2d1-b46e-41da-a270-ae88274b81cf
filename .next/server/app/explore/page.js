(()=>{var e={};e.id=314,e.ids=[314],e.modules={96330:e=>{"use strict";e.exports=require("@prisma/client")},5486:e=>{"use strict";e.exports=require("bcrypt")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},12412:e=>{"use strict";e.exports=require("assert")},79428:e=>{"use strict";e.exports=require("buffer")},55511:e=>{"use strict";e.exports=require("crypto")},94735:e=>{"use strict";e.exports=require("events")},81630:e=>{"use strict";e.exports=require("http")},55591:e=>{"use strict";e.exports=require("https")},33873:e=>{"use strict";e.exports=require("path")},11723:e=>{"use strict";e.exports=require("querystring")},79551:e=>{"use strict";e.exports=require("url")},28354:e=>{"use strict";e.exports=require("util")},74075:e=>{"use strict";e.exports=require("zlib")},93158:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var s=t(70260),i=t(28203),n=t(25155),a=t.n(n),l=t(67292),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);t.d(r,o);let d=["",{children:["explore",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,34013)),"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/app/explore/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,71354)),"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["/Users/<USER>/Documents/augment-projects/hacking-quiz/src/app/explore/page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/explore/page",pathname:"/explore",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},66202:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,59607,23))},482:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,28531,23))},29214:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,13219,23)),Promise.resolve().then(t.t.bind(t,34863,23)),Promise.resolve().then(t.t.bind(t,25155,23)),Promise.resolve().then(t.t.bind(t,40802,23)),Promise.resolve().then(t.t.bind(t,9350,23)),Promise.resolve().then(t.t.bind(t,48530,23)),Promise.resolve().then(t.t.bind(t,88921,23))},69382:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,66959,23)),Promise.resolve().then(t.t.bind(t,33875,23)),Promise.resolve().then(t.t.bind(t,88903,23)),Promise.resolve().then(t.t.bind(t,57174,23)),Promise.resolve().then(t.t.bind(t,84178,23)),Promise.resolve().then(t.t.bind(t,87190,23)),Promise.resolve().then(t.t.bind(t,61365,23))},41303:(e,r,t)=>{Promise.resolve().then(t.bind(t,28445))},69327:(e,r,t)=>{Promise.resolve().then(t.bind(t,82649))},82649:(e,r,t)=>{"use strict";t.d(r,{default:()=>n});var s=t(45512),i=t(90993);function n({children:e}){return(0,s.jsx)(i.SessionProvider,{children:e})}},59607:(e,r,t)=>{let{createProxy:s}=t(73439);e.exports=s("/Users/<USER>/Documents/augment-projects/hacking-quiz/node_modules/next/dist/client/app-dir/link.js")},34013:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>m});var s=t(62740),i=t(59607),n=t.n(i),a=t(51825),l=t(37702),o=t(27914),d=t(13797),c=t(99818),u=t(73300);async function m(){let e=await (0,a.getServerSession)(l.N),r=await o.db.quiz.findMany({where:{isPublished:!0},include:{creator:{select:{name:!0}},_count:{select:{questions:!0}}},orderBy:{updatedAt:"desc"},take:12});return(0,s.jsxs)("div",{className:"flex flex-col min-h-screen",children:[(0,s.jsx)("header",{className:"border-b",children:(0,s.jsxs)("div",{className:"container mx-auto px-4 flex h-16 items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-6",children:[(0,s.jsx)(n(),{href:"/",className:"text-xl font-bold",children:"QuizFlow"}),(0,s.jsxs)("nav",{className:"hidden md:flex gap-6",children:[(0,s.jsx)(n(),{href:"/features",className:"text-sm font-medium text-muted-foreground transition-colors hover:text-primary",children:"Features"}),(0,s.jsx)(n(),{href:"/explore",className:"text-sm font-medium text-muted-foreground transition-colors hover:text-primary",children:"Explore"}),(0,s.jsx)(n(),{href:"/docs",className:"text-sm font-medium text-muted-foreground transition-colors hover:text-primary",children:"Documentation"})]})]}),(0,s.jsx)("div",{className:"flex items-center gap-4",children:e?(0,s.jsx)(d.$,{asChild:!0,children:(0,s.jsx)(n(),{href:"/dashboard",children:"Dashboard"})}):(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsx)(d.$,{variant:"outline",asChild:!0,children:(0,s.jsx)(n(),{href:"/auth/login",children:"Sign In"})}),(0,s.jsx)(d.$,{asChild:!0,children:(0,s.jsx)(n(),{href:"/auth/register",children:"Sign Up"})})]})})]})}),(0,s.jsx)("main",{className:"flex-1",children:(0,s.jsx)("section",{className:"py-12 md:py-16 bg-muted/30",children:(0,s.jsxs)("div",{className:"container mx-auto px-4",children:[(0,s.jsxs)("div",{className:"max-w-3xl mx-auto text-center mb-12",children:[(0,s.jsx)("h1",{className:"text-3xl md:text-4xl font-bold mb-4",children:"Explore Quizzes"}),(0,s.jsx)("p",{className:"text-lg text-muted-foreground",children:"Discover and take quizzes created by the QuizFlow community"})]}),(0,s.jsxs)("div",{className:"flex flex-col md:flex-row gap-4 mb-8 justify-center",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{type:"text",placeholder:"Search quizzes...",className:"w-full md:w-80 p-2 pl-10 border rounded-md"}),(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})]}),(0,s.jsxs)("select",{className:"p-2 border rounded-md",children:[(0,s.jsx)("option",{value:"",children:"All Categories"}),(0,s.jsx)("option",{value:"security",children:"Security"}),(0,s.jsx)("option",{value:"programming",children:"Programming"}),(0,s.jsx)("option",{value:"networking",children:"Networking"}),(0,s.jsx)("option",{value:"other",children:"Other"})]})]}),0===r.length?(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)("h3",{className:"text-lg font-medium mb-2",children:"No quizzes available"}),(0,s.jsx)("p",{className:"text-muted-foreground mb-6",children:"There are no published quizzes available at the moment."}),e&&(0,s.jsx)(d.$,{asChild:!0,children:(0,s.jsx)(n(),{href:"/dashboard/quizzes/create",children:"Create a Quiz"})})]}):(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:r.map(e=>(0,s.jsxs)(c.Zp,{className:"h-full flex flex-col",children:[(0,s.jsx)(c.aR,{children:(0,s.jsx)("div",{className:"flex justify-between items-start",children:(0,s.jsxs)("div",{children:[(0,s.jsx)(c.ZB,{className:"line-clamp-1",children:e.title}),(0,s.jsxs)(c.BT,{children:["By ",e.creator?.name||"Anonymous"]})]})})}),(0,s.jsxs)(c.Wu,{className:"flex-1",children:[(0,s.jsx)("p",{className:"text-sm text-muted-foreground line-clamp-2 mb-4",children:e.description||"No description provided"}),(0,s.jsx)("div",{className:"flex flex-wrap gap-2 mb-4",children:e.tags.map((e,r)=>(0,s.jsx)("span",{className:"inline-block px-2 py-1 text-xs bg-muted rounded-full",children:e},r))}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-2 text-sm",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-muted-foreground",children:"Questions"}),(0,s.jsx)("p",{className:"font-medium",children:e._count?.questions||0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-muted-foreground",children:"Time Limit"}),(0,s.jsx)("p",{className:"font-medium",children:e.timeLimit?`${e.timeLimit} min`:"None"})]})]})]}),(0,s.jsx)(c.wL,{className:"border-t pt-4",children:(0,s.jsxs)("div",{className:"w-full flex justify-between items-center",children:[(0,s.jsxs)("div",{className:"text-xs text-muted-foreground",children:["Updated ",(0,u.Yq)(e.updatedAt)]}),(0,s.jsx)(d.$,{asChild:!0,children:(0,s.jsx)(n(),{href:`/quiz/${e.id}`,children:"Take Quiz"})})]})})]},e.id))})]})})}),(0,s.jsx)("footer",{className:"py-8 border-t",children:(0,s.jsx)("div",{className:"container mx-auto px-4 text-center",children:(0,s.jsxs)("p",{className:"text-muted-foreground",children:["\xa9 ",new Date().getFullYear()," QuizFlow. All rights reserved."]})})})]})}},71354:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>c,metadata:()=>d});var s=t(62740),i=t(2202),n=t.n(i),a=t(64988),l=t.n(a);t(61135);var o=t(28445);let d={title:"QuizFlow - Interactive Quiz Ecosystem",description:"A standardized, flexible, and interactive quiz ecosystem for creating and taking quizzes"};function c({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{className:`${n().variable} ${l().variable} antialiased min-h-screen bg-background text-foreground`,children:(0,s.jsx)(o.default,{children:e})})})}},28445:(e,r,t)=>{"use strict";t.d(r,{default:()=>s});let s=(0,t(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/components/auth/SessionProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/components/auth/SessionProvider.tsx","default")},13797:(e,r,t)=>{"use strict";t.d(r,{$:()=>x});var s=t(62740),i=t(76301);function n(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}var a=function(e){let r=function(e){let r=i.forwardRef((e,r)=>{let{children:t,...s}=e;if(i.isValidElement(t)){let e,a;let l=(e=Object.getOwnPropertyDescriptor(t.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?t.ref:(e=Object.getOwnPropertyDescriptor(t,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?t.props.ref:t.props.ref||t.ref,o=function(e,r){let t={...r};for(let s in r){let i=e[s],n=r[s];/^on[A-Z]/.test(s)?i&&n?t[s]=(...e)=>{let r=n(...e);return i(...e),r}:i&&(t[s]=i):"style"===s?t[s]={...i,...n}:"className"===s&&(t[s]=[i,n].filter(Boolean).join(" "))}return{...e,...t}}(s,t.props);return t.type!==i.Fragment&&(o.ref=r?function(...e){return r=>{let t=!1,s=e.map(e=>{let s=n(e,r);return t||"function"!=typeof s||(t=!0),s});if(t)return()=>{for(let r=0;r<s.length;r++){let t=s[r];"function"==typeof t?t():n(e[r],null)}}}}(r,l):l),i.cloneElement(t,o)}return i.Children.count(t)>1?i.Children.only(null):null});return r.displayName=`${e}.SlotClone`,r}(e),t=i.forwardRef((e,t)=>{let{children:n,...a}=e,l=i.Children.toArray(n),d=l.find(o);if(d){let e=d.props.children,n=l.map(r=>r!==d?r:i.Children.count(e)>1?i.Children.only(null):i.isValidElement(e)?e.props.children:null);return(0,s.jsx)(r,{...a,ref:t,children:i.isValidElement(e)?i.cloneElement(e,void 0,n):null})}return(0,s.jsx)(r,{...a,ref:t,children:n})});return t.displayName=`${e}.Slot`,t}("Slot"),l=Symbol("radix.slottable");function o(e){return i.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===l}var d=t(13673);let c=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,u=d.$;var m=t(73300);let p=((e,r)=>t=>{var s;if((null==r?void 0:r.variants)==null)return u(e,null==t?void 0:t.class,null==t?void 0:t.className);let{variants:i,defaultVariants:n}=r,a=Object.keys(i).map(e=>{let r=null==t?void 0:t[e],s=null==n?void 0:n[e];if(null===r)return null;let a=c(r)||c(s);return i[e][a]}),l=t&&Object.entries(t).reduce((e,r)=>{let[t,s]=r;return void 0===s||(e[t]=s),e},{});return u(e,a,null==r?void 0:null===(s=r.compoundVariants)||void 0===s?void 0:s.reduce((e,r)=>{let{class:t,className:s,...i}=r;return Object.entries(i).every(e=>{let[r,t]=e;return Array.isArray(t)?t.includes({...n,...l}[r]):({...n,...l})[r]===t})?[...e,t,s]:e},[]),null==t?void 0:t.class,null==t?void 0:t.className)})("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),x=i.forwardRef(({className:e,variant:r,size:t,asChild:i=!1,...n},l)=>{let o=i?a:"button";return(0,s.jsx)(o,{className:(0,m.cn)(p({variant:r,size:t,className:e})),ref:l,...n})});x.displayName="Button"},99818:(e,r,t)=>{"use strict";t.d(r,{BT:()=>d,Wu:()=>c,ZB:()=>o,Zp:()=>a,aR:()=>l,wL:()=>u});var s=t(62740),i=t(76301),n=t(73300);let a=i.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...r}));a.displayName="Card";let l=i.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",e),...r}));l.displayName="CardHeader";let o=i.forwardRef(({className:e,...r},t)=>(0,s.jsx)("h3",{ref:t,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",e),...r}));o.displayName="CardTitle";let d=i.forwardRef(({className:e,...r},t)=>(0,s.jsx)("p",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",e),...r}));d.displayName="CardDescription";let c=i.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,n.cn)("p-6 pt-0",e),...r}));c.displayName="CardContent";let u=i.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",e),...r}));u.displayName="CardFooter"},37702:(e,r,t)=>{"use strict";t.d(r,{N:()=>c});var s=t(46814),i=t(91642),n=t(28053),a=t(7553),l=t(5486),o=t.n(l),d=t(27914);let c={adapter:(0,s.y)(d.db),session:{strategy:"jwt"},pages:{signIn:"/auth/login",signOut:"/auth/logout",error:"/auth/error"},providers:[(0,n.A)({clientId:process.env.GOOGLE_CLIENT_ID||"",clientSecret:process.env.GOOGLE_CLIENT_SECRET||""}),(0,a.A)({clientId:process.env.GITHUB_CLIENT_ID||"",clientSecret:process.env.GITHUB_CLIENT_SECRET||""}),(0,i.A)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)return null;let r=await d.db.user.findUnique({where:{email:e.email}});return r&&r.password&&await o().compare(e.password,r.password)?{id:r.id,name:r.name,email:r.email,image:r.image,role:r.role}:null}})],callbacks:{session:async({session:e,token:r})=>(r&&(e.user.id=r.id,e.user.name=r.name,e.user.email=r.email,e.user.image=r.picture,e.user.role=r.role),e),async jwt({token:e,user:r}){let t=await d.db.user.findFirst({where:{email:e.email}});return t?{id:t.id,name:t.name,email:t.email,picture:t.image,role:t.role}:(r&&(e.id=r.id),e)}},secret:process.env.NEXTAUTH_SECRET}},27914:(e,r,t)=>{"use strict";t.d(r,{db:()=>i});var s=t(96330);let i=globalThis.prisma||new s.PrismaClient},73300:(e,r,t)=>{"use strict";t.d(r,{Yq:()=>a,cn:()=>n});var s=t(13673),i=t(47317);function n(...e){return(0,i.QP)((0,s.$)(e))}function a(e){return("string"==typeof e?new Date(e):e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})}},70440:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>i});var s=t(88077);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},61135:()=>{}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[638,263,3,367,85],()=>t(93158));module.exports=s})();