(()=>{var e={};e.id=974,e.ids=[974],e.modules={96330:e=>{"use strict";e.exports=require("@prisma/client")},5486:e=>{"use strict";e.exports=require("bcrypt")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},12412:e=>{"use strict";e.exports=require("assert")},79428:e=>{"use strict";e.exports=require("buffer")},55511:e=>{"use strict";e.exports=require("crypto")},94735:e=>{"use strict";e.exports=require("events")},81630:e=>{"use strict";e.exports=require("http")},55591:e=>{"use strict";e.exports=require("https")},33873:e=>{"use strict";e.exports=require("path")},11723:e=>{"use strict";e.exports=require("querystring")},79551:e=>{"use strict";e.exports=require("url")},28354:e=>{"use strict";e.exports=require("util")},74075:e=>{"use strict";e.exports=require("zlib")},75058:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var s=t(70260),n=t(28203),i=t(25155),a=t.n(i),o=t(67292),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(r,l);let d=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,61377)),"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/app/page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,71354)),"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["/Users/<USER>/Documents/augment-projects/hacking-quiz/src/app/page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},66202:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,59607,23))},482:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,28531,23))},29214:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,13219,23)),Promise.resolve().then(t.t.bind(t,34863,23)),Promise.resolve().then(t.t.bind(t,25155,23)),Promise.resolve().then(t.t.bind(t,40802,23)),Promise.resolve().then(t.t.bind(t,9350,23)),Promise.resolve().then(t.t.bind(t,48530,23)),Promise.resolve().then(t.t.bind(t,88921,23))},69382:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,66959,23)),Promise.resolve().then(t.t.bind(t,33875,23)),Promise.resolve().then(t.t.bind(t,88903,23)),Promise.resolve().then(t.t.bind(t,57174,23)),Promise.resolve().then(t.t.bind(t,84178,23)),Promise.resolve().then(t.t.bind(t,87190,23)),Promise.resolve().then(t.t.bind(t,61365,23))},41303:(e,r,t)=>{Promise.resolve().then(t.bind(t,28445))},69327:(e,r,t)=>{Promise.resolve().then(t.bind(t,82649))},82649:(e,r,t)=>{"use strict";t.d(r,{default:()=>i});var s=t(45512),n=t(90993);function i({children:e}){return(0,s.jsx)(n.SessionProvider,{children:e})}},59607:(e,r,t)=>{let{createProxy:s}=t(73439);e.exports=s("/Users/<USER>/Documents/augment-projects/hacking-quiz/node_modules/next/dist/client/app-dir/link.js")},71354:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>c,metadata:()=>d});var s=t(62740),n=t(2202),i=t.n(n),a=t(64988),o=t.n(a);t(61135);var l=t(28445);let d={title:"QuizFlow - Interactive Quiz Ecosystem",description:"A standardized, flexible, and interactive quiz ecosystem for creating and taking quizzes"};function c({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{className:`${i().variable} ${o().variable} antialiased min-h-screen bg-background text-foreground`,children:(0,s.jsx)(l.default,{children:e})})})}},61377:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>d});var s=t(62740),n=t(59607),i=t.n(n),a=t(13797),o=t(51825),l=t(37702);async function d(){let e=await (0,o.getServerSession)(l.N);return(0,s.jsxs)("div",{className:"flex flex-col min-h-screen",children:[(0,s.jsx)("header",{className:"border-b",children:(0,s.jsxs)("div",{className:"container mx-auto px-4 flex h-16 items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-6",children:[(0,s.jsx)(i(),{href:"/",className:"text-xl font-bold",children:"QuizFlow"}),(0,s.jsxs)("nav",{className:"hidden md:flex gap-6",children:[(0,s.jsx)(i(),{href:"/features",className:"text-sm font-medium text-muted-foreground transition-colors hover:text-primary",children:"Features"}),(0,s.jsx)(i(),{href:"/explore",className:"text-sm font-medium text-muted-foreground transition-colors hover:text-primary",children:"Explore"}),(0,s.jsx)(i(),{href:"/docs",className:"text-sm font-medium text-muted-foreground transition-colors hover:text-primary",children:"Documentation"})]})]}),(0,s.jsx)("div",{className:"flex items-center gap-4",children:e?(0,s.jsx)(a.$,{asChild:!0,children:(0,s.jsx)(i(),{href:"/dashboard",children:"Dashboard"})}):(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsx)(a.$,{variant:"outline",asChild:!0,children:(0,s.jsx)(i(),{href:"/auth/login",children:"Sign In"})}),(0,s.jsx)(a.$,{asChild:!0,children:(0,s.jsx)(i(),{href:"/auth/register",children:"Sign Up"})})]})})]})}),(0,s.jsx)("section",{className:"py-20 md:py-28",children:(0,s.jsxs)("div",{className:"container mx-auto px-4 text-center",children:[(0,s.jsx)("h1",{className:"text-4xl md:text-6xl font-bold mb-6",children:"Create, Share, and Take Interactive Quizzes"}),(0,s.jsx)("p",{className:"text-xl md:text-2xl text-muted-foreground mb-10 max-w-3xl mx-auto",children:"QuizFlow is a standardized, flexible, and interactive quiz ecosystem for creating and sharing knowledge assessments."}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,s.jsx)(a.$,{size:"lg",asChild:!0,children:(0,s.jsx)(i(),{href:e?"/dashboard/quizzes/create":"/auth/register",children:e?"Create a Quiz":"Get Started"})}),(0,s.jsx)(a.$,{size:"lg",variant:"outline",asChild:!0,children:(0,s.jsx)(i(),{href:"/explore",children:"Explore Quizzes"})})]})]})}),(0,s.jsx)("section",{className:"py-16 bg-muted/50",children:(0,s.jsxs)("div",{className:"container mx-auto px-4",children:[(0,s.jsx)("h2",{className:"text-3xl font-bold text-center mb-12",children:"Key Features"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:[(0,s.jsxs)("div",{className:"bg-background p-6 rounded-lg shadow-sm",children:[(0,s.jsx)("h3",{className:"text-xl font-bold mb-3",children:"Standardized Format"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Create quizzes using our well-documented, extensible, and universally understandable JSON format."})]}),(0,s.jsxs)("div",{className:"bg-background p-6 rounded-lg shadow-sm",children:[(0,s.jsx)("h3",{className:"text-xl font-bold mb-3",children:"Rich Interactivity"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Support for multiple question types, media integration, and dynamic content for engaging learning experiences."})]}),(0,s.jsxs)("div",{className:"bg-background p-6 rounded-lg shadow-sm",children:[(0,s.jsx)("h3",{className:"text-xl font-bold mb-3",children:"Open Ecosystem"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"An open-source foundation that encourages community involvement and integration with other systems."})]})]})]})}),(0,s.jsx)("section",{className:"py-20 bg-primary text-primary-foreground",children:(0,s.jsxs)("div",{className:"container mx-auto px-4 text-center",children:[(0,s.jsx)("h2",{className:"text-3xl font-bold mb-6",children:"Ready to create your first quiz?"}),(0,s.jsx)("p",{className:"text-xl mb-8 max-w-2xl mx-auto opacity-90",children:"Join QuizFlow today and start creating interactive quizzes for education, training, or fun."}),(0,s.jsx)(a.$,{size:"lg",variant:"secondary",asChild:!0,children:(0,s.jsx)(i(),{href:e?"/dashboard":"/auth/register",children:e?"Go to Dashboard":"Sign Up for Free"})})]})}),(0,s.jsx)("footer",{className:"py-12 border-t",children:(0,s.jsxs)("div",{className:"container mx-auto px-4",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-bold mb-4",children:"QuizFlow"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"A standardized, flexible, and interactive quiz ecosystem."})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium mb-4",children:"Product"}),(0,s.jsxs)("ul",{className:"space-y-2",children:[(0,s.jsx)("li",{children:(0,s.jsx)(i(),{href:"/features",className:"text-muted-foreground hover:text-foreground",children:"Features"})}),(0,s.jsx)("li",{children:(0,s.jsx)(i(),{href:"/explore",className:"text-muted-foreground hover:text-foreground",children:"Explore"})}),(0,s.jsx)("li",{children:(0,s.jsx)(i(),{href:"/pricing",className:"text-muted-foreground hover:text-foreground",children:"Pricing"})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium mb-4",children:"Resources"}),(0,s.jsxs)("ul",{className:"space-y-2",children:[(0,s.jsx)("li",{children:(0,s.jsx)(i(),{href:"/docs",className:"text-muted-foreground hover:text-foreground",children:"Documentation"})}),(0,s.jsx)("li",{children:(0,s.jsx)(i(),{href:"/guides",className:"text-muted-foreground hover:text-foreground",children:"Guides"})}),(0,s.jsx)("li",{children:(0,s.jsx)(i(),{href:"/api",className:"text-muted-foreground hover:text-foreground",children:"API"})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium mb-4",children:"Company"}),(0,s.jsxs)("ul",{className:"space-y-2",children:[(0,s.jsx)("li",{children:(0,s.jsx)(i(),{href:"/about",className:"text-muted-foreground hover:text-foreground",children:"About"})}),(0,s.jsx)("li",{children:(0,s.jsx)(i(),{href:"/blog",className:"text-muted-foreground hover:text-foreground",children:"Blog"})}),(0,s.jsx)("li",{children:(0,s.jsx)(i(),{href:"/contact",className:"text-muted-foreground hover:text-foreground",children:"Contact"})})]})]})]}),(0,s.jsx)("div",{className:"mt-12 pt-8 border-t text-center text-muted-foreground",children:(0,s.jsxs)("p",{children:["\xa9 ",new Date().getFullYear()," QuizFlow. All rights reserved."]})})]})})]})}},28445:(e,r,t)=>{"use strict";t.d(r,{default:()=>s});let s=(0,t(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/components/auth/SessionProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/components/auth/SessionProvider.tsx","default")},13797:(e,r,t)=>{"use strict";t.d(r,{$:()=>h});var s=t(62740),n=t(76301);function i(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}var a=function(e){let r=function(e){let r=n.forwardRef((e,r)=>{let{children:t,...s}=e;if(n.isValidElement(t)){let e,a;let o=(e=Object.getOwnPropertyDescriptor(t.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?t.ref:(e=Object.getOwnPropertyDescriptor(t,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?t.props.ref:t.props.ref||t.ref,l=function(e,r){let t={...r};for(let s in r){let n=e[s],i=r[s];/^on[A-Z]/.test(s)?n&&i?t[s]=(...e)=>{let r=i(...e);return n(...e),r}:n&&(t[s]=n):"style"===s?t[s]={...n,...i}:"className"===s&&(t[s]=[n,i].filter(Boolean).join(" "))}return{...e,...t}}(s,t.props);return t.type!==n.Fragment&&(l.ref=r?function(...e){return r=>{let t=!1,s=e.map(e=>{let s=i(e,r);return t||"function"!=typeof s||(t=!0),s});if(t)return()=>{for(let r=0;r<s.length;r++){let t=s[r];"function"==typeof t?t():i(e[r],null)}}}}(r,o):o),n.cloneElement(t,l)}return n.Children.count(t)>1?n.Children.only(null):null});return r.displayName=`${e}.SlotClone`,r}(e),t=n.forwardRef((e,t)=>{let{children:i,...a}=e,o=n.Children.toArray(i),d=o.find(l);if(d){let e=d.props.children,i=o.map(r=>r!==d?r:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,s.jsx)(r,{...a,ref:t,children:n.isValidElement(e)?n.cloneElement(e,void 0,i):null})}return(0,s.jsx)(r,{...a,ref:t,children:i})});return t.displayName=`${e}.Slot`,t}("Slot"),o=Symbol("radix.slottable");function l(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===o}var d=t(13673);let c=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,u=d.$;var m=t(73300);let x=((e,r)=>t=>{var s;if((null==r?void 0:r.variants)==null)return u(e,null==t?void 0:t.class,null==t?void 0:t.className);let{variants:n,defaultVariants:i}=r,a=Object.keys(n).map(e=>{let r=null==t?void 0:t[e],s=null==i?void 0:i[e];if(null===r)return null;let a=c(r)||c(s);return n[e][a]}),o=t&&Object.entries(t).reduce((e,r)=>{let[t,s]=r;return void 0===s||(e[t]=s),e},{});return u(e,a,null==r?void 0:null===(s=r.compoundVariants)||void 0===s?void 0:s.reduce((e,r)=>{let{class:t,className:s,...n}=r;return Object.entries(n).every(e=>{let[r,t]=e;return Array.isArray(t)?t.includes({...i,...o}[r]):({...i,...o})[r]===t})?[...e,t,s]:e},[]),null==t?void 0:t.class,null==t?void 0:t.className)})("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),h=n.forwardRef(({className:e,variant:r,size:t,asChild:n=!1,...i},o)=>{let l=n?a:"button";return(0,s.jsx)(l,{className:(0,m.cn)(x({variant:r,size:t,className:e})),ref:o,...i})});h.displayName="Button"},37702:(e,r,t)=>{"use strict";t.d(r,{N:()=>c});var s=t(46814),n=t(91642),i=t(28053),a=t(7553),o=t(5486),l=t.n(o),d=t(27914);let c={adapter:(0,s.y)(d.db),session:{strategy:"jwt"},pages:{signIn:"/auth/login",signOut:"/auth/logout",error:"/auth/error"},providers:[(0,i.A)({clientId:process.env.GOOGLE_CLIENT_ID||"",clientSecret:process.env.GOOGLE_CLIENT_SECRET||""}),(0,a.A)({clientId:process.env.GITHUB_CLIENT_ID||"",clientSecret:process.env.GITHUB_CLIENT_SECRET||""}),(0,n.A)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)return null;let r=await d.db.user.findUnique({where:{email:e.email}});return r&&r.password&&await l().compare(e.password,r.password)?{id:r.id,name:r.name,email:r.email,image:r.image,role:r.role}:null}})],callbacks:{session:async({session:e,token:r})=>(r&&(e.user.id=r.id,e.user.name=r.name,e.user.email=r.email,e.user.image=r.picture,e.user.role=r.role),e),async jwt({token:e,user:r}){let t=await d.db.user.findFirst({where:{email:e.email}});return t?{id:t.id,name:t.name,email:t.email,picture:t.image,role:t.role}:(r&&(e.id=r.id),e)}},secret:process.env.NEXTAUTH_SECRET}},27914:(e,r,t)=>{"use strict";t.d(r,{db:()=>n});var s=t(96330);let n=globalThis.prisma||new s.PrismaClient},73300:(e,r,t)=>{"use strict";t.d(r,{Yq:()=>a,cn:()=>i});var s=t(13673),n=t(47317);function i(...e){return(0,n.QP)((0,s.$)(e))}function a(e){return("string"==typeof e?new Date(e):e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})}},70440:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>n});var s=t(88077);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},61135:()=>{}};var r=require("../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[638,263,3,367,85],()=>t(75058));module.exports=s})();