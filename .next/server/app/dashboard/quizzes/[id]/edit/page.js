(()=>{var e={};e.id=500,e.ids=[500],e.modules={96330:e=>{"use strict";e.exports=require("@prisma/client")},5486:e=>{"use strict";e.exports=require("bcrypt")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},12412:e=>{"use strict";e.exports=require("assert")},79428:e=>{"use strict";e.exports=require("buffer")},55511:e=>{"use strict";e.exports=require("crypto")},94735:e=>{"use strict";e.exports=require("events")},81630:e=>{"use strict";e.exports=require("http")},55591:e=>{"use strict";e.exports=require("https")},33873:e=>{"use strict";e.exports=require("path")},11723:e=>{"use strict";e.exports=require("querystring")},79551:e=>{"use strict";e.exports=require("url")},28354:e=>{"use strict";e.exports=require("util")},74075:e=>{"use strict";e.exports=require("zlib")},20270:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var r=t(70260),i=t(28203),n=t(25155),l=t.n(n),a=t(67292),o={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>a[e]);t.d(s,o);let d=["",{children:["dashboard",{children:["quizzes",{children:["[id]",{children:["edit",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,54363)),"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/app/dashboard/quizzes/[id]/edit/page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,33405)),"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/app/dashboard/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,71354)),"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["/Users/<USER>/Documents/augment-projects/hacking-quiz/src/app/dashboard/quizzes/[id]/edit/page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/dashboard/quizzes/[id]/edit/page",pathname:"/dashboard/quizzes/[id]/edit",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},92295:(e,s,t)=>{Promise.resolve().then(t.bind(t,26996))},31615:(e,s,t)=>{Promise.resolve().then(t.bind(t,44848))},6062:(e,s,t)=>{Promise.resolve().then(t.bind(t,76297))},69110:(e,s,t)=>{Promise.resolve().then(t.bind(t,83822))},44848:(e,s,t)=>{"use strict";t.d(s,{default:()=>d});var r=t(45512),i=t(28531),n=t.n(i),l=t(79334),a=t(90993),o=t(39400);function d(){let e=(0,l.usePathname)(),{data:s}=(0,a.useSession)(),t=s=>e===s||e.startsWith(`${s}/`);return(0,r.jsx)("header",{className:"border-b",children:(0,r.jsxs)("div",{className:"container mx-auto px-4 flex h-16 items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-6",children:[(0,r.jsx)(n(),{href:"/",className:"text-xl font-bold",children:"QuizFlow"}),(0,r.jsxs)("nav",{className:"hidden md:flex gap-6",children:[(0,r.jsx)(n(),{href:"/dashboard",className:`text-sm font-medium transition-colors hover:text-primary ${!t("/dashboard")||t("/dashboard/quizzes")||t("/dashboard/activity")?"text-muted-foreground":"text-primary"}`,children:"Dashboard"}),(0,r.jsx)(n(),{href:"/dashboard/quizzes",className:`text-sm font-medium transition-colors hover:text-primary ${t("/dashboard/quizzes")?"text-primary":"text-muted-foreground"}`,children:"My Quizzes"}),(0,r.jsx)(n(),{href:"/dashboard/activity",className:`text-sm font-medium transition-colors hover:text-primary ${t("/dashboard/activity")?"text-primary":"text-muted-foreground"}`,children:"Activity"}),(0,r.jsx)(n(),{href:"/explore",className:`text-sm font-medium transition-colors hover:text-primary ${t("/explore")?"text-primary":"text-muted-foreground"}`,children:"Explore"})]})]}),(0,r.jsx)("div",{className:"flex items-center gap-4",children:s?(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsxs)("div",{className:"hidden md:block",children:[(0,r.jsx)("div",{className:"text-sm font-medium",children:s.user.name}),(0,r.jsx)("div",{className:"text-xs text-muted-foreground",children:s.user.email})]}),(0,r.jsx)(o.$,{variant:"outline",size:"sm",onClick:()=>(0,a.signOut)({callbackUrl:"/"}),children:"Sign Out"})]}):(0,r.jsx)(o.$,{asChild:!0,size:"sm",children:(0,r.jsx)(n(),{href:"/auth/login",children:"Sign In"})})})]})})}},83822:(e,s,t)=>{"use strict";t.d(s,{default:()=>ev});var r=t(45512),i=t(58009),n=t.t(i,2),l=t(79334);function a(e,s,{checkForDefaultPrevented:t=!0}={}){return function(r){if(e?.(r),!1===t||!r.defaultPrevented)return s?.(r)}}function o(e,s=[]){let t=[],n=()=>{let s=t.map(e=>i.createContext(e));return function(t){let r=t?.[e]||s;return i.useMemo(()=>({[`__scope${e}`]:{...t,[e]:r}}),[t,r])}};return n.scopeName=e,[function(s,n){let l=i.createContext(n),a=t.length;t=[...t,n];let o=s=>{let{scope:t,children:n,...o}=s,d=t?.[e]?.[a]||l,c=i.useMemo(()=>o,Object.values(o));return(0,r.jsx)(d.Provider,{value:c,children:n})};return o.displayName=s+"Provider",[o,function(t,r){let o=r?.[e]?.[a]||l,d=i.useContext(o);if(d)return d;if(void 0!==n)return n;throw Error(`\`${t}\` must be used within \`${s}\``)}]},function(...e){let s=e[0];if(1===e.length)return s;let t=()=>{let t=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let r=t.reduce((s,{useScope:t,scopeName:r})=>{let i=t(e)[`__scope${r}`];return{...s,...i}},{});return i.useMemo(()=>({[`__scope${s.scopeName}`]:r}),[r])}};return t.scopeName=s.scopeName,t}(n,...s)]}var d=t(29952),c=t(12705),u=globalThis?.document?i.useLayoutEffect:()=>{},m=n[" useId ".trim().toString()]||(()=>void 0),h=0;function x(e){let[s,t]=i.useState(m());return u(()=>{e||t(e=>e??String(h++))},[e]),e||(s?`radix-${s}`:"")}t(55740);var p=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,s)=>{let t=(0,c.TL)(`Primitive.${s}`),n=i.forwardRef((e,i)=>{let{asChild:n,...l}=e,a=n?t:s;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,r.jsx)(a,{...l,ref:i})});return n.displayName=`Primitive.${s}`,{...e,[s]:n}},{}),f=n[" useInsertionEffect ".trim().toString()]||u;function j({prop:e,defaultProp:s,onChange:t=()=>{},caller:r}){let[n,l,a]=function({defaultProp:e,onChange:s}){let[t,r]=i.useState(e),n=i.useRef(t),l=i.useRef(s);return f(()=>{l.current=s},[s]),i.useEffect(()=>{n.current!==t&&(l.current?.(t),n.current=t)},[t,n]),[t,r,l]}({defaultProp:s,onChange:t}),o=void 0!==e,d=o?e:n;{let s=i.useRef(void 0!==e);i.useEffect(()=>{let e=s.current;if(e!==o){let s=o?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${s}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}s.current=o},[o,r])}return[d,i.useCallback(s=>{if(o){let t="function"==typeof s?s(e):s;t!==e&&a.current?.(t)}else l(s)},[o,e,l,a])]}Symbol("RADIX:SYNC_STATE");var v=i.createContext(void 0);function g(e){let s=i.useContext(v);return e||s||"ltr"}var b="rovingFocusGroup.onEntryFocus",N={bubbles:!1,cancelable:!0},y="RovingFocusGroup",[w,C,k]=function(e){let s=e+"CollectionProvider",[t,n]=o(s),[l,a]=t(s,{collectionRef:{current:null},itemMap:new Map}),u=e=>{let{scope:s,children:t}=e,n=i.useRef(null),a=i.useRef(new Map).current;return(0,r.jsx)(l,{scope:s,itemMap:a,collectionRef:n,children:t})};u.displayName=s;let m=e+"CollectionSlot",h=(0,c.TL)(m),x=i.forwardRef((e,s)=>{let{scope:t,children:i}=e,n=a(m,t),l=(0,d.s)(s,n.collectionRef);return(0,r.jsx)(h,{ref:l,children:i})});x.displayName=m;let p=e+"CollectionItemSlot",f="data-radix-collection-item",j=(0,c.TL)(p),v=i.forwardRef((e,s)=>{let{scope:t,children:n,...l}=e,o=i.useRef(null),c=(0,d.s)(s,o),u=a(p,t);return i.useEffect(()=>(u.itemMap.set(o,{ref:o,...l}),()=>void u.itemMap.delete(o))),(0,r.jsx)(j,{[f]:"",ref:c,children:n})});return v.displayName=p,[{Provider:u,Slot:x,ItemSlot:v},function(s){let t=a(e+"CollectionConsumer",s);return i.useCallback(()=>{let e=t.collectionRef.current;if(!e)return[];let s=Array.from(e.querySelectorAll(`[${f}]`));return Array.from(t.itemMap.values()).sort((e,t)=>s.indexOf(e.ref.current)-s.indexOf(t.ref.current))},[t.collectionRef,t.itemMap])},n]}(y),[S,q]=o(y,[k]),[z,P]=S(y),E=i.forwardRef((e,s)=>(0,r.jsx)(w.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,r.jsx)(w.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,r.jsx)(_,{...e,ref:s})})}));E.displayName=y;var _=i.forwardRef((e,s)=>{let{__scopeRovingFocusGroup:t,orientation:n,loop:l=!1,dir:o,currentTabStopId:c,defaultCurrentTabStopId:u,onCurrentTabStopIdChange:m,onEntryFocus:h,preventScrollOnEntryFocus:x=!1,...f}=e,v=i.useRef(null),w=(0,d.s)(s,v),k=g(o),[S,q]=j({prop:c,defaultProp:u??null,onChange:m,caller:y}),[P,E]=i.useState(!1),_=function(e){let s=i.useRef(e);return i.useEffect(()=>{s.current=e}),i.useMemo(()=>(...e)=>s.current?.(...e),[])}(h),R=C(t),T=i.useRef(!1),[$,F]=i.useState(0);return i.useEffect(()=>{let e=v.current;if(e)return e.addEventListener(b,_),()=>e.removeEventListener(b,_)},[_]),(0,r.jsx)(z,{scope:t,orientation:n,dir:k,loop:l,currentTabStopId:S,onItemFocus:i.useCallback(e=>q(e),[q]),onItemShiftTab:i.useCallback(()=>E(!0),[]),onFocusableItemAdd:i.useCallback(()=>F(e=>e+1),[]),onFocusableItemRemove:i.useCallback(()=>F(e=>e-1),[]),children:(0,r.jsx)(p.div,{tabIndex:P||0===$?-1:0,"data-orientation":n,...f,ref:w,style:{outline:"none",...e.style},onMouseDown:a(e.onMouseDown,()=>{T.current=!0}),onFocus:a(e.onFocus,e=>{let s=!T.current;if(e.target===e.currentTarget&&s&&!P){let s=new CustomEvent(b,N);if(e.currentTarget.dispatchEvent(s),!s.defaultPrevented){let e=R().filter(e=>e.focusable);A([e.find(e=>e.active),e.find(e=>e.id===S),...e].filter(Boolean).map(e=>e.ref.current),x)}}T.current=!1}),onBlur:a(e.onBlur,()=>E(!1))})})}),R="RovingFocusGroupItem",T=i.forwardRef((e,s)=>{let{__scopeRovingFocusGroup:t,focusable:n=!0,active:l=!1,tabStopId:o,children:d,...c}=e,u=x(),m=o||u,h=P(R,t),f=h.currentTabStopId===m,j=C(t),{onFocusableItemAdd:v,onFocusableItemRemove:g,currentTabStopId:b}=h;return i.useEffect(()=>{if(n)return v(),()=>g()},[n,v,g]),(0,r.jsx)(w.ItemSlot,{scope:t,id:m,focusable:n,active:l,children:(0,r.jsx)(p.span,{tabIndex:f?0:-1,"data-orientation":h.orientation,...c,ref:s,onMouseDown:a(e.onMouseDown,e=>{n?h.onItemFocus(m):e.preventDefault()}),onFocus:a(e.onFocus,()=>h.onItemFocus(m)),onKeyDown:a(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){h.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let s=function(e,s,t){var r;let i=(r=e.key,"rtl"!==t?r:"ArrowLeft"===r?"ArrowRight":"ArrowRight"===r?"ArrowLeft":r);if(!("vertical"===s&&["ArrowLeft","ArrowRight"].includes(i))&&!("horizontal"===s&&["ArrowUp","ArrowDown"].includes(i)))return $[i]}(e,h.orientation,h.dir);if(void 0!==s){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let t=j().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===s)t.reverse();else if("prev"===s||"next"===s){"prev"===s&&t.reverse();let r=t.indexOf(e.currentTarget);t=h.loop?function(e,s){return e.map((t,r)=>e[(s+r)%e.length])}(t,r+1):t.slice(r+1)}setTimeout(()=>A(t))}}),children:"function"==typeof d?d({isCurrentTabStop:f,hasTabStop:null!=b}):d})})});T.displayName=R;var $={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function A(e,s=!1){let t=document.activeElement;for(let r of e)if(r===t||(r.focus({preventScroll:s}),document.activeElement!==t))return}var F=e=>{let{present:s,children:t}=e,r=function(e){var s,t;let[r,n]=i.useState(),l=i.useRef(null),a=i.useRef(e),o=i.useRef("none"),[d,c]=(s=e?"mounted":"unmounted",t={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},i.useReducer((e,s)=>t[e][s]??e,s));return i.useEffect(()=>{let e=I(l.current);o.current="mounted"===d?e:"none"},[d]),u(()=>{let s=l.current,t=a.current;if(t!==e){let r=o.current,i=I(s);e?c("MOUNT"):"none"===i||s?.display==="none"?c("UNMOUNT"):t&&r!==i?c("ANIMATION_OUT"):c("UNMOUNT"),a.current=e}},[e,c]),u(()=>{if(r){let e;let s=r.ownerDocument.defaultView??window,t=t=>{let i=I(l.current).includes(t.animationName);if(t.target===r&&i&&(c("ANIMATION_END"),!a.current)){let t=r.style.animationFillMode;r.style.animationFillMode="forwards",e=s.setTimeout(()=>{"forwards"===r.style.animationFillMode&&(r.style.animationFillMode=t)})}},i=e=>{e.target===r&&(o.current=I(l.current))};return r.addEventListener("animationstart",i),r.addEventListener("animationcancel",t),r.addEventListener("animationend",t),()=>{s.clearTimeout(e),r.removeEventListener("animationstart",i),r.removeEventListener("animationcancel",t),r.removeEventListener("animationend",t)}}c("ANIMATION_END")},[r,c]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:i.useCallback(e=>{l.current=e?getComputedStyle(e):null,n(e)},[])}}(s),n="function"==typeof t?t({present:r.isPresent}):i.Children.only(t),l=(0,d.s)(r.ref,function(e){let s=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,t=s&&"isReactWarning"in s&&s.isReactWarning;return t?e.ref:(t=(s=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in s&&s.isReactWarning)?e.props.ref:e.props.ref||e.ref}(n));return"function"==typeof t||r.isPresent?i.cloneElement(n,{ref:l}):null};function I(e){return e?.animationName||"none"}F.displayName="Presence";var L="Tabs",[M,O]=o(L,[q]),D=q(),[Q,B]=M(L),U=i.forwardRef((e,s)=>{let{__scopeTabs:t,value:i,onValueChange:n,defaultValue:l,orientation:a="horizontal",dir:o,activationMode:d="automatic",...c}=e,u=g(o),[m,h]=j({prop:i,onChange:n,defaultProp:l??"",caller:L});return(0,r.jsx)(Q,{scope:t,baseId:x(),value:m,onValueChange:h,orientation:a,dir:u,activationMode:d,children:(0,r.jsx)(p.div,{dir:u,"data-orientation":a,...c,ref:s})})});U.displayName=L;var W="TabsList",V=i.forwardRef((e,s)=>{let{__scopeTabs:t,loop:i=!0,...n}=e,l=B(W,t),a=D(t);return(0,r.jsx)(E,{asChild:!0,...a,orientation:l.orientation,dir:l.dir,loop:i,children:(0,r.jsx)(p.div,{role:"tablist","aria-orientation":l.orientation,...n,ref:s})})});V.displayName=W;var K="TabsTrigger",H=i.forwardRef((e,s)=>{let{__scopeTabs:t,value:i,disabled:n=!1,...l}=e,o=B(K,t),d=D(t),c=Z(o.baseId,i),u=Y(o.baseId,i),m=i===o.value;return(0,r.jsx)(T,{asChild:!0,...d,focusable:!n,active:m,children:(0,r.jsx)(p.button,{type:"button",role:"tab","aria-selected":m,"aria-controls":u,"data-state":m?"active":"inactive","data-disabled":n?"":void 0,disabled:n,id:c,...l,ref:s,onMouseDown:a(e.onMouseDown,e=>{n||0!==e.button||!1!==e.ctrlKey?e.preventDefault():o.onValueChange(i)}),onKeyDown:a(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&o.onValueChange(i)}),onFocus:a(e.onFocus,()=>{let e="manual"!==o.activationMode;m||n||!e||o.onValueChange(i)})})})});H.displayName=K;var G="TabsContent",J=i.forwardRef((e,s)=>{let{__scopeTabs:t,value:n,forceMount:l,children:a,...o}=e,d=B(G,t),c=Z(d.baseId,n),u=Y(d.baseId,n),m=n===d.value,h=i.useRef(m);return i.useEffect(()=>{let e=requestAnimationFrame(()=>h.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,r.jsx)(F,{present:l||m,children:({present:t})=>(0,r.jsx)(p.div,{"data-state":m?"active":"inactive","data-orientation":d.orientation,role:"tabpanel","aria-labelledby":c,hidden:!t,id:u,tabIndex:0,...o,ref:s,style:{...e.style,animationDuration:h.current?"0s":void 0},children:t&&a})})});function Z(e,s){return`${e}-trigger-${s}`}function Y(e,s){return`${e}-content-${s}`}J.displayName=G;var X=t(96720);let ee=i.forwardRef(({className:e,...s},t)=>(0,r.jsx)(V,{ref:t,className:(0,X.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...s}));ee.displayName=V.displayName;let es=i.forwardRef(({className:e,...s},t)=>(0,r.jsx)(H,{ref:t,className:(0,X.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...s}));es.displayName=H.displayName;let et=i.forwardRef(({className:e,...s},t)=>(0,r.jsx)(J,{ref:t,className:(0,X.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...s}));et.displayName=J.displayName;var er=t(39400),ei=t(64590);function en({quiz:e,onSave:s,isSaving:t}){let[n,l]=(0,i.useState)(e.title),[a,o]=(0,i.useState)(e.description||""),[d,c]=(0,i.useState)(e.tags.join(", ")),[u,m]=(0,i.useState)(e.passingScore?.toString()||"70"),[h,x]=(0,i.useState)(e.timeLimit?.toString()||"15"),[p,f]=(0,i.useState)(e.locale),[j,v]=(0,i.useState)(e.markupFormat);return(0,r.jsxs)("form",{onSubmit:e=>{e.preventDefault(),s({title:n,description:a||null,tags:d.split(",").map(e=>e.trim()).filter(e=>e),passingScore:u?parseFloat(u):null,timeLimit:h?parseInt(h):null,locale:p,markupFormat:j})},className:"space-y-6",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{htmlFor:"title",className:"text-sm font-medium",children:"Quiz Title"}),(0,r.jsx)("input",{id:"title",type:"text",value:n,onChange:e=>l(e.target.value),className:"w-full p-2 border rounded-md",required:!0})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{htmlFor:"description",className:"text-sm font-medium",children:"Description"}),(0,r.jsx)("textarea",{id:"description",value:a,onChange:e=>o(e.target.value),className:"w-full p-2 border rounded-md min-h-[100px]",placeholder:"Enter quiz description"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{htmlFor:"tags",className:"text-sm font-medium",children:"Tags"}),(0,r.jsx)("input",{id:"tags",type:"text",value:d,onChange:e=>c(e.target.value),className:"w-full p-2 border rounded-md",placeholder:"Enter tags separated by commas (e.g., security, basics, networking)"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Separate tags with commas (e.g., security, basics, networking)"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{htmlFor:"passingScore",className:"text-sm font-medium",children:"Passing Score (%)"}),(0,r.jsx)("input",{id:"passingScore",type:"number",min:"0",max:"100",value:u,onChange:e=>m(e.target.value),className:"w-full p-2 border rounded-md"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{htmlFor:"timeLimit",className:"text-sm font-medium",children:"Time Limit (minutes)"}),(0,r.jsx)("input",{id:"timeLimit",type:"number",min:"1",value:h,onChange:e=>x(e.target.value),className:"w-full p-2 border rounded-md"})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{htmlFor:"locale",className:"text-sm font-medium",children:"Locale"}),(0,r.jsxs)("select",{id:"locale",value:p,onChange:e=>f(e.target.value),className:"w-full p-2 border rounded-md",children:[(0,r.jsx)("option",{value:"en-US",children:"English (US)"}),(0,r.jsx)("option",{value:"en-GB",children:"English (UK)"}),(0,r.jsx)("option",{value:"es-ES",children:"Spanish"}),(0,r.jsx)("option",{value:"fr-FR",children:"French"}),(0,r.jsx)("option",{value:"de-DE",children:"German"}),(0,r.jsx)("option",{value:"ja-JP",children:"Japanese"}),(0,r.jsx)("option",{value:"zh-CN",children:"Chinese (Simplified)"})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{htmlFor:"markupFormat",className:"text-sm font-medium",children:"Markup Format"}),(0,r.jsxs)("select",{id:"markupFormat",value:j,onChange:e=>v(e.target.value),className:"w-full p-2 border rounded-md",children:[(0,r.jsx)("option",{value:"markdown",children:"Markdown"}),(0,r.jsx)("option",{value:"html",children:"HTML"}),(0,r.jsx)("option",{value:"plain_text",children:"Plain Text"})]})]})]}),(0,r.jsx)("div",{className:"flex justify-end pt-4",children:(0,r.jsx)(er.$,{type:"submit",disabled:t,children:t?"Saving...":"Save Details"})})]})}function el({data:e,onChange:s}){let[t,n]=(0,i.useState)(e?.single_correct_answer===void 0||e.single_correct_answer),[l,a]=(0,i.useState)(e?.options||[{id:(0,X.lk)(),text:"",is_correct:!1},{id:(0,X.lk)(),text:"",is_correct:!1}]),[o,d]=(0,i.useState)(e?.scoring_method||"all_or_nothing"),c=(e,s)=>{a(l.map(t=>t.id===e?{...t,text:s}:t))},u=(e,s)=>{t&&s?a(l.map(s=>({...s,is_correct:s.id===e}))):a(l.map(t=>t.id===e?{...t,is_correct:s}:t))},m=(e,s)=>{a(l.map(t=>t.id===e?{...t,feedback:s}:t))},h=e=>{if(l.length<=2){alert("A multiple choice question must have at least 2 options.");return}a(l.filter(s=>s.id!==e))};return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{className:"text-sm font-medium",children:"Question Type"}),(0,r.jsxs)("div",{className:"flex space-x-4",children:[(0,r.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,r.jsx)("input",{type:"radio",checked:t,onChange:()=>n(!0)}),(0,r.jsx)("span",{children:"Single Choice (Radio Buttons)"})]}),(0,r.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,r.jsx)("input",{type:"radio",checked:!t,onChange:()=>n(!1)}),(0,r.jsx)("span",{children:"Multiple Choice (Checkboxes)"})]})]})]}),!t&&(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{className:"text-sm font-medium",children:"Scoring Method"}),(0,r.jsxs)("div",{className:"flex space-x-4",children:[(0,r.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,r.jsx)("input",{type:"radio",checked:"all_or_nothing"===o,onChange:()=>d("all_or_nothing")}),(0,r.jsx)("span",{children:"All or Nothing"})]}),(0,r.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,r.jsx)("input",{type:"radio",checked:"partial_credit"===o,onChange:()=>d("partial_credit")}),(0,r.jsx)("span",{children:"Partial Credit"})]})]}),(0,r.jsxs)("p",{className:"text-xs text-muted-foreground",children:["All or Nothing: Full points only if all correct options are selected and no incorrect options.",(0,r.jsx)("br",{}),"Partial Credit: Points awarded based on correct selections."]})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("label",{className:"text-sm font-medium",children:"Options"}),l.map((e,s)=>(0,r.jsxs)("div",{className:"border rounded-md p-4 space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("h4",{className:"font-medium",children:["Option ",s+1]}),(0,r.jsxs)(er.$,{type:"button",variant:"ghost",size:"sm",onClick:()=>h(e.id),className:"h-8 w-8 p-0",children:[(0,r.jsx)("span",{className:"sr-only",children:"Remove"}),(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{className:"text-sm font-medium",children:"Option Text"}),(0,r.jsx)("input",{type:"text",value:e.text,onChange:s=>c(e.id,s.target.value),className:"w-full p-2 border rounded-md",placeholder:"Enter option text",required:!0})]}),(0,r.jsx)("div",{className:"space-y-2",children:(0,r.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,r.jsx)("input",{type:t?"radio":"checkbox",checked:e.is_correct,onChange:s=>u(e.id,s.target.checked),name:"correctOption"}),(0,r.jsx)("span",{children:"Correct Answer"})]})}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{className:"text-sm font-medium",children:"Feedback for this option"}),(0,r.jsx)("input",{type:"text",value:e.feedback||"",onChange:s=>m(e.id,s.target.value),className:"w-full p-2 border rounded-md",placeholder:"Optional feedback when this option is selected"})]})]},e.id)),(0,r.jsx)(er.$,{type:"button",variant:"outline",onClick:()=>{a([...l,{id:(0,X.lk)(),text:"",is_correct:!1}])},className:"w-full",children:"Add Option"})]})]})}function ea({data:e,onChange:s}){let[t,n]=(0,i.useState)(e?.correct_answer===void 0||e.correct_answer);return(0,r.jsx)("div",{className:"space-y-4",children:(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{className:"text-sm font-medium",children:"Correct Answer"}),(0,r.jsxs)("div",{className:"flex space-x-4",children:[(0,r.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,r.jsx)("input",{type:"radio",checked:!0===t,onChange:()=>n(!0),name:"correctAnswer"}),(0,r.jsx)("span",{children:"True"})]}),(0,r.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,r.jsx)("input",{type:"radio",checked:!1===t,onChange:()=>n(!1),name:"correctAnswer"}),(0,r.jsx)("span",{children:"False"})]})]})]})})}function eo({data:e,onChange:s}){let[t,n]=(0,i.useState)(e?.correct_answers||[""]),[l,a]=(0,i.useState)(e?.case_sensitive!==void 0&&e.case_sensitive),[o,d]=(0,i.useState)(e?.trim_whitespace===void 0||e.trim_whitespace),[c,u]=(0,i.useState)(e?.exact_match===void 0||e.exact_match),m=(e,s)=>{let r=[...t];r[e]=s,n(r)},h=e=>{if(t.length<=1){alert("You must have at least one correct answer.");return}let s=[...t];s.splice(e,1),n(s)};return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("label",{className:"text-sm font-medium",children:"Correct Answers"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Add multiple acceptable answers. The question will be marked correct if the student's answer matches any of these."}),t.map((e,s)=>(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("input",{type:"text",value:e,onChange:e=>m(s,e.target.value),className:"flex-1 p-2 border rounded-md",placeholder:"Enter a correct answer",required:!0}),(0,r.jsxs)(er.$,{type:"button",variant:"ghost",size:"sm",onClick:()=>h(s),className:"h-8 w-8 p-0",disabled:t.length<=1,children:[(0,r.jsx)("span",{className:"sr-only",children:"Remove"}),(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})]})]},s)),(0,r.jsx)(er.$,{type:"button",variant:"outline",onClick:()=>{n([...t,""])},className:"w-full",children:"Add Another Correct Answer"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{className:"text-sm font-medium",children:"Answer Options"}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,r.jsx)("input",{type:"checkbox",checked:l,onChange:e=>a(e.target.checked)}),(0,r.jsx)("span",{children:"Case Sensitive"})]}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground ml-6",children:'If checked, "Answer" and "answer" will be treated as different answers.'})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,r.jsx)("input",{type:"checkbox",checked:o,onChange:e=>d(e.target.checked)}),(0,r.jsx)("span",{children:"Trim Whitespace"})]}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground ml-6",children:"If checked, leading and trailing spaces will be ignored."})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,r.jsx)("input",{type:"checkbox",checked:c,onChange:e=>u(e.target.checked)}),(0,r.jsx)("span",{children:"Exact Match Required"})]}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground ml-6",children:"If unchecked, partial matches or keyword detection may be used (implementation dependent)."})]})]})]})}function ed({data:e,onChange:s}){let[t,n]=(0,i.useState)(e?.stems||[{id:(0,X.lk)(),text:""},{id:(0,X.lk)(),text:""}]),[l,a]=(0,i.useState)(e?.options||[{id:(0,X.lk)(),text:""},{id:(0,X.lk)(),text:""}]),[o,d]=(0,i.useState)(e?.correct_pairs||[]),c=(e,s)=>{n(t.map(t=>t.id===e?{...t,text:s}:t))},u=(e,s)=>{a(l.map(t=>t.id===e?{...t,text:s}:t))},m=(e,s)=>{d(o.map(t=>t.stem_id===e?{...t,option_id:s}:t))},h=e=>{if(t.length<=2){alert("A matching question must have at least 2 stems.");return}n(t.filter(s=>s.id!==e)),d(o.filter(s=>s.stem_id!==e))},x=e=>{if(l.length<=2){alert("A matching question must have at least 2 options.");return}a(l.filter(s=>s.id!==e)),d(o.map(s=>s.option_id===e?{...s,option_id:""}:s))};return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("label",{className:"text-sm font-medium",children:"Stems (Left Side)"}),t.map((e,s)=>(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("input",{type:"text",value:e.text,onChange:s=>c(e.id,s.target.value),className:"flex-1 p-2 border rounded-md",placeholder:`Stem ${s+1}`,required:!0}),(0,r.jsxs)(er.$,{type:"button",variant:"ghost",size:"sm",onClick:()=>h(e.id),className:"h-8 w-8 p-0",disabled:t.length<=2,children:[(0,r.jsx)("span",{className:"sr-only",children:"Remove"}),(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})]})]},e.id)),(0,r.jsx)(er.$,{type:"button",variant:"outline",onClick:()=>{let e=(0,X.lk)();n([...t,{id:e,text:""}]),d([...o,{stem_id:e,option_id:""}])},className:"w-full",children:"Add Stem"})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("label",{className:"text-sm font-medium",children:"Options (Right Side)"}),l.map((e,s)=>(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("input",{type:"text",value:e.text,onChange:s=>u(e.id,s.target.value),className:"flex-1 p-2 border rounded-md",placeholder:`Option ${s+1}`,required:!0}),(0,r.jsxs)(er.$,{type:"button",variant:"ghost",size:"sm",onClick:()=>x(e.id),className:"h-8 w-8 p-0",disabled:l.length<=2,children:[(0,r.jsx)("span",{className:"sr-only",children:"Remove"}),(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})]})]},e.id)),(0,r.jsx)(er.$,{type:"button",variant:"outline",onClick:()=>{a([...l,{id:(0,X.lk)(),text:""}])},className:"w-full",children:"Add Option"})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("label",{className:"text-sm font-medium",children:"Correct Matches"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"For each stem, select the matching option."}),t.map((e,s)=>{let t=o.find(s=>s.stem_id===e.id);return(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"flex-1 p-2 border rounded-md bg-muted",children:e.text||`Stem ${s+1}`}),(0,r.jsx)("div",{className:"text-center px-2",children:"matches"}),(0,r.jsxs)("select",{value:t?.option_id||"",onChange:s=>m(e.id,s.target.value),className:"flex-1 p-2 border rounded-md",required:!0,children:[(0,r.jsx)("option",{value:"",children:"-- Select matching option --"}),l.map(e=>(0,r.jsx)("option",{value:e.id,children:e.text||`Option ${l.findIndex(s=>s.id===e.id)+1}`},e.id))]})]},e.id)})]})]})}function ec({data:e,onChange:s}){let[t,n]=(0,i.useState)(e?.text_template||"This is a [BLANK] question with [BLANK] to fill in."),[l,a]=(0,i.useState)(e?.blanks||[]),o=(e,s,t)=>{let r=[...l];if(r[e]){let i=[...r[e].correct_answers];i[s]=t,r[e]={...r[e],correct_answers:i},a(r)}},d=(e,s,t)=>{let r=[...l];r[e]&&(r[e]={...r[e],[s]:t},a(r))},c=e=>{let s=[...l];s[e]&&(s[e]={...s[e],correct_answers:[...s[e].correct_answers,""]},a(s))},u=(e,s)=>{let t=[...l];if(t[e]&&t[e].correct_answers.length>1){let r=[...t[e].correct_answers];r.splice(s,1),t[e]={...t[e],correct_answers:r},a(t)}};return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{htmlFor:"textTemplate",className:"text-sm font-medium",children:"Question Text with Blanks"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Use [BLANK] to indicate where students should fill in answers."}),(0,r.jsx)("textarea",{id:"textTemplate",value:t,onChange:e=>n(e.target.value),className:"w-full p-2 border rounded-md min-h-[100px]",placeholder:"Enter text with [BLANK] placeholders",required:!0})]}),l.length>0?(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)("h3",{className:"text-sm font-medium",children:"Define Blanks"}),l.map((e,s)=>(0,r.jsxs)("div",{className:"border rounded-md p-4 space-y-4",children:[(0,r.jsxs)("h4",{className:"font-medium",children:["Blank ",s+1]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("label",{className:"text-sm font-medium",children:"Correct Answers"}),e.correct_answers.map((t,i)=>(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("input",{type:"text",value:t,onChange:e=>o(s,i,e.target.value),className:"flex-1 p-2 border rounded-md",placeholder:"Enter a correct answer",required:!0}),(0,r.jsxs)(er.$,{type:"button",variant:"ghost",size:"sm",onClick:()=>u(s,i),className:"h-8 w-8 p-0",disabled:e.correct_answers.length<=1,children:[(0,r.jsx)("span",{className:"sr-only",children:"Remove"}),(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})]})]},i)),(0,r.jsx)(er.$,{type:"button",variant:"outline",onClick:()=>c(s),className:"w-full",children:"Add Another Correct Answer"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{className:"text-sm font-medium",children:"Answer Options"}),(0,r.jsx)("div",{className:"space-y-2",children:(0,r.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,r.jsx)("input",{type:"checkbox",checked:e.case_sensitive,onChange:e=>d(s,"case_sensitive",e.target.checked)}),(0,r.jsx)("span",{children:"Case Sensitive"})]})}),(0,r.jsx)("div",{className:"space-y-2",children:(0,r.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,r.jsx)("input",{type:"checkbox",checked:e.trim_whitespace,onChange:e=>d(s,"trim_whitespace",e.target.checked)}),(0,r.jsx)("span",{children:"Trim Whitespace"})]})})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{htmlFor:`hint-${s}`,className:"text-sm font-medium",children:"Hint (Optional)"}),(0,r.jsx)("input",{id:`hint-${s}`,type:"text",value:e.hint||"",onChange:e=>d(s,"hint",e.target.value),className:"w-full p-2 border rounded-md",placeholder:"Enter a hint for this blank"})]})]},e.id))]}):(0,r.jsx)("div",{className:"p-4 bg-muted rounded-md",children:(0,r.jsx)("p",{className:"text-center",children:"Add [BLANK] placeholders to your text to create blanks for students to fill in."})})]})}function eu({data:e,onChange:s}){let[t,n]=(0,i.useState)(e?.min_word_count?.toString()||""),[l,a]=(0,i.useState)(e?.max_word_count?.toString()||""),[o,d]=(0,i.useState)(e?.guidelines||"");return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{htmlFor:"minWordCount",className:"text-sm font-medium",children:"Minimum Word Count"}),(0,r.jsx)("input",{id:"minWordCount",type:"number",min:"0",value:t,onChange:e=>n(e.target.value),className:"w-full p-2 border rounded-md",placeholder:"Optional"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{htmlFor:"maxWordCount",className:"text-sm font-medium",children:"Maximum Word Count"}),(0,r.jsx)("input",{id:"maxWordCount",type:"number",min:"0",value:l,onChange:e=>a(e.target.value),className:"w-full p-2 border rounded-md",placeholder:"Optional"})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{htmlFor:"guidelines",className:"text-sm font-medium",children:"Guidelines for Students"}),(0,r.jsx)("textarea",{id:"guidelines",value:o,onChange:e=>d(e.target.value),className:"w-full p-2 border rounded-md min-h-[100px]",placeholder:"Enter guidelines or prompts for the essay (optional)"})]}),(0,r.jsx)("div",{className:"p-4 bg-muted rounded-md",children:(0,r.jsxs)("p",{className:"text-sm",children:[(0,r.jsx)("strong",{children:"Note:"})," Essay questions typically require manual grading. Students will see the guidelines and word count limits when answering the question."]})})]})}function em({questionType:e,initialData:s,onSubmit:t,isSaving:n,submitLabel:l}){let[a,o]=(0,i.useState)(s?.text?"string"==typeof s.text?s.text:JSON.parse(s.text).default||"":""),[d,c]=(0,i.useState)(s?.points?.toString()||"1"),[u,m]=(0,i.useState)(s?.feedbackCorrect||""),[h,x]=(0,i.useState)(s?.feedbackIncorrect||""),[p,f]=(0,i.useState)(s?{...s}:{});return(0,r.jsxs)("form",{onSubmit:e=>{e.preventDefault(),t({text:a,points:parseFloat(d),feedbackCorrect:u||null,feedbackIncorrect:h||null,...p})},className:"space-y-6",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{htmlFor:"questionText",className:"text-sm font-medium",children:"Question Text"}),(0,r.jsx)("textarea",{id:"questionText",value:a,onChange:e=>o(e.target.value),className:"w-full p-2 border rounded-md min-h-[100px]",placeholder:"Enter your question here...",required:!0})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{htmlFor:"points",className:"text-sm font-medium",children:"Points"}),(0,r.jsx)("input",{id:"points",type:"number",min:"0.5",step:"0.5",value:d,onChange:e=>c(e.target.value),className:"w-full p-2 border rounded-md",required:!0})]}),(()=>{switch(e){case"multiple_choice":return(0,r.jsx)(el,{data:p,onChange:f});case"true_false":return(0,r.jsx)(ea,{data:p,onChange:f});case"short_answer":return(0,r.jsx)(eo,{data:p,onChange:f});case"matching":return(0,r.jsx)(ed,{data:p,onChange:f});case"fill_in_the_blank":return(0,r.jsx)(ec,{data:p,onChange:f});case"essay":return(0,r.jsx)(eu,{data:p,onChange:f});default:return(0,r.jsxs)("p",{children:["Unsupported question type: ",e]})}})(),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{htmlFor:"feedbackCorrect",className:"text-sm font-medium",children:"Feedback for Correct Answer"}),(0,r.jsx)("textarea",{id:"feedbackCorrect",value:u,onChange:e=>m(e.target.value),className:"w-full p-2 border rounded-md",placeholder:"Feedback to show when the answer is correct"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{htmlFor:"feedbackIncorrect",className:"text-sm font-medium",children:"Feedback for Incorrect Answer"}),(0,r.jsx)("textarea",{id:"feedbackIncorrect",value:h,onChange:e=>x(e.target.value),className:"w-full p-2 border rounded-md",placeholder:"Feedback to show when the answer is incorrect"})]}),(0,r.jsx)("div",{className:"flex justify-end pt-4",children:(0,r.jsx)(er.$,{type:"submit",disabled:n,children:n?"Saving...":l})})]})}function eh({questions:e,onAddQuestion:s,onUpdateQuestion:t,onDeleteQuestion:n,isSaving:l}){let[a,o]=(0,i.useState)("existing"),[d,c]=(0,i.useState)(null),[u,m]=(0,i.useState)("multiple_choice"),h=e=>{confirm("Are you sure you want to delete this question?")&&(n(e),d===e&&c(null))},x=d?e.find(e=>e.id===d):null;return(0,r.jsx)("div",{className:"space-y-6",children:(0,r.jsxs)(U,{value:a,onValueChange:o,children:[(0,r.jsxs)(ee,{className:"grid grid-cols-2 w-full max-w-md",children:[(0,r.jsx)(es,{value:"existing",children:"Existing Questions"}),(0,r.jsx)(es,{value:"add",children:"Add Question"})]}),(0,r.jsx)(et,{value:"existing",children:(0,r.jsx)("div",{className:"space-y-6",children:0===e.length?(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)("p",{className:"text-muted-foreground mb-4",children:"No questions yet. Add your first question to get started."}),(0,r.jsx)(er.$,{onClick:()=>o("add"),children:"Add First Question"})]}):(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"border rounded-md p-4 h-[500px] overflow-y-auto",children:[(0,r.jsxs)("h3",{className:"font-medium mb-4",children:["Questions (",e.length,")"]}),(0,r.jsx)("ul",{className:"space-y-2",children:e.map(e=>(0,r.jsx)("li",{className:`p-3 border rounded-md cursor-pointer transition-colors ${d===e.id?"border-primary bg-primary/5":"hover:border-primary/50"}`,onClick:()=>c(e.id),children:(0,r.jsxs)("div",{className:"flex justify-between items-start",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium line-clamp-1",children:"string"==typeof e.text?e.text:JSON.parse(e.text).default||"Question"}),(0,r.jsxs)("p",{className:"text-xs text-muted-foreground",children:[e.type," • ",e.points," points"]})]}),(0,r.jsxs)(er.$,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0",onClick:s=>{s.stopPropagation(),h(e.id)},children:[(0,r.jsx)("span",{className:"sr-only",children:"Delete"}),(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})]})]})},e.id))})]}),(0,r.jsx)("div",{className:"border rounded-md p-4 h-[500px] overflow-y-auto",children:x?(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-medium mb-4",children:"Edit Question"}),(0,r.jsx)(em,{questionType:x.type,initialData:x,onSubmit:e=>{d&&(t(d,e),c(null))},isSaving:l,submitLabel:"Update Question"})]}):(0,r.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,r.jsx)("p",{className:"text-muted-foreground",children:"Select a question to edit"})})})]})})}),(0,r.jsx)(et,{value:"add",children:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{htmlFor:"questionType",className:"text-sm font-medium",children:"Question Type"}),(0,r.jsxs)("select",{id:"questionType",value:u,onChange:e=>m(e.target.value),className:"w-full p-2 border rounded-md",children:[(0,r.jsx)("option",{value:"multiple_choice",children:"Multiple Choice"}),(0,r.jsx)("option",{value:"true_false",children:"True/False"}),(0,r.jsx)("option",{value:"short_answer",children:"Short Answer"}),(0,r.jsx)("option",{value:"matching",children:"Matching"}),(0,r.jsx)("option",{value:"fill_in_the_blank",children:"Fill in the Blank"}),(0,r.jsx)("option",{value:"essay",children:"Essay"})]})]}),(0,r.jsxs)("div",{className:"border rounded-md p-4",children:[(0,r.jsx)("h3",{className:"font-medium mb-4",children:"Add New Question"}),(0,r.jsx)(em,{questionType:u,onSubmit:e=>{s({questionId:(0,X.lk)(),type:u,...e}),o("existing")},isSaving:l,submitLabel:"Add Question"})]})]})})]})})}function ex({quiz:e,setQuiz:s,isSaving:t}){let[n,l]=(0,i.useState)("pools"),[a,o]=(0,i.useState)(null),[d,c]=(0,i.useState)(""),[u,m]=(0,i.useState)(""),[h,x]=(0,i.useState)(null),[p,f]=(0,i.useState)(!1),[j,v]=(0,i.useState)(null),[g,b]=(0,i.useState)(""),[N,y]=(0,i.useState)("1"),[w,C]=(0,i.useState)(!0),[k,S]=(0,i.useState)(!1),[q,z]=(0,i.useState)(null),[P,E]=(0,i.useState)(!1),[_,R]=(0,i.useState)([]),[T,$]=(0,i.useState)([]),A=async s=>{o(s),v(null);let t=e.questionPools.find(e=>e.id===s);if(t){x({id:t.id,title:t.title||"",description:t.description||""}),$(t.questions);let s=e.questionPools.flatMap(e=>e.questions.map(e=>e.id));R(e.questions.filter(e=>!s.includes(e.id)))}},F=s=>{v(s),o(null);let t=e.selectionRules.find(e=>e.id===s);t&&z({id:t.id,poolId:t.poolId,selectCount:t.selectCount,randomize:t.randomize,shuffleOrder:t.shuffleOrder})},I=async()=>{f(!0);try{let t=await fetch(`/api/quizzes/${e.id}/pools`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({poolId:(0,X.lk)(),title:d,description:u})});if(!t.ok)throw Error("Failed to create pool");let r=await t.json();s(e=>({...e,questionPools:[...e.questionPools,{...r,questions:[]}]})),c(""),m(""),l("pools")}catch(e){console.error("Error creating pool:",e)}finally{f(!1)}},L=async()=>{if(h){f(!0);try{let t=await fetch(`/api/quizzes/${e.id}/pools/${h.id}`,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({title:h.title,description:h.description})});if(!t.ok)throw Error("Failed to update pool");let r=await t.json();s(e=>({...e,questionPools:e.questionPools.map(e=>e.id===h.id?{...e,...r}:e)}))}catch(e){console.error("Error updating pool:",e)}finally{f(!1)}}},M=async t=>{if(confirm("Are you sure you want to delete this pool? This will also delete any selection rules that use this pool."))try{if(!(await fetch(`/api/quizzes/${e.id}/pools/${t}`,{method:"DELETE"})).ok)throw Error("Failed to delete pool");s(e=>({...e,questionPools:e.questionPools.filter(e=>e.id!==t),selectionRules:e.selectionRules.filter(e=>e.poolId!==t)})),a===t&&(o(null),x(null))}catch(e){console.error("Error deleting pool:",e)}},O=async()=>{E(!0);try{let t=await fetch(`/api/quizzes/${e.id}/rules`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({poolId:g,selectCount:parseInt(N),randomize:w,shuffleOrder:k})});if(!t.ok)throw Error("Failed to create rule");let r=await t.json();s(e=>({...e,selectionRules:[...e.selectionRules,r]})),b(""),y("1"),C(!0),S(!1),l("rules")}catch(e){console.error("Error creating rule:",e)}finally{E(!1)}},D=async()=>{if(q){E(!0);try{let t=await fetch(`/api/quizzes/${e.id}/rules/${q.id}`,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({poolId:q.poolId,selectCount:q.selectCount,randomize:q.randomize,shuffleOrder:q.shuffleOrder})});if(!t.ok)throw Error("Failed to update rule");let r=await t.json();s(e=>({...e,selectionRules:e.selectionRules.map(e=>e.id===q.id?{...e,...r}:e)}))}catch(e){console.error("Error updating rule:",e)}finally{E(!1)}}},Q=async t=>{if(confirm("Are you sure you want to delete this selection rule?"))try{if(!(await fetch(`/api/quizzes/${e.id}/rules/${t}`,{method:"DELETE"})).ok)throw Error("Failed to delete rule");s(e=>({...e,selectionRules:e.selectionRules.filter(e=>e.id!==t)})),j===t&&(v(null),z(null))}catch(e){console.error("Error deleting rule:",e)}};return(0,r.jsx)("div",{className:"space-y-6",children:(0,r.jsxs)(U,{value:n,onValueChange:l,children:[(0,r.jsxs)(ee,{className:"grid grid-cols-3 w-full max-w-md",children:[(0,r.jsx)(es,{value:"pools",children:"Question Pools"}),(0,r.jsx)(es,{value:"rules",children:"Selection Rules"}),(0,r.jsx)(es,{value:"create",children:"Create New"})]}),(0,r.jsx)(et,{value:"pools",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"border rounded-md p-4 h-[500px] overflow-y-auto",children:[(0,r.jsxs)("h3",{className:"font-medium mb-4",children:["Pools (",e.questionPools.length,")"]}),0===e.questionPools.length?(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)("p",{className:"text-muted-foreground mb-4",children:"No question pools yet. Create your first pool to get started."}),(0,r.jsx)(er.$,{onClick:()=>l("create"),children:"Create First Pool"})]}):(0,r.jsx)("ul",{className:"space-y-2",children:e.questionPools.map(e=>(0,r.jsx)("li",{className:`p-3 border rounded-md cursor-pointer transition-colors ${a===e.id?"border-primary bg-primary/5":"hover:border-primary/50"}`,onClick:()=>A(e.id),children:(0,r.jsxs)("div",{className:"flex justify-between items-start",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium",children:e.title||`Pool ${e.poolId}`}),(0,r.jsxs)("p",{className:"text-xs text-muted-foreground",children:[e.questions.length," questions"]})]}),(0,r.jsxs)(er.$,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0",onClick:s=>{s.stopPropagation(),M(e.id)},children:[(0,r.jsx)("span",{className:"sr-only",children:"Delete"}),(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})]})]})},e.id))})]}),(0,r.jsx)("div",{className:"border rounded-md p-4 h-[500px] overflow-y-auto",children:a&&h?(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("h3",{className:"font-medium",children:"Edit Pool"}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{htmlFor:"poolTitle",className:"text-sm font-medium",children:"Pool Title"}),(0,r.jsx)("input",{id:"poolTitle",type:"text",value:h.title,onChange:e=>x({...h,title:e.target.value}),className:"w-full p-2 border rounded-md",placeholder:"Enter pool title"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{htmlFor:"poolDescription",className:"text-sm font-medium",children:"Description"}),(0,r.jsx)("textarea",{id:"poolDescription",value:h.description,onChange:e=>x({...h,description:e.target.value}),className:"w-full p-2 border rounded-md",placeholder:"Enter pool description"})]}),(0,r.jsx)("div",{className:"flex justify-end",children:(0,r.jsx)(er.$,{onClick:L,disabled:p,children:p?"Saving...":"Save Pool"})}),(0,r.jsx)("div",{className:"pt-4 border-t mt-4",children:(0,r.jsx)("h4",{className:"font-medium mb-2",children:"Questions in this Pool"})})]}):(0,r.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,r.jsx)("p",{className:"text-muted-foreground",children:"Select a pool to edit"})})})]})}),(0,r.jsx)(et,{value:"rules",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"border rounded-md p-4 h-[500px] overflow-y-auto",children:[(0,r.jsxs)("h3",{className:"font-medium mb-4",children:["Selection Rules (",e.selectionRules.length,")"]}),0===e.selectionRules.length?(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)("p",{className:"text-muted-foreground mb-4",children:"No selection rules yet. Create your first rule to get started."}),(0,r.jsx)(er.$,{onClick:()=>l("create"),children:"Create First Rule"})]}):(0,r.jsx)("ul",{className:"space-y-2",children:e.selectionRules.map(s=>{let t=e.questionPools.find(e=>e.id===s.poolId);return(0,r.jsx)("li",{className:`p-3 border rounded-md cursor-pointer transition-colors ${j===s.id?"border-primary bg-primary/5":"hover:border-primary/50"}`,onClick:()=>F(s.id),children:(0,r.jsxs)("div",{className:"flex justify-between items-start",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("p",{className:"font-medium",children:["Select ",s.selectCount," from ",t?.title||s.poolId]}),(0,r.jsxs)("p",{className:"text-xs text-muted-foreground",children:[s.randomize?"Random selection":"Sequential selection",s.shuffleOrder?", shuffled order":""]})]}),(0,r.jsxs)(er.$,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0",onClick:e=>{e.stopPropagation(),Q(s.id)},children:[(0,r.jsx)("span",{className:"sr-only",children:"Delete"}),(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})]})]})},s.id)})})]}),(0,r.jsx)("div",{className:"border rounded-md p-4 h-[500px] overflow-y-auto",children:j&&q?(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("h3",{className:"font-medium",children:"Edit Selection Rule"}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{htmlFor:"rulePool",className:"text-sm font-medium",children:"Question Pool"}),(0,r.jsxs)("select",{id:"rulePool",value:q.poolId,onChange:e=>z({...q,poolId:e.target.value}),className:"w-full p-2 border rounded-md",required:!0,children:[(0,r.jsx)("option",{value:"",children:"-- Select a pool --"}),e.questionPools.map(e=>(0,r.jsxs)("option",{value:e.id,children:[e.title||`Pool ${e.poolId}`," (",e.questions.length," questions)"]},e.id))]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{htmlFor:"ruleSelectCount",className:"text-sm font-medium",children:"Number of Questions to Select"}),(0,r.jsx)("input",{id:"ruleSelectCount",type:"number",min:"1",value:q.selectCount,onChange:e=>z({...q,selectCount:parseInt(e.target.value)}),className:"w-full p-2 border rounded-md",required:!0})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,r.jsx)("input",{type:"checkbox",checked:q.randomize,onChange:e=>z({...q,randomize:e.target.checked})}),(0,r.jsx)("span",{children:"Randomize Selection"})]}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground ml-6",children:"If checked, questions will be randomly selected from the pool."})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,r.jsx)("input",{type:"checkbox",checked:q.shuffleOrder,onChange:e=>z({...q,shuffleOrder:e.target.checked})}),(0,r.jsx)("span",{children:"Shuffle Order"})]}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground ml-6",children:"If checked, the order of selected questions will be randomized."})]}),(0,r.jsx)("div",{className:"flex justify-end",children:(0,r.jsx)(er.$,{onClick:D,disabled:P,children:P?"Saving...":"Save Rule"})})]}):(0,r.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,r.jsx)("p",{className:"text-muted-foreground",children:"Select a rule to edit"})})})]})}),(0,r.jsx)(et,{value:"create",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"border rounded-md p-4",children:[(0,r.jsx)("h3",{className:"font-medium mb-4",children:"Create New Pool"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{htmlFor:"newPoolTitle",className:"text-sm font-medium",children:"Pool Title"}),(0,r.jsx)("input",{id:"newPoolTitle",type:"text",value:d,onChange:e=>c(e.target.value),className:"w-full p-2 border rounded-md",placeholder:"Enter pool title",required:!0})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{htmlFor:"newPoolDescription",className:"text-sm font-medium",children:"Description"}),(0,r.jsx)("textarea",{id:"newPoolDescription",value:u,onChange:e=>m(e.target.value),className:"w-full p-2 border rounded-md",placeholder:"Enter pool description (optional)"})]}),(0,r.jsx)("div",{className:"flex justify-end",children:(0,r.jsx)(er.$,{onClick:I,disabled:!d||p,children:p?"Creating...":"Create Pool"})})]})]}),(0,r.jsxs)("div",{className:"border rounded-md p-4",children:[(0,r.jsx)("h3",{className:"font-medium mb-4",children:"Create New Selection Rule"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{htmlFor:"newRulePool",className:"text-sm font-medium",children:"Question Pool"}),(0,r.jsxs)("select",{id:"newRulePool",value:g,onChange:e=>b(e.target.value),className:"w-full p-2 border rounded-md",required:!0,children:[(0,r.jsx)("option",{value:"",children:"-- Select a pool --"}),e.questionPools.map(e=>(0,r.jsxs)("option",{value:e.id,children:[e.title||`Pool ${e.poolId}`," (",e.questions.length," questions)"]},e.id))]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{htmlFor:"newRuleSelectCount",className:"text-sm font-medium",children:"Number of Questions to Select"}),(0,r.jsx)("input",{id:"newRuleSelectCount",type:"number",min:"1",value:N,onChange:e=>y(e.target.value),className:"w-full p-2 border rounded-md",required:!0})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,r.jsx)("input",{type:"checkbox",checked:w,onChange:e=>C(e.target.checked)}),(0,r.jsx)("span",{children:"Randomize Selection"})]}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground ml-6",children:"If checked, questions will be randomly selected from the pool."})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,r.jsx)("input",{type:"checkbox",checked:k,onChange:e=>S(e.target.checked)}),(0,r.jsx)("span",{children:"Shuffle Order"})]}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground ml-6",children:"If checked, the order of selected questions will be randomized."})]}),(0,r.jsx)("div",{className:"flex justify-end",children:(0,r.jsx)(er.$,{onClick:O,disabled:!g||!N||P,children:P?"Creating...":"Create Rule"})})]})]})]})})]})})}var ep=t(28531),ef=t.n(ep);function ej({quiz:e,onPublishStatusChange:s,isSaving:t}){let[n,l]=(0,i.useState)(!1);return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"p-4 border rounded-md bg-muted/30",children:[(0,r.jsx)("h3",{className:"font-medium mb-2",children:"Current Status"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:`w-3 h-3 rounded-full ${e.isPublished?"bg-green-500":"bg-amber-500"}`}),(0,r.jsx)("p",{children:e.isPublished?"Published - This quiz is visible to others":"Draft - Only you can see this quiz"})]})]}),n?(0,r.jsxs)("div",{className:"p-4 border rounded-md bg-primary/5",children:[(0,r.jsx)("h3",{className:"font-medium mb-2",children:"Publish Confirmation"}),(0,r.jsx)("p",{className:"mb-4",children:"Are you sure you want to publish this quiz? Once published, it will be visible to others."}),(0,r.jsxs)("div",{className:"flex space-x-4",children:[(0,r.jsx)(er.$,{onClick:()=>{s(!0),l(!1)},disabled:t,children:t?"Publishing...":"Yes, Publish Quiz"}),(0,r.jsx)(er.$,{variant:"outline",onClick:()=>{l(!1)},disabled:t,children:"Cancel"})]})]}):(0,r.jsxs)("div",{className:"flex space-x-4",children:[(0,r.jsx)(er.$,{onClick:()=>{e.isPublished?s(!1):l(!0)},variant:e.isPublished?"outline":"default",disabled:t,children:t?e.isPublished?"Unpublishing...":"Publishing...":e.isPublished?"Unpublish Quiz":"Publish Quiz"}),e.isPublished&&(0,r.jsx)(er.$,{asChild:!0,children:(0,r.jsx)(ef(),{href:`/quiz/${e.id}`,target:"_blank",children:"View Published Quiz"})})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("h3",{className:"font-medium",children:"Publishing Checklist"}),(0,r.jsxs)("ul",{className:"space-y-2",children:[(0,r.jsxs)("li",{className:"flex items-start space-x-2",children:[(0,r.jsx)("div",{className:`mt-1 w-5 h-5 flex items-center justify-center rounded-full ${e.title?"bg-green-100 text-green-600":"bg-amber-100 text-amber-600"}`,children:e.title?"✓":"!"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium",children:"Quiz Title"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:e.title?"Title is set":"Quiz needs a title"})]})]}),(0,r.jsxs)("li",{className:"flex items-start space-x-2",children:[(0,r.jsx)("div",{className:`mt-1 w-5 h-5 flex items-center justify-center rounded-full ${e.questions.length>0?"bg-green-100 text-green-600":"bg-amber-100 text-amber-600"}`,children:e.questions.length>0?"✓":"!"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium",children:"Questions"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:e.questions.length>0?`Quiz has ${e.questions.length} question(s)`:"Quiz needs at least one question"})]})]}),(0,r.jsxs)("li",{className:"flex items-start space-x-2",children:[(0,r.jsx)("div",{className:`mt-1 w-5 h-5 flex items-center justify-center rounded-full ${null!==e.passingScore?"bg-green-100 text-green-600":"bg-amber-100 text-amber-600"}`,children:null!==e.passingScore?"✓":"!"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium",children:"Passing Score"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:null!==e.passingScore?`Passing score is set to ${e.passingScore}%`:"Consider setting a passing score"})]})]}),(0,r.jsxs)("li",{className:"flex items-start space-x-2",children:[(0,r.jsx)("div",{className:`mt-1 w-5 h-5 flex items-center justify-center rounded-full ${null!==e.timeLimit?"bg-green-100 text-green-600":"bg-amber-100 text-amber-600"}`,children:null!==e.timeLimit?"✓":"!"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium",children:"Time Limit"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:null!==e.timeLimit?`Time limit is set to ${e.timeLimit} minutes`:"Consider setting a time limit"})]})]})]})]})]})}function ev({quiz:e}){let s=(0,l.useRouter)(),[t,n]=(0,i.useState)(e),[a,o]=(0,i.useState)("details"),[d,c]=(0,i.useState)(!1),[u,m]=(0,i.useState)(null),h=async e=>{c(!0),m(null);try{let s=await fetch(`/api/quizzes/${t.id}`,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!s.ok)throw Error("Failed to update quiz details");let r=await s.json();n(e=>({...e,...r})),m("Quiz details saved successfully")}catch(e){console.error("Error saving quiz details:",e),m("Error saving quiz details")}finally{c(!1)}},x=async e=>{c(!0),m(null);try{let s=await fetch(`/api/quizzes/${t.id}/questions`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!s.ok)throw Error("Failed to add question");let r=await s.json();n(e=>({...e,questions:[...e.questions,r]})),m("Question added successfully")}catch(e){console.error("Error adding question:",e),m("Error adding question")}finally{c(!1)}},p=async(e,s)=>{c(!0),m(null);try{let r=await fetch(`/api/quizzes/${t.id}/questions/${e}`,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify(s)});if(!r.ok)throw Error("Failed to update question");let i=await r.json();n(s=>({...s,questions:s.questions.map(s=>s.id===e?{...s,...i}:s)})),m("Question updated successfully")}catch(e){console.error("Error updating question:",e),m("Error updating question")}finally{c(!1)}},f=async e=>{c(!0),m(null);try{if(!(await fetch(`/api/quizzes/${t.id}/questions/${e}`,{method:"DELETE"})).ok)throw Error("Failed to delete question");n(s=>({...s,questions:s.questions.filter(s=>s.id!==e)})),m("Question deleted successfully")}catch(e){console.error("Error deleting question:",e),m("Error deleting question")}finally{c(!1)}},j=async e=>{c(!0),m(null);try{if(!(await fetch(`/api/quizzes/${t.id}/publish`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({isPublished:e})})).ok)throw Error(`Failed to ${e?"publish":"unpublish"} quiz`);n(s=>({...s,isPublished:e})),m(`Quiz ${e?"published":"unpublished"} successfully`)}catch(s){console.error(`Error ${e?"publishing":"unpublishing"} quiz:`,s),m(`Error ${e?"publishing":"unpublishing"} quiz`)}finally{c(!1)}};return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold",children:"Edit Quiz"}),(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[u&&(0,r.jsx)("p",{className:`text-sm ${u.includes("Error")?"text-red-500":"text-green-500"}`,children:u}),(0,r.jsx)(er.$,{variant:"outline",onClick:()=>s.push("/dashboard/quizzes"),children:"Back to Quizzes"}),(0,r.jsx)(er.$,{onClick:()=>s.push(`/dashboard/quizzes/${t.id}/preview`),children:"Preview Quiz"})]})]}),(0,r.jsxs)(U,{value:a,onValueChange:o,children:[(0,r.jsxs)(ee,{className:"grid grid-cols-4 w-full max-w-3xl",children:[(0,r.jsx)(es,{value:"details",children:"Quiz Details"}),(0,r.jsx)(es,{value:"questions",children:"Questions"}),(0,r.jsx)(es,{value:"pools",children:"Question Pools"}),(0,r.jsx)(es,{value:"publish",children:"Publish"})]}),(0,r.jsx)(et,{value:"details",children:(0,r.jsxs)(ei.Zp,{children:[(0,r.jsxs)(ei.aR,{children:[(0,r.jsx)(ei.ZB,{children:"Quiz Details"}),(0,r.jsx)(ei.BT,{children:"Edit the basic information for your quiz"})]}),(0,r.jsx)(ei.Wu,{children:(0,r.jsx)(en,{quiz:t,onSave:h,isSaving:d})})]})}),(0,r.jsx)(et,{value:"questions",children:(0,r.jsxs)(ei.Zp,{children:[(0,r.jsxs)(ei.aR,{children:[(0,r.jsx)(ei.ZB,{children:"Questions"}),(0,r.jsx)(ei.BT,{children:"Add, edit, or remove questions from your quiz"})]}),(0,r.jsx)(ei.Wu,{children:(0,r.jsx)(eh,{questions:t.questions,onAddQuestion:x,onUpdateQuestion:p,onDeleteQuestion:f,isSaving:d})})]})}),(0,r.jsx)(et,{value:"pools",children:(0,r.jsxs)(ei.Zp,{children:[(0,r.jsxs)(ei.aR,{children:[(0,r.jsx)(ei.ZB,{children:"Question Pools"}),(0,r.jsx)(ei.BT,{children:"Create pools of questions for dynamic selection"})]}),(0,r.jsx)(ei.Wu,{children:(0,r.jsx)(ex,{quiz:t,setQuiz:n,isSaving:d})})]})}),(0,r.jsx)(et,{value:"publish",children:(0,r.jsxs)(ei.Zp,{children:[(0,r.jsxs)(ei.aR,{children:[(0,r.jsx)(ei.ZB,{children:"Publish Settings"}),(0,r.jsx)(ei.BT,{children:"Control the visibility and availability of your quiz"})]}),(0,r.jsx)(ei.Wu,{children:(0,r.jsx)(ej,{quiz:t,onPublishStatusChange:j,isSaving:d})})]})})]})]})}},39400:(e,s,t)=>{"use strict";t.d(s,{$:()=>d});var r=t(45512),i=t(58009),n=t(12705),l=t(21643),a=t(96720);let o=(0,l.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=i.forwardRef(({className:e,variant:s,size:t,asChild:i=!1,...l},d)=>{let c=i?n.DX:"button";return(0,r.jsx)(c,{className:(0,a.cn)(o({variant:s,size:t,className:e})),ref:d,...l})});d.displayName="Button"},64590:(e,s,t)=>{"use strict";t.d(s,{BT:()=>d,Wu:()=>c,ZB:()=>o,Zp:()=>l,aR:()=>a,wL:()=>u});var r=t(45512),i=t(58009),n=t(96720);let l=i.forwardRef(({className:e,...s},t)=>(0,r.jsx)("div",{ref:t,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...s}));l.displayName="Card";let a=i.forwardRef(({className:e,...s},t)=>(0,r.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",e),...s}));a.displayName="CardHeader";let o=i.forwardRef(({className:e,...s},t)=>(0,r.jsx)("h3",{ref:t,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",e),...s}));o.displayName="CardTitle";let d=i.forwardRef(({className:e,...s},t)=>(0,r.jsx)("p",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",e),...s}));d.displayName="CardDescription";let c=i.forwardRef(({className:e,...s},t)=>(0,r.jsx)("div",{ref:t,className:(0,n.cn)("p-6 pt-0",e),...s}));c.displayName="CardContent";let u=i.forwardRef(({className:e,...s},t)=>(0,r.jsx)("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",e),...s}));u.displayName="CardFooter"},96720:(e,s,t)=>{"use strict";t.d(s,{Yq:()=>a,cn:()=>n,lk:()=>l});var r=t(82281),i=t(94805);function n(...e){return(0,i.QP)((0,r.$)(e))}function l(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){let s=16*Math.random()|0;return("x"===e?s:3&s|8).toString(16)})}function a(e){return("string"==typeof e?new Date(e):e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})}},33405:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var r=t(62740),i=t(26996);function n({children:e}){return(0,r.jsxs)("div",{className:"flex min-h-screen flex-col",children:[(0,r.jsx)(i.default,{}),(0,r.jsx)("main",{className:"flex-1",children:e})]})}},54363:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>d});var r=t(62740),i=t(31831),n=t(51825),l=t(37702),a=t(27914),o=t(76297);async function d({params:e}){let s=await (0,n.getServerSession)(l.N);s||(0,i.notFound)();let t=await a.db.quiz.findUnique({where:{id:e.id,creatorId:s.user.id},include:{questions:!0,questionPools:{include:{questions:!0}},selectionRules:!0}});return t||(0,i.notFound)(),(0,r.jsx)("div",{className:"container mx-auto py-8 px-4",children:(0,r.jsx)(o.default,{quiz:t})})}},26996:(e,s,t)=>{"use strict";t.d(s,{default:()=>r});let r=(0,t(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/components/dashboard/Navbar.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/components/dashboard/Navbar.tsx","default")},76297:(e,s,t)=>{"use strict";t.d(s,{default:()=>r});let r=(0,t(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/components/quiz/editor/QuizEditor.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/components/quiz/editor/QuizEditor.tsx","default")}};var s=require("../../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[638,263,3,367,640,30],()=>t(20270));module.exports=r})();