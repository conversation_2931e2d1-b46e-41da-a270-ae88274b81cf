(()=>{var e={};e.id=500,e.ids=[500],e.modules={96330:e=>{"use strict";e.exports=require("@prisma/client")},5486:e=>{"use strict";e.exports=require("bcrypt")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},12412:e=>{"use strict";e.exports=require("assert")},79428:e=>{"use strict";e.exports=require("buffer")},55511:e=>{"use strict";e.exports=require("crypto")},94735:e=>{"use strict";e.exports=require("events")},81630:e=>{"use strict";e.exports=require("http")},55591:e=>{"use strict";e.exports=require("https")},33873:e=>{"use strict";e.exports=require("path")},11723:e=>{"use strict";e.exports=require("querystring")},79551:e=>{"use strict";e.exports=require("url")},28354:e=>{"use strict";e.exports=require("util")},74075:e=>{"use strict";e.exports=require("zlib")},20270:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var i=t(70260),r=t(28203),n=t(25155),a=t.n(n),l=t(67292),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);t.d(s,o);let d=["",{children:["dashboard",{children:["quizzes",{children:["[id]",{children:["edit",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,54363)),"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/app/dashboard/quizzes/[id]/edit/page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,33405)),"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/app/dashboard/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,71354)),"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["/Users/<USER>/Documents/augment-projects/hacking-quiz/src/app/dashboard/quizzes/[id]/edit/page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},m=new i.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/dashboard/quizzes/[id]/edit/page",pathname:"/dashboard/quizzes/[id]/edit",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},92295:(e,s,t)=>{Promise.resolve().then(t.bind(t,26996))},31615:(e,s,t)=>{Promise.resolve().then(t.bind(t,44848))},6062:(e,s,t)=>{Promise.resolve().then(t.bind(t,76297))},69110:(e,s,t)=>{Promise.resolve().then(t.bind(t,57010))},44848:(e,s,t)=>{"use strict";t.d(s,{default:()=>d});var i=t(45512),r=t(28531),n=t.n(r),a=t(79334),l=t(90993),o=t(39400);function d(){let e=(0,a.usePathname)(),{data:s}=(0,l.useSession)(),t=s=>e===s||e.startsWith(`${s}/`);return(0,i.jsx)("header",{className:"border-b",children:(0,i.jsxs)("div",{className:"container mx-auto px-4 flex h-16 items-center justify-between",children:[(0,i.jsxs)("div",{className:"flex items-center gap-6",children:[(0,i.jsx)(n(),{href:"/",className:"text-xl font-bold",children:"QuizFlow"}),(0,i.jsxs)("nav",{className:"hidden md:flex gap-6",children:[(0,i.jsx)(n(),{href:"/dashboard",className:`text-sm font-medium transition-colors hover:text-primary ${!t("/dashboard")||t("/dashboard/quizzes")||t("/dashboard/activity")?"text-muted-foreground":"text-primary"}`,children:"Dashboard"}),(0,i.jsx)(n(),{href:"/dashboard/quizzes",className:`text-sm font-medium transition-colors hover:text-primary ${t("/dashboard/quizzes")?"text-primary":"text-muted-foreground"}`,children:"My Quizzes"}),(0,i.jsx)(n(),{href:"/dashboard/activity",className:`text-sm font-medium transition-colors hover:text-primary ${t("/dashboard/activity")?"text-primary":"text-muted-foreground"}`,children:"Activity"}),(0,i.jsx)(n(),{href:"/explore",className:`text-sm font-medium transition-colors hover:text-primary ${t("/explore")?"text-primary":"text-muted-foreground"}`,children:"Explore"})]})]}),(0,i.jsx)("div",{className:"flex items-center gap-4",children:s?(0,i.jsxs)("div",{className:"flex items-center gap-4",children:[(0,i.jsxs)("div",{className:"hidden md:block",children:[(0,i.jsx)("div",{className:"text-sm font-medium",children:s.user.name}),(0,i.jsx)("div",{className:"text-xs text-muted-foreground",children:s.user.email})]}),(0,i.jsx)(o.$,{variant:"outline",size:"sm",onClick:()=>(0,l.signOut)({callbackUrl:"/"}),children:"Sign Out"})]}):(0,i.jsx)(o.$,{asChild:!0,size:"sm",children:(0,i.jsx)(n(),{href:"/auth/login",children:"Sign In"})})})]})})}},57010:(e,s,t)=>{"use strict";t.d(s,{default:()=>y});var i=t(45512),r=t(58009),n=t(79334),a=t(39400),l=t(64590);function o({quiz:e,onSave:s,isSaving:t}){let[n,l]=(0,r.useState)(e.title),[o,d]=(0,r.useState)(e.description||""),[c,u]=(0,r.useState)(e.tags.join(", ")),[m,h]=(0,r.useState)(e.passingScore?.toString()||"70"),[x,p]=(0,r.useState)(e.timeLimit?.toString()||"15"),[f,j]=(0,r.useState)(e.locale),[v,g]=(0,r.useState)(e.markupFormat);return(0,i.jsxs)("form",{onSubmit:e=>{e.preventDefault(),s({title:n,description:o||null,tags:c.split(",").map(e=>e.trim()).filter(e=>e),passingScore:m?parseFloat(m):null,timeLimit:x?parseInt(x):null,locale:f,markupFormat:v})},className:"space-y-6",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)("label",{htmlFor:"title",className:"text-sm font-medium",children:"Quiz Title"}),(0,i.jsx)("input",{id:"title",type:"text",value:n,onChange:e=>l(e.target.value),className:"w-full p-2 border rounded-md",required:!0})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)("label",{htmlFor:"description",className:"text-sm font-medium",children:"Description"}),(0,i.jsx)("textarea",{id:"description",value:o,onChange:e=>d(e.target.value),className:"w-full p-2 border rounded-md min-h-[100px]",placeholder:"Enter quiz description"})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)("label",{htmlFor:"tags",className:"text-sm font-medium",children:"Tags"}),(0,i.jsx)("input",{id:"tags",type:"text",value:c,onChange:e=>u(e.target.value),className:"w-full p-2 border rounded-md",placeholder:"Enter tags separated by commas (e.g., security, basics, networking)"}),(0,i.jsx)("p",{className:"text-xs text-muted-foreground",children:"Separate tags with commas (e.g., security, basics, networking)"})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)("label",{htmlFor:"passingScore",className:"text-sm font-medium",children:"Passing Score (%)"}),(0,i.jsx)("input",{id:"passingScore",type:"number",min:"0",max:"100",value:m,onChange:e=>h(e.target.value),className:"w-full p-2 border rounded-md"})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)("label",{htmlFor:"timeLimit",className:"text-sm font-medium",children:"Time Limit (minutes)"}),(0,i.jsx)("input",{id:"timeLimit",type:"number",min:"1",value:x,onChange:e=>p(e.target.value),className:"w-full p-2 border rounded-md"})]})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)("label",{htmlFor:"locale",className:"text-sm font-medium",children:"Locale"}),(0,i.jsxs)("select",{id:"locale",value:f,onChange:e=>j(e.target.value),className:"w-full p-2 border rounded-md",children:[(0,i.jsx)("option",{value:"en-US",children:"English (US)"}),(0,i.jsx)("option",{value:"en-GB",children:"English (UK)"}),(0,i.jsx)("option",{value:"es-ES",children:"Spanish"}),(0,i.jsx)("option",{value:"fr-FR",children:"French"}),(0,i.jsx)("option",{value:"de-DE",children:"German"}),(0,i.jsx)("option",{value:"ja-JP",children:"Japanese"}),(0,i.jsx)("option",{value:"zh-CN",children:"Chinese (Simplified)"})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)("label",{htmlFor:"markupFormat",className:"text-sm font-medium",children:"Markup Format"}),(0,i.jsxs)("select",{id:"markupFormat",value:v,onChange:e=>g(e.target.value),className:"w-full p-2 border rounded-md",children:[(0,i.jsx)("option",{value:"markdown",children:"Markdown"}),(0,i.jsx)("option",{value:"html",children:"HTML"}),(0,i.jsx)("option",{value:"plain_text",children:"Plain Text"})]})]})]}),(0,i.jsx)("div",{className:"flex justify-end pt-4",children:(0,i.jsx)(a.$,{type:"submit",disabled:t,children:t?"Saving...":"Save Details"})})]})}var d=t(96720);function c({data:e,onChange:s}){let[t,n]=(0,r.useState)(e?.single_correct_answer===void 0||e.single_correct_answer),[l,o]=(0,r.useState)(e?.options||[{id:(0,d.lk)(),text:"",is_correct:!1},{id:(0,d.lk)(),text:"",is_correct:!1}]),[c,u]=(0,r.useState)(e?.scoring_method||"all_or_nothing"),m=(e,s)=>{o(l.map(t=>t.id===e?{...t,text:s}:t))},h=(e,s)=>{t&&s?o(l.map(s=>({...s,is_correct:s.id===e}))):o(l.map(t=>t.id===e?{...t,is_correct:s}:t))},x=(e,s)=>{o(l.map(t=>t.id===e?{...t,feedback:s}:t))},p=e=>{if(l.length<=2){alert("A multiple choice question must have at least 2 options.");return}o(l.filter(s=>s.id!==e))};return(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)("label",{className:"text-sm font-medium",children:"Question Type"}),(0,i.jsxs)("div",{className:"flex space-x-4",children:[(0,i.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,i.jsx)("input",{type:"radio",checked:t,onChange:()=>n(!0)}),(0,i.jsx)("span",{children:"Single Choice (Radio Buttons)"})]}),(0,i.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,i.jsx)("input",{type:"radio",checked:!t,onChange:()=>n(!1)}),(0,i.jsx)("span",{children:"Multiple Choice (Checkboxes)"})]})]})]}),!t&&(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)("label",{className:"text-sm font-medium",children:"Scoring Method"}),(0,i.jsxs)("div",{className:"flex space-x-4",children:[(0,i.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,i.jsx)("input",{type:"radio",checked:"all_or_nothing"===c,onChange:()=>u("all_or_nothing")}),(0,i.jsx)("span",{children:"All or Nothing"})]}),(0,i.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,i.jsx)("input",{type:"radio",checked:"partial_credit"===c,onChange:()=>u("partial_credit")}),(0,i.jsx)("span",{children:"Partial Credit"})]})]}),(0,i.jsxs)("p",{className:"text-xs text-muted-foreground",children:["All or Nothing: Full points only if all correct options are selected and no incorrect options.",(0,i.jsx)("br",{}),"Partial Credit: Points awarded based on correct selections."]})]}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsx)("label",{className:"text-sm font-medium",children:"Options"}),l.map((e,s)=>(0,i.jsxs)("div",{className:"border rounded-md p-4 space-y-4",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("h4",{className:"font-medium",children:["Option ",s+1]}),(0,i.jsxs)(a.$,{type:"button",variant:"ghost",size:"sm",onClick:()=>p(e.id),className:"h-8 w-8 p-0",children:[(0,i.jsx)("span",{className:"sr-only",children:"Remove"}),(0,i.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,i.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)("label",{className:"text-sm font-medium",children:"Option Text"}),(0,i.jsx)("input",{type:"text",value:e.text,onChange:s=>m(e.id,s.target.value),className:"w-full p-2 border rounded-md",placeholder:"Enter option text",required:!0})]}),(0,i.jsx)("div",{className:"space-y-2",children:(0,i.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,i.jsx)("input",{type:t?"radio":"checkbox",checked:e.is_correct,onChange:s=>h(e.id,s.target.checked),name:"correctOption"}),(0,i.jsx)("span",{children:"Correct Answer"})]})}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)("label",{className:"text-sm font-medium",children:"Feedback for this option"}),(0,i.jsx)("input",{type:"text",value:e.feedback||"",onChange:s=>x(e.id,s.target.value),className:"w-full p-2 border rounded-md",placeholder:"Optional feedback when this option is selected"})]})]},e.id)),(0,i.jsx)(a.$,{type:"button",variant:"outline",onClick:()=>{o([...l,{id:(0,d.lk)(),text:"",is_correct:!1}])},className:"w-full",children:"Add Option"})]})]})}function u({data:e,onChange:s}){let[t,n]=(0,r.useState)(e?.correct_answer===void 0||e.correct_answer);return(0,i.jsx)("div",{className:"space-y-4",children:(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)("label",{className:"text-sm font-medium",children:"Correct Answer"}),(0,i.jsxs)("div",{className:"flex space-x-4",children:[(0,i.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,i.jsx)("input",{type:"radio",checked:!0===t,onChange:()=>n(!0),name:"correctAnswer"}),(0,i.jsx)("span",{children:"True"})]}),(0,i.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,i.jsx)("input",{type:"radio",checked:!1===t,onChange:()=>n(!1),name:"correctAnswer"}),(0,i.jsx)("span",{children:"False"})]})]})]})})}function m({data:e,onChange:s}){let[t,n]=(0,r.useState)(e?.correct_answers||[""]),[l,o]=(0,r.useState)(e?.case_sensitive!==void 0&&e.case_sensitive),[d,c]=(0,r.useState)(e?.trim_whitespace===void 0||e.trim_whitespace),[u,m]=(0,r.useState)(e?.exact_match===void 0||e.exact_match),h=(e,s)=>{let i=[...t];i[e]=s,n(i)},x=e=>{if(t.length<=1){alert("You must have at least one correct answer.");return}let s=[...t];s.splice(e,1),n(s)};return(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsx)("label",{className:"text-sm font-medium",children:"Correct Answers"}),(0,i.jsx)("p",{className:"text-xs text-muted-foreground",children:"Add multiple acceptable answers. The question will be marked correct if the student's answer matches any of these."}),t.map((e,s)=>(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)("input",{type:"text",value:e,onChange:e=>h(s,e.target.value),className:"flex-1 p-2 border rounded-md",placeholder:"Enter a correct answer",required:!0}),(0,i.jsxs)(a.$,{type:"button",variant:"ghost",size:"sm",onClick:()=>x(s),className:"h-8 w-8 p-0",disabled:t.length<=1,children:[(0,i.jsx)("span",{className:"sr-only",children:"Remove"}),(0,i.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,i.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})]})]},s)),(0,i.jsx)(a.$,{type:"button",variant:"outline",onClick:()=>{n([...t,""])},className:"w-full",children:"Add Another Correct Answer"})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)("label",{className:"text-sm font-medium",children:"Answer Options"}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,i.jsx)("input",{type:"checkbox",checked:l,onChange:e=>o(e.target.checked)}),(0,i.jsx)("span",{children:"Case Sensitive"})]}),(0,i.jsx)("p",{className:"text-xs text-muted-foreground ml-6",children:'If checked, "Answer" and "answer" will be treated as different answers.'})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,i.jsx)("input",{type:"checkbox",checked:d,onChange:e=>c(e.target.checked)}),(0,i.jsx)("span",{children:"Trim Whitespace"})]}),(0,i.jsx)("p",{className:"text-xs text-muted-foreground ml-6",children:"If checked, leading and trailing spaces will be ignored."})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,i.jsx)("input",{type:"checkbox",checked:u,onChange:e=>m(e.target.checked)}),(0,i.jsx)("span",{children:"Exact Match Required"})]}),(0,i.jsx)("p",{className:"text-xs text-muted-foreground ml-6",children:"If unchecked, partial matches or keyword detection may be used (implementation dependent)."})]})]})]})}function h({data:e,onChange:s}){let[t,n]=(0,r.useState)(e?.stems||[{id:(0,d.lk)(),text:""},{id:(0,d.lk)(),text:""}]),[l,o]=(0,r.useState)(e?.options||[{id:(0,d.lk)(),text:""},{id:(0,d.lk)(),text:""}]),[c,u]=(0,r.useState)(e?.correct_pairs||[]),m=(e,s)=>{n(t.map(t=>t.id===e?{...t,text:s}:t))},h=(e,s)=>{o(l.map(t=>t.id===e?{...t,text:s}:t))},x=(e,s)=>{u(c.map(t=>t.stem_id===e?{...t,option_id:s}:t))},p=e=>{if(t.length<=2){alert("A matching question must have at least 2 stems.");return}n(t.filter(s=>s.id!==e)),u(c.filter(s=>s.stem_id!==e))},f=e=>{if(l.length<=2){alert("A matching question must have at least 2 options.");return}o(l.filter(s=>s.id!==e)),u(c.map(s=>s.option_id===e?{...s,option_id:""}:s))};return(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsx)("label",{className:"text-sm font-medium",children:"Stems (Left Side)"}),t.map((e,s)=>(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)("input",{type:"text",value:e.text,onChange:s=>m(e.id,s.target.value),className:"flex-1 p-2 border rounded-md",placeholder:`Stem ${s+1}`,required:!0}),(0,i.jsxs)(a.$,{type:"button",variant:"ghost",size:"sm",onClick:()=>p(e.id),className:"h-8 w-8 p-0",disabled:t.length<=2,children:[(0,i.jsx)("span",{className:"sr-only",children:"Remove"}),(0,i.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,i.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})]})]},e.id)),(0,i.jsx)(a.$,{type:"button",variant:"outline",onClick:()=>{let e=(0,d.lk)();n([...t,{id:e,text:""}]),u([...c,{stem_id:e,option_id:""}])},className:"w-full",children:"Add Stem"})]}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsx)("label",{className:"text-sm font-medium",children:"Options (Right Side)"}),l.map((e,s)=>(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)("input",{type:"text",value:e.text,onChange:s=>h(e.id,s.target.value),className:"flex-1 p-2 border rounded-md",placeholder:`Option ${s+1}`,required:!0}),(0,i.jsxs)(a.$,{type:"button",variant:"ghost",size:"sm",onClick:()=>f(e.id),className:"h-8 w-8 p-0",disabled:l.length<=2,children:[(0,i.jsx)("span",{className:"sr-only",children:"Remove"}),(0,i.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,i.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})]})]},e.id)),(0,i.jsx)(a.$,{type:"button",variant:"outline",onClick:()=>{o([...l,{id:(0,d.lk)(),text:""}])},className:"w-full",children:"Add Option"})]}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsx)("label",{className:"text-sm font-medium",children:"Correct Matches"}),(0,i.jsx)("p",{className:"text-xs text-muted-foreground",children:"For each stem, select the matching option."}),t.map((e,s)=>{let t=c.find(s=>s.stem_id===e.id);return(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)("div",{className:"flex-1 p-2 border rounded-md bg-muted",children:e.text||`Stem ${s+1}`}),(0,i.jsx)("div",{className:"text-center px-2",children:"matches"}),(0,i.jsxs)("select",{value:t?.option_id||"",onChange:s=>x(e.id,s.target.value),className:"flex-1 p-2 border rounded-md",required:!0,children:[(0,i.jsx)("option",{value:"",children:"-- Select matching option --"}),l.map(e=>(0,i.jsx)("option",{value:e.id,children:e.text||`Option ${l.findIndex(s=>s.id===e.id)+1}`},e.id))]})]},e.id)})]})]})}function x({data:e,onChange:s}){let[t,n]=(0,r.useState)(e?.text_template||"This is a [BLANK] question with [BLANK] to fill in."),[l,o]=(0,r.useState)(e?.blanks||[]),d=(e,s,t)=>{let i=[...l];if(i[e]){let r=[...i[e].correct_answers];r[s]=t,i[e]={...i[e],correct_answers:r},o(i)}},c=(e,s,t)=>{let i=[...l];i[e]&&(i[e]={...i[e],[s]:t},o(i))},u=e=>{let s=[...l];s[e]&&(s[e]={...s[e],correct_answers:[...s[e].correct_answers,""]},o(s))},m=(e,s)=>{let t=[...l];if(t[e]&&t[e].correct_answers.length>1){let i=[...t[e].correct_answers];i.splice(s,1),t[e]={...t[e],correct_answers:i},o(t)}};return(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)("label",{htmlFor:"textTemplate",className:"text-sm font-medium",children:"Question Text with Blanks"}),(0,i.jsx)("p",{className:"text-xs text-muted-foreground",children:"Use [BLANK] to indicate where students should fill in answers."}),(0,i.jsx)("textarea",{id:"textTemplate",value:t,onChange:e=>n(e.target.value),className:"w-full p-2 border rounded-md min-h-[100px]",placeholder:"Enter text with [BLANK] placeholders",required:!0})]}),l.length>0?(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsx)("h3",{className:"text-sm font-medium",children:"Define Blanks"}),l.map((e,s)=>(0,i.jsxs)("div",{className:"border rounded-md p-4 space-y-4",children:[(0,i.jsxs)("h4",{className:"font-medium",children:["Blank ",s+1]}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsx)("label",{className:"text-sm font-medium",children:"Correct Answers"}),e.correct_answers.map((t,r)=>(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)("input",{type:"text",value:t,onChange:e=>d(s,r,e.target.value),className:"flex-1 p-2 border rounded-md",placeholder:"Enter a correct answer",required:!0}),(0,i.jsxs)(a.$,{type:"button",variant:"ghost",size:"sm",onClick:()=>m(s,r),className:"h-8 w-8 p-0",disabled:e.correct_answers.length<=1,children:[(0,i.jsx)("span",{className:"sr-only",children:"Remove"}),(0,i.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,i.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})]})]},r)),(0,i.jsx)(a.$,{type:"button",variant:"outline",onClick:()=>u(s),className:"w-full",children:"Add Another Correct Answer"})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)("label",{className:"text-sm font-medium",children:"Answer Options"}),(0,i.jsx)("div",{className:"space-y-2",children:(0,i.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,i.jsx)("input",{type:"checkbox",checked:e.case_sensitive,onChange:e=>c(s,"case_sensitive",e.target.checked)}),(0,i.jsx)("span",{children:"Case Sensitive"})]})}),(0,i.jsx)("div",{className:"space-y-2",children:(0,i.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,i.jsx)("input",{type:"checkbox",checked:e.trim_whitespace,onChange:e=>c(s,"trim_whitespace",e.target.checked)}),(0,i.jsx)("span",{children:"Trim Whitespace"})]})})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)("label",{htmlFor:`hint-${s}`,className:"text-sm font-medium",children:"Hint (Optional)"}),(0,i.jsx)("input",{id:`hint-${s}`,type:"text",value:e.hint||"",onChange:e=>c(s,"hint",e.target.value),className:"w-full p-2 border rounded-md",placeholder:"Enter a hint for this blank"})]})]},e.id))]}):(0,i.jsx)("div",{className:"p-4 bg-muted rounded-md",children:(0,i.jsx)("p",{className:"text-center",children:"Add [BLANK] placeholders to your text to create blanks for students to fill in."})})]})}function p({data:e,onChange:s}){let[t,n]=(0,r.useState)(e?.min_word_count?.toString()||""),[a,l]=(0,r.useState)(e?.max_word_count?.toString()||""),[o,d]=(0,r.useState)(e?.guidelines||"");return(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)("label",{htmlFor:"minWordCount",className:"text-sm font-medium",children:"Minimum Word Count"}),(0,i.jsx)("input",{id:"minWordCount",type:"number",min:"0",value:t,onChange:e=>n(e.target.value),className:"w-full p-2 border rounded-md",placeholder:"Optional"})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)("label",{htmlFor:"maxWordCount",className:"text-sm font-medium",children:"Maximum Word Count"}),(0,i.jsx)("input",{id:"maxWordCount",type:"number",min:"0",value:a,onChange:e=>l(e.target.value),className:"w-full p-2 border rounded-md",placeholder:"Optional"})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)("label",{htmlFor:"guidelines",className:"text-sm font-medium",children:"Guidelines for Students"}),(0,i.jsx)("textarea",{id:"guidelines",value:o,onChange:e=>d(e.target.value),className:"w-full p-2 border rounded-md min-h-[100px]",placeholder:"Enter guidelines or prompts for the essay (optional)"})]}),(0,i.jsx)("div",{className:"p-4 bg-muted rounded-md",children:(0,i.jsxs)("p",{className:"text-sm",children:[(0,i.jsx)("strong",{children:"Note:"})," Essay questions typically require manual grading. Students will see the guidelines and word count limits when answering the question."]})})]})}function f({questionType:e,initialData:s,onSubmit:t,isSaving:n,submitLabel:l}){let[o,d]=(0,r.useState)(s?.text?"string"==typeof s.text?s.text:JSON.parse(s.text).default||"":""),[f,j]=(0,r.useState)(s?.points?.toString()||"1"),[v,g]=(0,r.useState)(s?.feedbackCorrect||""),[N,b]=(0,r.useState)(s?.feedbackIncorrect||""),[y,w]=(0,r.useState)(s?{...s}:{});return(0,i.jsxs)("form",{onSubmit:e=>{e.preventDefault(),t({text:o,points:parseFloat(f),feedbackCorrect:v||null,feedbackIncorrect:N||null,...y})},className:"space-y-6",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)("label",{htmlFor:"questionText",className:"text-sm font-medium",children:"Question Text"}),(0,i.jsx)("textarea",{id:"questionText",value:o,onChange:e=>d(e.target.value),className:"w-full p-2 border rounded-md min-h-[100px]",placeholder:"Enter your question here...",required:!0})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)("label",{htmlFor:"points",className:"text-sm font-medium",children:"Points"}),(0,i.jsx)("input",{id:"points",type:"number",min:"0.5",step:"0.5",value:f,onChange:e=>j(e.target.value),className:"w-full p-2 border rounded-md",required:!0})]}),(()=>{switch(e){case"multiple_choice":return(0,i.jsx)(c,{data:y,onChange:w});case"true_false":return(0,i.jsx)(u,{data:y,onChange:w});case"short_answer":return(0,i.jsx)(m,{data:y,onChange:w});case"matching":return(0,i.jsx)(h,{data:y,onChange:w});case"fill_in_the_blank":return(0,i.jsx)(x,{data:y,onChange:w});case"essay":return(0,i.jsx)(p,{data:y,onChange:w});default:return(0,i.jsxs)("p",{children:["Unsupported question type: ",e]})}})(),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)("label",{htmlFor:"feedbackCorrect",className:"text-sm font-medium",children:"Feedback for Correct Answer"}),(0,i.jsx)("textarea",{id:"feedbackCorrect",value:v,onChange:e=>g(e.target.value),className:"w-full p-2 border rounded-md",placeholder:"Feedback to show when the answer is correct"})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)("label",{htmlFor:"feedbackIncorrect",className:"text-sm font-medium",children:"Feedback for Incorrect Answer"}),(0,i.jsx)("textarea",{id:"feedbackIncorrect",value:N,onChange:e=>b(e.target.value),className:"w-full p-2 border rounded-md",placeholder:"Feedback to show when the answer is incorrect"})]}),(0,i.jsx)("div",{className:"flex justify-end pt-4",children:(0,i.jsx)(a.$,{type:"submit",disabled:n,children:n?"Saving...":l})})]})}function j({questions:e,onAddQuestion:s,onUpdateQuestion:t,onDeleteQuestion:n,isSaving:l}){let[o,c]=(0,r.useState)("existing"),[u,m]=(0,r.useState)(null),[h,x]=(0,r.useState)("multiple_choice"),p=e=>{confirm("Are you sure you want to delete this question?")&&(n(e),u===e&&m(null))},j=u?e.find(e=>e.id===u):null;return(0,i.jsx)("div",{className:"space-y-6",children:(0,i.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:o,onValueChange:c,children:[(0,i.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"grid grid-cols-2 w-full max-w-md",children:[(0,i.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"existing",children:"Existing Questions"}),(0,i.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"add",children:"Add Question"})]}),(0,i.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"existing",children:(0,i.jsx)("div",{className:"space-y-6",children:0===e.length?(0,i.jsxs)("div",{className:"text-center py-8",children:[(0,i.jsx)("p",{className:"text-muted-foreground mb-4",children:"No questions yet. Add your first question to get started."}),(0,i.jsx)(a.$,{onClick:()=>c("add"),children:"Add First Question"})]}):(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{className:"border rounded-md p-4 h-[500px] overflow-y-auto",children:[(0,i.jsxs)("h3",{className:"font-medium mb-4",children:["Questions (",e.length,")"]}),(0,i.jsx)("ul",{className:"space-y-2",children:e.map(e=>(0,i.jsx)("li",{className:`p-3 border rounded-md cursor-pointer transition-colors ${u===e.id?"border-primary bg-primary/5":"hover:border-primary/50"}`,onClick:()=>m(e.id),children:(0,i.jsxs)("div",{className:"flex justify-between items-start",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"font-medium line-clamp-1",children:"string"==typeof e.text?e.text:JSON.parse(e.text).default||"Question"}),(0,i.jsxs)("p",{className:"text-xs text-muted-foreground",children:[e.type," • ",e.points," points"]})]}),(0,i.jsxs)(a.$,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0",onClick:s=>{s.stopPropagation(),p(e.id)},children:[(0,i.jsx)("span",{className:"sr-only",children:"Delete"}),(0,i.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,i.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})]})]})},e.id))})]}),(0,i.jsx)("div",{className:"border rounded-md p-4 h-[500px] overflow-y-auto",children:j?(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"font-medium mb-4",children:"Edit Question"}),(0,i.jsx)(f,{questionType:j.type,initialData:j,onSubmit:e=>{u&&(t(u,e),m(null))},isSaving:l,submitLabel:"Update Question"})]}):(0,i.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,i.jsx)("p",{className:"text-muted-foreground",children:"Select a question to edit"})})})]})})}),(0,i.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"add",children:(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)("label",{htmlFor:"questionType",className:"text-sm font-medium",children:"Question Type"}),(0,i.jsxs)("select",{id:"questionType",value:h,onChange:e=>x(e.target.value),className:"w-full p-2 border rounded-md",children:[(0,i.jsx)("option",{value:"multiple_choice",children:"Multiple Choice"}),(0,i.jsx)("option",{value:"true_false",children:"True/False"}),(0,i.jsx)("option",{value:"short_answer",children:"Short Answer"}),(0,i.jsx)("option",{value:"matching",children:"Matching"}),(0,i.jsx)("option",{value:"fill_in_the_blank",children:"Fill in the Blank"}),(0,i.jsx)("option",{value:"essay",children:"Essay"})]})]}),(0,i.jsxs)("div",{className:"border rounded-md p-4",children:[(0,i.jsx)("h3",{className:"font-medium mb-4",children:"Add New Question"}),(0,i.jsx)(f,{questionType:h,onSubmit:e=>{s({questionId:(0,d.lk)(),type:h,...e}),c("existing")},isSaving:l,submitLabel:"Add Question"})]})]})})]})})}function v({quiz:e,setQuiz:s,isSaving:t}){let[n,l]=(0,r.useState)("pools"),[o,c]=(0,r.useState)(null),[u,m]=(0,r.useState)(""),[h,x]=(0,r.useState)(""),[p,f]=(0,r.useState)(null),[j,v]=(0,r.useState)(!1),[g,N]=(0,r.useState)(null),[b,y]=(0,r.useState)(""),[w,C]=(0,r.useState)("1"),[k,q]=(0,r.useState)(!0),[S,O]=(0,r.useState)(!1),[_,E]=(0,r.useState)(null),[z,P]=(0,r.useState)(!1),[D,F]=(0,r.useState)([]),[T,$]=(0,r.useState)([]),L=async s=>{c(s),N(null);let t=e.questionPools.find(e=>e.id===s);if(t){f({id:t.id,title:t.title||"",description:t.description||""}),$(t.questions);let s=e.questionPools.flatMap(e=>e.questions.map(e=>e.id));F(e.questions.filter(e=>!s.includes(e.id)))}},U=s=>{N(s),c(null);let t=e.selectionRules.find(e=>e.id===s);t&&E({id:t.id,poolId:t.poolId,selectCount:t.selectCount,randomize:t.randomize,shuffleOrder:t.shuffleOrder})},A=async()=>{v(!0);try{let t=await fetch(`/api/quizzes/${e.id}/pools`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({poolId:(0,d.lk)(),title:u,description:h})});if(!t.ok)throw Error("Failed to create pool");let i=await t.json();s(e=>({...e,questionPools:[...e.questionPools,{...i,questions:[]}]})),m(""),x(""),l("pools")}catch(e){console.error("Error creating pool:",e)}finally{v(!1)}},M=async()=>{if(p){v(!0);try{let t=await fetch(`/api/quizzes/${e.id}/pools/${p.id}`,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({title:p.title,description:p.description})});if(!t.ok)throw Error("Failed to update pool");let i=await t.json();s(e=>({...e,questionPools:e.questionPools.map(e=>e.id===p.id?{...e,...i}:e)}))}catch(e){console.error("Error updating pool:",e)}finally{v(!1)}}},R=async t=>{if(confirm("Are you sure you want to delete this pool? This will also delete any selection rules that use this pool."))try{if(!(await fetch(`/api/quizzes/${e.id}/pools/${t}`,{method:"DELETE"})).ok)throw Error("Failed to delete pool");s(e=>({...e,questionPools:e.questionPools.filter(e=>e.id!==t),selectionRules:e.selectionRules.filter(e=>e.poolId!==t)})),o===t&&(c(null),f(null))}catch(e){console.error("Error deleting pool:",e)}},Q=async()=>{P(!0);try{let t=await fetch(`/api/quizzes/${e.id}/rules`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({poolId:b,selectCount:parseInt(w),randomize:k,shuffleOrder:S})});if(!t.ok)throw Error("Failed to create rule");let i=await t.json();s(e=>({...e,selectionRules:[...e.selectionRules,i]})),y(""),C("1"),q(!0),O(!1),l("rules")}catch(e){console.error("Error creating rule:",e)}finally{P(!1)}},I=async()=>{if(_){P(!0);try{let t=await fetch(`/api/quizzes/${e.id}/rules/${_.id}`,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({poolId:_.poolId,selectCount:_.selectCount,randomize:_.randomize,shuffleOrder:_.shuffleOrder})});if(!t.ok)throw Error("Failed to update rule");let i=await t.json();s(e=>({...e,selectionRules:e.selectionRules.map(e=>e.id===_.id?{...e,...i}:e)}))}catch(e){console.error("Error updating rule:",e)}finally{P(!1)}}},B=async t=>{if(confirm("Are you sure you want to delete this selection rule?"))try{if(!(await fetch(`/api/quizzes/${e.id}/rules/${t}`,{method:"DELETE"})).ok)throw Error("Failed to delete rule");s(e=>({...e,selectionRules:e.selectionRules.filter(e=>e.id!==t)})),g===t&&(N(null),E(null))}catch(e){console.error("Error deleting rule:",e)}};return(0,i.jsx)("div",{className:"space-y-6",children:(0,i.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:n,onValueChange:l,children:[(0,i.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"grid grid-cols-3 w-full max-w-md",children:[(0,i.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"pools",children:"Question Pools"}),(0,i.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"rules",children:"Selection Rules"}),(0,i.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"create",children:"Create New"})]}),(0,i.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"pools",children:(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{className:"border rounded-md p-4 h-[500px] overflow-y-auto",children:[(0,i.jsxs)("h3",{className:"font-medium mb-4",children:["Pools (",e.questionPools.length,")"]}),0===e.questionPools.length?(0,i.jsxs)("div",{className:"text-center py-8",children:[(0,i.jsx)("p",{className:"text-muted-foreground mb-4",children:"No question pools yet. Create your first pool to get started."}),(0,i.jsx)(a.$,{onClick:()=>l("create"),children:"Create First Pool"})]}):(0,i.jsx)("ul",{className:"space-y-2",children:e.questionPools.map(e=>(0,i.jsx)("li",{className:`p-3 border rounded-md cursor-pointer transition-colors ${o===e.id?"border-primary bg-primary/5":"hover:border-primary/50"}`,onClick:()=>L(e.id),children:(0,i.jsxs)("div",{className:"flex justify-between items-start",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"font-medium",children:e.title||`Pool ${e.poolId}`}),(0,i.jsxs)("p",{className:"text-xs text-muted-foreground",children:[e.questions.length," questions"]})]}),(0,i.jsxs)(a.$,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0",onClick:s=>{s.stopPropagation(),R(e.id)},children:[(0,i.jsx)("span",{className:"sr-only",children:"Delete"}),(0,i.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,i.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})]})]})},e.id))})]}),(0,i.jsx)("div",{className:"border rounded-md p-4 h-[500px] overflow-y-auto",children:o&&p?(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsx)("h3",{className:"font-medium",children:"Edit Pool"}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)("label",{htmlFor:"poolTitle",className:"text-sm font-medium",children:"Pool Title"}),(0,i.jsx)("input",{id:"poolTitle",type:"text",value:p.title,onChange:e=>f({...p,title:e.target.value}),className:"w-full p-2 border rounded-md",placeholder:"Enter pool title"})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)("label",{htmlFor:"poolDescription",className:"text-sm font-medium",children:"Description"}),(0,i.jsx)("textarea",{id:"poolDescription",value:p.description,onChange:e=>f({...p,description:e.target.value}),className:"w-full p-2 border rounded-md",placeholder:"Enter pool description"})]}),(0,i.jsx)("div",{className:"flex justify-end",children:(0,i.jsx)(a.$,{onClick:M,disabled:j,children:j?"Saving...":"Save Pool"})}),(0,i.jsx)("div",{className:"pt-4 border-t mt-4",children:(0,i.jsx)("h4",{className:"font-medium mb-2",children:"Questions in this Pool"})})]}):(0,i.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,i.jsx)("p",{className:"text-muted-foreground",children:"Select a pool to edit"})})})]})}),(0,i.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"rules",children:(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{className:"border rounded-md p-4 h-[500px] overflow-y-auto",children:[(0,i.jsxs)("h3",{className:"font-medium mb-4",children:["Selection Rules (",e.selectionRules.length,")"]}),0===e.selectionRules.length?(0,i.jsxs)("div",{className:"text-center py-8",children:[(0,i.jsx)("p",{className:"text-muted-foreground mb-4",children:"No selection rules yet. Create your first rule to get started."}),(0,i.jsx)(a.$,{onClick:()=>l("create"),children:"Create First Rule"})]}):(0,i.jsx)("ul",{className:"space-y-2",children:e.selectionRules.map(s=>{let t=e.questionPools.find(e=>e.id===s.poolId);return(0,i.jsx)("li",{className:`p-3 border rounded-md cursor-pointer transition-colors ${g===s.id?"border-primary bg-primary/5":"hover:border-primary/50"}`,onClick:()=>U(s.id),children:(0,i.jsxs)("div",{className:"flex justify-between items-start",children:[(0,i.jsxs)("div",{children:[(0,i.jsxs)("p",{className:"font-medium",children:["Select ",s.selectCount," from ",t?.title||s.poolId]}),(0,i.jsxs)("p",{className:"text-xs text-muted-foreground",children:[s.randomize?"Random selection":"Sequential selection",s.shuffleOrder?", shuffled order":""]})]}),(0,i.jsxs)(a.$,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0",onClick:e=>{e.stopPropagation(),B(s.id)},children:[(0,i.jsx)("span",{className:"sr-only",children:"Delete"}),(0,i.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,i.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})]})]})},s.id)})})]}),(0,i.jsx)("div",{className:"border rounded-md p-4 h-[500px] overflow-y-auto",children:g&&_?(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsx)("h3",{className:"font-medium",children:"Edit Selection Rule"}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)("label",{htmlFor:"rulePool",className:"text-sm font-medium",children:"Question Pool"}),(0,i.jsxs)("select",{id:"rulePool",value:_.poolId,onChange:e=>E({..._,poolId:e.target.value}),className:"w-full p-2 border rounded-md",required:!0,children:[(0,i.jsx)("option",{value:"",children:"-- Select a pool --"}),e.questionPools.map(e=>(0,i.jsxs)("option",{value:e.id,children:[e.title||`Pool ${e.poolId}`," (",e.questions.length," questions)"]},e.id))]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)("label",{htmlFor:"ruleSelectCount",className:"text-sm font-medium",children:"Number of Questions to Select"}),(0,i.jsx)("input",{id:"ruleSelectCount",type:"number",min:"1",value:_.selectCount,onChange:e=>E({..._,selectCount:parseInt(e.target.value)}),className:"w-full p-2 border rounded-md",required:!0})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,i.jsx)("input",{type:"checkbox",checked:_.randomize,onChange:e=>E({..._,randomize:e.target.checked})}),(0,i.jsx)("span",{children:"Randomize Selection"})]}),(0,i.jsx)("p",{className:"text-xs text-muted-foreground ml-6",children:"If checked, questions will be randomly selected from the pool."})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,i.jsx)("input",{type:"checkbox",checked:_.shuffleOrder,onChange:e=>E({..._,shuffleOrder:e.target.checked})}),(0,i.jsx)("span",{children:"Shuffle Order"})]}),(0,i.jsx)("p",{className:"text-xs text-muted-foreground ml-6",children:"If checked, the order of selected questions will be randomized."})]}),(0,i.jsx)("div",{className:"flex justify-end",children:(0,i.jsx)(a.$,{onClick:I,disabled:z,children:z?"Saving...":"Save Rule"})})]}):(0,i.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,i.jsx)("p",{className:"text-muted-foreground",children:"Select a rule to edit"})})})]})}),(0,i.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"create",children:(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,i.jsxs)("div",{className:"border rounded-md p-4",children:[(0,i.jsx)("h3",{className:"font-medium mb-4",children:"Create New Pool"}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)("label",{htmlFor:"newPoolTitle",className:"text-sm font-medium",children:"Pool Title"}),(0,i.jsx)("input",{id:"newPoolTitle",type:"text",value:u,onChange:e=>m(e.target.value),className:"w-full p-2 border rounded-md",placeholder:"Enter pool title",required:!0})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)("label",{htmlFor:"newPoolDescription",className:"text-sm font-medium",children:"Description"}),(0,i.jsx)("textarea",{id:"newPoolDescription",value:h,onChange:e=>x(e.target.value),className:"w-full p-2 border rounded-md",placeholder:"Enter pool description (optional)"})]}),(0,i.jsx)("div",{className:"flex justify-end",children:(0,i.jsx)(a.$,{onClick:A,disabled:!u||j,children:j?"Creating...":"Create Pool"})})]})]}),(0,i.jsxs)("div",{className:"border rounded-md p-4",children:[(0,i.jsx)("h3",{className:"font-medium mb-4",children:"Create New Selection Rule"}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)("label",{htmlFor:"newRulePool",className:"text-sm font-medium",children:"Question Pool"}),(0,i.jsxs)("select",{id:"newRulePool",value:b,onChange:e=>y(e.target.value),className:"w-full p-2 border rounded-md",required:!0,children:[(0,i.jsx)("option",{value:"",children:"-- Select a pool --"}),e.questionPools.map(e=>(0,i.jsxs)("option",{value:e.id,children:[e.title||`Pool ${e.poolId}`," (",e.questions.length," questions)"]},e.id))]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)("label",{htmlFor:"newRuleSelectCount",className:"text-sm font-medium",children:"Number of Questions to Select"}),(0,i.jsx)("input",{id:"newRuleSelectCount",type:"number",min:"1",value:w,onChange:e=>C(e.target.value),className:"w-full p-2 border rounded-md",required:!0})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,i.jsx)("input",{type:"checkbox",checked:k,onChange:e=>q(e.target.checked)}),(0,i.jsx)("span",{children:"Randomize Selection"})]}),(0,i.jsx)("p",{className:"text-xs text-muted-foreground ml-6",children:"If checked, questions will be randomly selected from the pool."})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,i.jsx)("input",{type:"checkbox",checked:S,onChange:e=>O(e.target.checked)}),(0,i.jsx)("span",{children:"Shuffle Order"})]}),(0,i.jsx)("p",{className:"text-xs text-muted-foreground ml-6",children:"If checked, the order of selected questions will be randomized."})]}),(0,i.jsx)("div",{className:"flex justify-end",children:(0,i.jsx)(a.$,{onClick:Q,disabled:!b||!w||z,children:z?"Creating...":"Create Rule"})})]})]})]})})]})})}(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}();var g=t(28531),N=t.n(g);function b({quiz:e,onPublishStatusChange:s,isSaving:t}){let[n,l]=(0,r.useState)(!1);return(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)("div",{className:"p-4 border rounded-md bg-muted/30",children:[(0,i.jsx)("h3",{className:"font-medium mb-2",children:"Current Status"}),(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)("div",{className:`w-3 h-3 rounded-full ${e.isPublished?"bg-green-500":"bg-amber-500"}`}),(0,i.jsx)("p",{children:e.isPublished?"Published - This quiz is visible to others":"Draft - Only you can see this quiz"})]})]}),n?(0,i.jsxs)("div",{className:"p-4 border rounded-md bg-primary/5",children:[(0,i.jsx)("h3",{className:"font-medium mb-2",children:"Publish Confirmation"}),(0,i.jsx)("p",{className:"mb-4",children:"Are you sure you want to publish this quiz? Once published, it will be visible to others."}),(0,i.jsxs)("div",{className:"flex space-x-4",children:[(0,i.jsx)(a.$,{onClick:()=>{s(!0),l(!1)},disabled:t,children:t?"Publishing...":"Yes, Publish Quiz"}),(0,i.jsx)(a.$,{variant:"outline",onClick:()=>{l(!1)},disabled:t,children:"Cancel"})]})]}):(0,i.jsxs)("div",{className:"flex space-x-4",children:[(0,i.jsx)(a.$,{onClick:()=>{e.isPublished?s(!1):l(!0)},variant:e.isPublished?"outline":"default",disabled:t,children:t?e.isPublished?"Unpublishing...":"Publishing...":e.isPublished?"Unpublish Quiz":"Publish Quiz"}),e.isPublished&&(0,i.jsx)(a.$,{asChild:!0,children:(0,i.jsx)(N(),{href:`/quiz/${e.id}`,target:"_blank",children:"View Published Quiz"})})]}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsx)("h3",{className:"font-medium",children:"Publishing Checklist"}),(0,i.jsxs)("ul",{className:"space-y-2",children:[(0,i.jsxs)("li",{className:"flex items-start space-x-2",children:[(0,i.jsx)("div",{className:`mt-1 w-5 h-5 flex items-center justify-center rounded-full ${e.title?"bg-green-100 text-green-600":"bg-amber-100 text-amber-600"}`,children:e.title?"✓":"!"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"font-medium",children:"Quiz Title"}),(0,i.jsx)("p",{className:"text-sm text-muted-foreground",children:e.title?"Title is set":"Quiz needs a title"})]})]}),(0,i.jsxs)("li",{className:"flex items-start space-x-2",children:[(0,i.jsx)("div",{className:`mt-1 w-5 h-5 flex items-center justify-center rounded-full ${e.questions.length>0?"bg-green-100 text-green-600":"bg-amber-100 text-amber-600"}`,children:e.questions.length>0?"✓":"!"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"font-medium",children:"Questions"}),(0,i.jsx)("p",{className:"text-sm text-muted-foreground",children:e.questions.length>0?`Quiz has ${e.questions.length} question(s)`:"Quiz needs at least one question"})]})]}),(0,i.jsxs)("li",{className:"flex items-start space-x-2",children:[(0,i.jsx)("div",{className:`mt-1 w-5 h-5 flex items-center justify-center rounded-full ${null!==e.passingScore?"bg-green-100 text-green-600":"bg-amber-100 text-amber-600"}`,children:null!==e.passingScore?"✓":"!"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"font-medium",children:"Passing Score"}),(0,i.jsx)("p",{className:"text-sm text-muted-foreground",children:null!==e.passingScore?`Passing score is set to ${e.passingScore}%`:"Consider setting a passing score"})]})]}),(0,i.jsxs)("li",{className:"flex items-start space-x-2",children:[(0,i.jsx)("div",{className:`mt-1 w-5 h-5 flex items-center justify-center rounded-full ${null!==e.timeLimit?"bg-green-100 text-green-600":"bg-amber-100 text-amber-600"}`,children:null!==e.timeLimit?"✓":"!"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"font-medium",children:"Time Limit"}),(0,i.jsx)("p",{className:"text-sm text-muted-foreground",children:null!==e.timeLimit?`Time limit is set to ${e.timeLimit} minutes`:"Consider setting a time limit"})]})]})]})]})]})}function y({quiz:e}){let s=(0,n.useRouter)(),[t,d]=(0,r.useState)(e),[c,u]=(0,r.useState)("details"),[m,h]=(0,r.useState)(!1),[x,p]=(0,r.useState)(null),f=async e=>{h(!0),p(null);try{let s=await fetch(`/api/quizzes/${t.id}`,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!s.ok)throw Error("Failed to update quiz details");let i=await s.json();d(e=>({...e,...i})),p("Quiz details saved successfully")}catch(e){console.error("Error saving quiz details:",e),p("Error saving quiz details")}finally{h(!1)}},g=async e=>{h(!0),p(null);try{let s=await fetch(`/api/quizzes/${t.id}/questions`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!s.ok)throw Error("Failed to add question");let i=await s.json();d(e=>({...e,questions:[...e.questions,i]})),p("Question added successfully")}catch(e){console.error("Error adding question:",e),p("Error adding question")}finally{h(!1)}},N=async(e,s)=>{h(!0),p(null);try{let i=await fetch(`/api/quizzes/${t.id}/questions/${e}`,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify(s)});if(!i.ok)throw Error("Failed to update question");let r=await i.json();d(s=>({...s,questions:s.questions.map(s=>s.id===e?{...s,...r}:s)})),p("Question updated successfully")}catch(e){console.error("Error updating question:",e),p("Error updating question")}finally{h(!1)}},y=async e=>{h(!0),p(null);try{if(!(await fetch(`/api/quizzes/${t.id}/questions/${e}`,{method:"DELETE"})).ok)throw Error("Failed to delete question");d(s=>({...s,questions:s.questions.filter(s=>s.id!==e)})),p("Question deleted successfully")}catch(e){console.error("Error deleting question:",e),p("Error deleting question")}finally{h(!1)}},w=async e=>{h(!0),p(null);try{if(!(await fetch(`/api/quizzes/${t.id}/publish`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({isPublished:e})})).ok)throw Error(`Failed to ${e?"publish":"unpublish"} quiz`);d(s=>({...s,isPublished:e})),p(`Quiz ${e?"published":"unpublished"} successfully`)}catch(s){console.error(`Error ${e?"publishing":"unpublishing"} quiz:`,s),p(`Error ${e?"publishing":"unpublishing"} quiz`)}finally{h(!1)}};return(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)("div",{className:"flex justify-between items-center",children:[(0,i.jsx)("h1",{className:"text-3xl font-bold",children:"Edit Quiz"}),(0,i.jsxs)("div",{className:"flex items-center gap-4",children:[x&&(0,i.jsx)("p",{className:`text-sm ${x.includes("Error")?"text-red-500":"text-green-500"}`,children:x}),(0,i.jsx)(a.$,{variant:"outline",onClick:()=>s.push("/dashboard/quizzes"),children:"Back to Quizzes"}),(0,i.jsx)(a.$,{onClick:()=>s.push(`/dashboard/quizzes/${t.id}/preview`),children:"Preview Quiz"})]})]}),(0,i.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:c,onValueChange:u,children:[(0,i.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"grid grid-cols-4 w-full max-w-3xl",children:[(0,i.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"details",children:"Quiz Details"}),(0,i.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"questions",children:"Questions"}),(0,i.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"pools",children:"Question Pools"}),(0,i.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"publish",children:"Publish"})]}),(0,i.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"details",children:(0,i.jsxs)(l.Zp,{children:[(0,i.jsxs)(l.aR,{children:[(0,i.jsx)(l.ZB,{children:"Quiz Details"}),(0,i.jsx)(l.BT,{children:"Edit the basic information for your quiz"})]}),(0,i.jsx)(l.Wu,{children:(0,i.jsx)(o,{quiz:t,onSave:f,isSaving:m})})]})}),(0,i.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"questions",children:(0,i.jsxs)(l.Zp,{children:[(0,i.jsxs)(l.aR,{children:[(0,i.jsx)(l.ZB,{children:"Questions"}),(0,i.jsx)(l.BT,{children:"Add, edit, or remove questions from your quiz"})]}),(0,i.jsx)(l.Wu,{children:(0,i.jsx)(j,{questions:t.questions,onAddQuestion:g,onUpdateQuestion:N,onDeleteQuestion:y,isSaving:m})})]})}),(0,i.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"pools",children:(0,i.jsxs)(l.Zp,{children:[(0,i.jsxs)(l.aR,{children:[(0,i.jsx)(l.ZB,{children:"Question Pools"}),(0,i.jsx)(l.BT,{children:"Create pools of questions for dynamic selection"})]}),(0,i.jsx)(l.Wu,{children:(0,i.jsx)(v,{quiz:t,setQuiz:d,isSaving:m})})]})}),(0,i.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"publish",children:(0,i.jsxs)(l.Zp,{children:[(0,i.jsxs)(l.aR,{children:[(0,i.jsx)(l.ZB,{children:"Publish Settings"}),(0,i.jsx)(l.BT,{children:"Control the visibility and availability of your quiz"})]}),(0,i.jsx)(l.Wu,{children:(0,i.jsx)(b,{quiz:t,onPublishStatusChange:w,isSaving:m})})]})})]})]})}!function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()},39400:(e,s,t)=>{"use strict";t.d(s,{$:()=>d});var i=t(45512),r=t(58009),n=t(89383),a=t(21643),l=t(96720);let o=(0,a.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=r.forwardRef(({className:e,variant:s,size:t,asChild:r=!1,...a},d)=>{let c=r?n.DX:"button";return(0,i.jsx)(c,{className:(0,l.cn)(o({variant:s,size:t,className:e})),ref:d,...a})});d.displayName="Button"},64590:(e,s,t)=>{"use strict";t.d(s,{BT:()=>d,Wu:()=>c,ZB:()=>o,Zp:()=>a,aR:()=>l,wL:()=>u});var i=t(45512),r=t(58009),n=t(96720);let a=r.forwardRef(({className:e,...s},t)=>(0,i.jsx)("div",{ref:t,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...s}));a.displayName="Card";let l=r.forwardRef(({className:e,...s},t)=>(0,i.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",e),...s}));l.displayName="CardHeader";let o=r.forwardRef(({className:e,...s},t)=>(0,i.jsx)("h3",{ref:t,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",e),...s}));o.displayName="CardTitle";let d=r.forwardRef(({className:e,...s},t)=>(0,i.jsx)("p",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",e),...s}));d.displayName="CardDescription";let c=r.forwardRef(({className:e,...s},t)=>(0,i.jsx)("div",{ref:t,className:(0,n.cn)("p-6 pt-0",e),...s}));c.displayName="CardContent";let u=r.forwardRef(({className:e,...s},t)=>(0,i.jsx)("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",e),...s}));u.displayName="CardFooter"},96720:(e,s,t)=>{"use strict";t.d(s,{Yq:()=>l,cn:()=>n,lk:()=>a});var i=t(82281),r=t(94805);function n(...e){return(0,r.QP)((0,i.$)(e))}function a(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){let s=16*Math.random()|0;return("x"===e?s:3&s|8).toString(16)})}function l(e){return("string"==typeof e?new Date(e):e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})}},33405:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var i=t(62740),r=t(26996);function n({children:e}){return(0,i.jsxs)("div",{className:"flex min-h-screen flex-col",children:[(0,i.jsx)(r.default,{}),(0,i.jsx)("main",{className:"flex-1",children:e})]})}},54363:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>d});var i=t(62740),r=t(31831),n=t(51825),a=t(37702),l=t(27914),o=t(76297);async function d({params:e}){let s=await (0,n.getServerSession)(a.N);s||(0,r.notFound)();let t=await l.db.quiz.findUnique({where:{id:e.id,creatorId:s.user.id},include:{questions:!0,questionPools:{include:{questions:!0}},selectionRules:!0}});return t||(0,r.notFound)(),(0,i.jsx)("div",{className:"container mx-auto py-8 px-4",children:(0,i.jsx)(o.default,{quiz:t})})}},26996:(e,s,t)=>{"use strict";t.d(s,{default:()=>i});let i=(0,t(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/components/dashboard/Navbar.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/components/dashboard/Navbar.tsx","default")},76297:(e,s,t)=>{"use strict";t.d(s,{default:()=>i});let i=(0,t(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/components/quiz/editor/QuizEditor.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/components/quiz/editor/QuizEditor.tsx","default")}};var s=require("../../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),i=s.X(0,[638,263,3,367,920,30],()=>t(20270));module.exports=i})();