(()=>{var e={};e.id=132,e.ids=[132],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},99084:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>l});var s=r(70260),n=r(28203),a=r(25155),i=r.n(a),o=r(67292),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);r.d(t,d);let l=["",{children:["dashboard",{children:["quizzes",{children:["create",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,11211)),"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/app/dashboard/quizzes/create/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,33405)),"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/app/dashboard/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,71354)),"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["/Users/<USER>/Documents/augment-projects/hacking-quiz/src/app/dashboard/quizzes/create/page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/dashboard/quizzes/create/page",pathname:"/dashboard/quizzes/create",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},29214:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,13219,23)),Promise.resolve().then(r.t.bind(r,34863,23)),Promise.resolve().then(r.t.bind(r,25155,23)),Promise.resolve().then(r.t.bind(r,40802,23)),Promise.resolve().then(r.t.bind(r,9350,23)),Promise.resolve().then(r.t.bind(r,48530,23)),Promise.resolve().then(r.t.bind(r,88921,23))},69382:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,66959,23)),Promise.resolve().then(r.t.bind(r,33875,23)),Promise.resolve().then(r.t.bind(r,88903,23)),Promise.resolve().then(r.t.bind(r,57174,23)),Promise.resolve().then(r.t.bind(r,84178,23)),Promise.resolve().then(r.t.bind(r,87190,23)),Promise.resolve().then(r.t.bind(r,61365,23))},41303:(e,t,r)=>{Promise.resolve().then(r.bind(r,28445))},69327:(e,t,r)=>{Promise.resolve().then(r.bind(r,82649))},40677:(e,t,r)=>{Promise.resolve().then(r.bind(r,11211))},53829:(e,t,r)=>{Promise.resolve().then(r.bind(r,74287))},92295:(e,t,r)=>{Promise.resolve().then(r.bind(r,26996))},31615:(e,t,r)=>{Promise.resolve().then(r.bind(r,44848))},74287:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var s=r(45512),n=r(58009),a=r(79334),i=r(90993),o=r(39400),d=r(64590),l=r(96720);function c(){let e=(0,a.useRouter)(),{data:t}=(0,i.useSession)(),[r,c]=(0,n.useState)(""),[u,m]=(0,n.useState)(""),[p,x]=(0,n.useState)(""),[h,f]=(0,n.useState)(70),[v,g]=(0,n.useState)(15),[b,y]=(0,n.useState)(!1),[j,z]=(0,n.useState)(null),N=async s=>{if(s.preventDefault(),y(!0),z(null),!t){z("You must be logged in to create a quiz"),y(!1);return}try{let t={quizId:(0,l.lk)(),title:r,description:u,tags:p.split(",").map(e=>e.trim()).filter(e=>e),passingScore:parseFloat(h.toString()),timeLimit:parseInt(v.toString())},s=await fetch("/api/quizzes",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)}),n=await s.json();if(!s.ok)throw Error(n.message||"Failed to create quiz");e.push(`/dashboard/quizzes/${n.id}/edit`)}catch(e){e instanceof Error?z(e.message):z("An error occurred while creating the quiz"),y(!1)}};return(0,s.jsx)("div",{className:"container mx-auto py-8 px-4",children:(0,s.jsxs)("div",{className:"max-w-3xl mx-auto",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold mb-8",children:"Create New Quiz"}),(0,s.jsxs)(d.Zp,{children:[(0,s.jsxs)(d.aR,{children:[(0,s.jsx)(d.ZB,{children:"Quiz Details"}),(0,s.jsx)(d.BT,{children:"Enter the basic information for your new quiz"})]}),(0,s.jsx)(d.Wu,{children:(0,s.jsxs)("form",{onSubmit:N,className:"space-y-6",children:[j&&(0,s.jsx)("div",{className:"p-3 text-sm bg-red-50 text-red-500 rounded-md",children:j}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("label",{htmlFor:"title",className:"text-sm font-medium",children:"Quiz Title"}),(0,s.jsx)("input",{id:"title",type:"text",value:r,onChange:e=>c(e.target.value),className:"w-full p-2 border rounded-md",placeholder:"Enter quiz title",required:!0})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("label",{htmlFor:"description",className:"text-sm font-medium",children:"Description"}),(0,s.jsx)("textarea",{id:"description",value:u,onChange:e=>m(e.target.value),className:"w-full p-2 border rounded-md min-h-[100px]",placeholder:"Enter quiz description"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("label",{htmlFor:"tags",className:"text-sm font-medium",children:"Tags"}),(0,s.jsx)("input",{id:"tags",type:"text",value:p,onChange:e=>x(e.target.value),className:"w-full p-2 border rounded-md",placeholder:"Enter tags separated by commas (e.g., security, basics, networking)"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("label",{htmlFor:"passingScore",className:"text-sm font-medium",children:"Passing Score (%)"}),(0,s.jsx)("input",{id:"passingScore",type:"number",min:"0",max:"100",value:h,onChange:e=>f(parseInt(e.target.value)),className:"w-full p-2 border rounded-md"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("label",{htmlFor:"timeLimit",className:"text-sm font-medium",children:"Time Limit (minutes)"}),(0,s.jsx)("input",{id:"timeLimit",type:"number",min:"1",value:v,onChange:e=>g(parseInt(e.target.value)),className:"w-full p-2 border rounded-md"})]})]}),(0,s.jsxs)("div",{className:"flex justify-end space-x-4 pt-4",children:[(0,s.jsx)(o.$,{type:"button",variant:"outline",onClick:()=>e.back(),disabled:b,children:"Cancel"}),(0,s.jsx)(o.$,{type:"submit",disabled:b,children:b?"Creating...":"Create Quiz"})]})]})})]})]})})}},82649:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});var s=r(45512),n=r(90993);function a({children:e}){return(0,s.jsx)(n.SessionProvider,{children:e})}},44848:(e,t,r)=>{"use strict";r.d(t,{default:()=>l});var s=r(45512),n=r(28531),a=r.n(n),i=r(79334),o=r(90993),d=r(39400);function l(){let e=(0,i.usePathname)(),{data:t}=(0,o.useSession)(),r=t=>e===t||e.startsWith(`${t}/`);return(0,s.jsx)("header",{className:"border-b",children:(0,s.jsxs)("div",{className:"container mx-auto px-4 flex h-16 items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-6",children:[(0,s.jsx)(a(),{href:"/",className:"text-xl font-bold",children:"QuizFlow"}),(0,s.jsxs)("nav",{className:"hidden md:flex gap-6",children:[(0,s.jsx)(a(),{href:"/dashboard",className:`text-sm font-medium transition-colors hover:text-primary ${!r("/dashboard")||r("/dashboard/quizzes")||r("/dashboard/activity")?"text-muted-foreground":"text-primary"}`,children:"Dashboard"}),(0,s.jsx)(a(),{href:"/dashboard/quizzes",className:`text-sm font-medium transition-colors hover:text-primary ${r("/dashboard/quizzes")?"text-primary":"text-muted-foreground"}`,children:"My Quizzes"}),(0,s.jsx)(a(),{href:"/dashboard/activity",className:`text-sm font-medium transition-colors hover:text-primary ${r("/dashboard/activity")?"text-primary":"text-muted-foreground"}`,children:"Activity"}),(0,s.jsx)(a(),{href:"/explore",className:`text-sm font-medium transition-colors hover:text-primary ${r("/explore")?"text-primary":"text-muted-foreground"}`,children:"Explore"})]})]}),(0,s.jsx)("div",{className:"flex items-center gap-4",children:t?(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsxs)("div",{className:"hidden md:block",children:[(0,s.jsx)("div",{className:"text-sm font-medium",children:t.user.name}),(0,s.jsx)("div",{className:"text-xs text-muted-foreground",children:t.user.email})]}),(0,s.jsx)(d.$,{variant:"outline",size:"sm",onClick:()=>(0,o.signOut)({callbackUrl:"/"}),children:"Sign Out"})]}):(0,s.jsx)(d.$,{asChild:!0,size:"sm",children:(0,s.jsx)(a(),{href:"/auth/login",children:"Sign In"})})})]})})}},39400:(e,t,r)=>{"use strict";r.d(t,{$:()=>l});var s=r(45512),n=r(58009),a=r(89383),i=r(21643),o=r(96720);let d=(0,i.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),l=n.forwardRef(({className:e,variant:t,size:r,asChild:n=!1,...i},l)=>{let c=n?a.DX:"button";return(0,s.jsx)(c,{className:(0,o.cn)(d({variant:t,size:r,className:e})),ref:l,...i})});l.displayName="Button"},64590:(e,t,r)=>{"use strict";r.d(t,{BT:()=>l,Wu:()=>c,ZB:()=>d,Zp:()=>i,aR:()=>o,wL:()=>u});var s=r(45512),n=r(58009),a=r(96720);let i=n.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,a.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));i.displayName="Card";let o=n.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,a.cn)("flex flex-col space-y-1.5 p-6",e),...t}));o.displayName="CardHeader";let d=n.forwardRef(({className:e,...t},r)=>(0,s.jsx)("h3",{ref:r,className:(0,a.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));d.displayName="CardTitle";let l=n.forwardRef(({className:e,...t},r)=>(0,s.jsx)("p",{ref:r,className:(0,a.cn)("text-sm text-muted-foreground",e),...t}));l.displayName="CardDescription";let c=n.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,a.cn)("p-6 pt-0",e),...t}));c.displayName="CardContent";let u=n.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,a.cn)("flex items-center p-6 pt-0",e),...t}));u.displayName="CardFooter"},96720:(e,t,r)=>{"use strict";r.d(t,{Yq:()=>o,cn:()=>a,lk:()=>i});var s=r(82281),n=r(94805);function a(...e){return(0,n.QP)((0,s.$)(e))}function i(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){let t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)})}function o(e){return("string"==typeof e?new Date(e):e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})}},37301:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createDedupedByCallsiteServerErrorLoggerDev",{enumerable:!0,get:function(){return d}});let s=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=n(void 0);if(r&&r.has(e))return r.get(e);var s={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&Object.prototype.hasOwnProperty.call(e,i)){var o=a?Object.getOwnPropertyDescriptor(e,i):null;o&&(o.get||o.set)?Object.defineProperty(s,i,o):s[i]=e[i]}return s.default=e,r&&r.set(e,s),s}(r(76301));function n(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(n=function(e){return e?r:t})(e)}let a={current:null},i="function"==typeof s.cache?s.cache:e=>e,o=console.warn;function d(e){return function(...t){o(e(...t))}}i(e=>{try{o(a.current)}finally{a.current=null}})},33405:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(62740),n=r(26996);function a({children:e}){return(0,s.jsxs)("div",{className:"flex min-h-screen flex-col",children:[(0,s.jsx)(n.default,{}),(0,s.jsx)("main",{className:"flex-1",children:e})]})}},11211:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/app/dashboard/quizzes/create/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/app/dashboard/quizzes/create/page.tsx","default")},71354:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c,metadata:()=>l});var s=r(62740),n=r(2202),a=r.n(n),i=r(64988),o=r.n(i);r(61135);var d=r(28445);let l={title:"QuizFlow - Interactive Quiz Ecosystem",description:"A standardized, flexible, and interactive quiz ecosystem for creating and taking quizzes"};function c({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{className:`${a().variable} ${o().variable} antialiased min-h-screen bg-background text-foreground`,children:(0,s.jsx)(d.default,{children:e})})})}},28445:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/components/auth/SessionProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/components/auth/SessionProvider.tsx","default")},26996:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/components/dashboard/Navbar.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/components/dashboard/Navbar.tsx","default")},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(88077);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},61135:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[638,3,367,920],()=>r(99084));module.exports=s})();