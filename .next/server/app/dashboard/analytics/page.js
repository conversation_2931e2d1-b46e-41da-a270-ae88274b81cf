(()=>{var e={};e.id=754,e.ids=[754],e.modules={96330:e=>{"use strict";e.exports=require("@prisma/client")},5486:e=>{"use strict";e.exports=require("bcrypt")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},12412:e=>{"use strict";e.exports=require("assert")},79428:e=>{"use strict";e.exports=require("buffer")},55511:e=>{"use strict";e.exports=require("crypto")},94735:e=>{"use strict";e.exports=require("events")},81630:e=>{"use strict";e.exports=require("http")},55591:e=>{"use strict";e.exports=require("https")},33873:e=>{"use strict";e.exports=require("path")},11723:e=>{"use strict";e.exports=require("querystring")},79551:e=>{"use strict";e.exports=require("url")},28354:e=>{"use strict";e.exports=require("util")},74075:e=>{"use strict";e.exports=require("zlib")},52862:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var r=s(70260),n=s(28203),a=s(25155),i=s.n(a),o=s(67292),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(t,l);let d=["",{children:["dashboard",{children:["analytics",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,46109)),"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/app/dashboard/analytics/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,33405)),"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/app/dashboard/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,71354)),"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["/Users/<USER>/Documents/augment-projects/hacking-quiz/src/app/dashboard/analytics/page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/dashboard/analytics/page",pathname:"/dashboard/analytics",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},32691:(e,t,s)=>{Promise.resolve().then(s.bind(s,74825)),Promise.resolve().then(s.bind(s,73083)),Promise.resolve().then(s.bind(s,98002)),Promise.resolve().then(s.bind(s,23956))},27115:(e,t,s)=>{Promise.resolve().then(s.bind(s,93429)),Promise.resolve().then(s.bind(s,77615)),Promise.resolve().then(s.bind(s,54654)),Promise.resolve().then(s.bind(s,84480))},92295:(e,t,s)=>{Promise.resolve().then(s.bind(s,26996))},31615:(e,t,s)=>{Promise.resolve().then(s.bind(s,44848))},93429:(e,t,s)=>{"use strict";s.d(t,{default:()=>a});var r=s(45512),n=s(64590);function a({totalQuizzes:e,totalQuizzesTaken:t,totalResponsesReceived:s,averageScore:a,completionRate:i}){return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(n.Zp,{children:(0,r.jsx)(n.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Total Quizzes Created"}),(0,r.jsx)("p",{className:"text-3xl font-bold",children:e})]})})}),(0,r.jsx)(n.Zp,{children:(0,r.jsx)(n.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Quizzes Taken"}),(0,r.jsx)("p",{className:"text-3xl font-bold",children:t})]})})}),(0,r.jsx)(n.Zp,{children:(0,r.jsx)(n.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Responses Received"}),(0,r.jsx)("p",{className:"text-3xl font-bold",children:s})]})})}),(0,r.jsx)(n.Zp,{children:(0,r.jsx)(n.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Average Score"}),(0,r.jsxs)("p",{className:"text-3xl font-bold",children:[a.toFixed(1),"%"]})]})})})]})}},77615:(e,t,s)=>{"use strict";s.d(t,{default:()=>a});var r=s(45512),n=s(58009);function a({data:e}){let t=(0,n.useRef)(null);return 0===e.length?(0,r.jsx)("div",{className:"flex items-center justify-center h-[300px] bg-muted/20 rounded-md",children:(0,r.jsx)("p",{className:"text-muted-foreground",children:"No data available"})}):(0,r.jsx)("div",{ref:t,className:"h-[400px]"})}},54654:(e,t,s)=>{"use strict";s.d(t,{default:()=>a});var r=s(45512),n=s(96720);function a({responses:e}){return 0===e.length?(0,r.jsx)("div",{className:"text-center py-8",children:(0,r.jsx)("p",{className:"text-muted-foreground",children:"No recent activity"})}):(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"w-full",children:[(0,r.jsx)("thead",{children:(0,r.jsxs)("tr",{className:"border-b",children:[(0,r.jsx)("th",{className:"text-left py-3 px-4 font-medium",children:"Quiz"}),(0,r.jsx)("th",{className:"text-left py-3 px-4 font-medium",children:"User"}),(0,r.jsx)("th",{className:"text-left py-3 px-4 font-medium",children:"Score"}),(0,r.jsx)("th",{className:"text-left py-3 px-4 font-medium",children:"Time Spent"}),(0,r.jsx)("th",{className:"text-left py-3 px-4 font-medium",children:"Date"}),(0,r.jsx)("th",{className:"text-left py-3 px-4 font-medium",children:"Status"})]})}),(0,r.jsx)("tbody",{children:e.map(e=>(0,r.jsxs)("tr",{className:"border-b hover:bg-muted/50",children:[(0,r.jsx)("td",{className:"py-3 px-4",children:e.quiz.title}),(0,r.jsx)("td",{className:"py-3 px-4",children:e.user?.name||e.user?.email||"Anonymous"}),(0,r.jsxs)("td",{className:"py-3 px-4",children:[e.score.toFixed(1),"%"]}),(0,r.jsx)("td",{className:"py-3 px-4",children:e.timeSpent?function(e){if(e<60)return`${e} sec`;let t=Math.floor(e/60);if(t<60)return`${t} min ${e%60} sec`;let s=Math.floor(t/60);return`${s} hr ${t%60} min`}(e.timeSpent):"N/A"}),(0,r.jsx)("td",{className:"py-3 px-4",children:(0,n.Yq)(e.completedAt||e.startedAt)}),(0,r.jsx)("td",{className:"py-3 px-4",children:(0,r.jsx)("span",{className:`inline-block px-2 py-1 text-xs rounded-full ${e.completedAt?"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100":"bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-100"}`,children:e.completedAt?"Completed":"In Progress"})})]},e.id))})]})})}},84480:(e,t,s)=>{"use strict";s.d(t,{default:()=>a});var r=s(45512),n=s(58009);function a({data:e}){let t=(0,n.useRef)(null);return 0===e.length?(0,r.jsx)("div",{className:"flex items-center justify-center h-[300px] bg-muted/20 rounded-md",children:(0,r.jsx)("p",{className:"text-muted-foreground",children:"No data available"})}):(0,r.jsx)("div",{ref:t,className:"h-[300px]"})}},44848:(e,t,s)=>{"use strict";s.d(t,{default:()=>d});var r=s(45512),n=s(28531),a=s.n(n),i=s(79334),o=s(90993),l=s(39400);function d(){let e=(0,i.usePathname)(),{data:t}=(0,o.useSession)(),s=t=>e===t||e.startsWith(`${t}/`);return(0,r.jsx)("header",{className:"border-b",children:(0,r.jsxs)("div",{className:"container mx-auto px-4 flex h-16 items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-6",children:[(0,r.jsx)(a(),{href:"/",className:"text-xl font-bold",children:"QuizFlow"}),(0,r.jsxs)("nav",{className:"hidden md:flex gap-6",children:[(0,r.jsx)(a(),{href:"/dashboard",className:`text-sm font-medium transition-colors hover:text-primary ${!s("/dashboard")||s("/dashboard/quizzes")||s("/dashboard/activity")?"text-muted-foreground":"text-primary"}`,children:"Dashboard"}),(0,r.jsx)(a(),{href:"/dashboard/quizzes",className:`text-sm font-medium transition-colors hover:text-primary ${s("/dashboard/quizzes")?"text-primary":"text-muted-foreground"}`,children:"My Quizzes"}),(0,r.jsx)(a(),{href:"/dashboard/activity",className:`text-sm font-medium transition-colors hover:text-primary ${s("/dashboard/activity")?"text-primary":"text-muted-foreground"}`,children:"Activity"}),(0,r.jsx)(a(),{href:"/explore",className:`text-sm font-medium transition-colors hover:text-primary ${s("/explore")?"text-primary":"text-muted-foreground"}`,children:"Explore"})]})]}),(0,r.jsx)("div",{className:"flex items-center gap-4",children:t?(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsxs)("div",{className:"hidden md:block",children:[(0,r.jsx)("div",{className:"text-sm font-medium",children:t.user.name}),(0,r.jsx)("div",{className:"text-xs text-muted-foreground",children:t.user.email})]}),(0,r.jsx)(l.$,{variant:"outline",size:"sm",onClick:()=>(0,o.signOut)({callbackUrl:"/"}),children:"Sign Out"})]}):(0,r.jsx)(l.$,{asChild:!0,size:"sm",children:(0,r.jsx)(a(),{href:"/auth/login",children:"Sign In"})})})]})})}},39400:(e,t,s)=>{"use strict";s.d(t,{$:()=>d});var r=s(45512),n=s(58009),a=s(89383),i=s(21643),o=s(96720);let l=(0,i.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=n.forwardRef(({className:e,variant:t,size:s,asChild:n=!1,...i},d)=>{let c=n?a.DX:"button";return(0,r.jsx)(c,{className:(0,o.cn)(l({variant:t,size:s,className:e})),ref:d,...i})});d.displayName="Button"},64590:(e,t,s)=>{"use strict";s.d(t,{BT:()=>d,Wu:()=>c,ZB:()=>l,Zp:()=>i,aR:()=>o,wL:()=>u});var r=s(45512),n=s(58009),a=s(96720);let i=n.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,a.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));i.displayName="Card";let o=n.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,a.cn)("flex flex-col space-y-1.5 p-6",e),...t}));o.displayName="CardHeader";let l=n.forwardRef(({className:e,...t},s)=>(0,r.jsx)("h3",{ref:s,className:(0,a.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));l.displayName="CardTitle";let d=n.forwardRef(({className:e,...t},s)=>(0,r.jsx)("p",{ref:s,className:(0,a.cn)("text-sm text-muted-foreground",e),...t}));d.displayName="CardDescription";let c=n.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,a.cn)("p-6 pt-0",e),...t}));c.displayName="CardContent";let u=n.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,a.cn)("flex items-center p-6 pt-0",e),...t}));u.displayName="CardFooter"},96720:(e,t,s)=>{"use strict";s.d(t,{Yq:()=>o,cn:()=>a,lk:()=>i});var r=s(82281),n=s(94805);function a(...e){return(0,n.QP)((0,r.$)(e))}function i(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){let t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)})}function o(e){return("string"==typeof e?new Date(e):e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})}},46109:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>x});var r=s(62740),n=s(31831),a=s(51825),i=s(37702),o=s(27914),l=s(99818),d=s(74825),c=s(73083),u=s(23956),m=s(98002);async function x(){let e=await (0,a.getServerSession)(i.N);e||(0,n.redirect)("/auth/login");let t=await o.db.quiz.findMany({where:{creatorId:e.user.id},include:{_count:{select:{responses:!0}}}}),s=await o.db.userResponse.findMany({where:{userId:e.user.id},include:{quiz:!0},orderBy:{completedAt:"desc"}}),x=await o.db.userResponse.findMany({where:{quiz:{creatorId:e.user.id}},include:{quiz:!0,user:{select:{id:!0,name:!0,email:!0}}},orderBy:{completedAt:"desc"},take:10}),p=t.length,h=s.length,f=x.length,g=s.length>0?s.reduce((e,t)=>e+t.score,0)/s.length:0,v=s.length>0?s.filter(e=>e.completedAt).length/s.length*100:0,b=t.map(e=>{let t=x.filter(t=>t.quizId===e.id),s=t.length>0?t.reduce((e,t)=>e+t.score,0)/t.length:0;return{quizTitle:e.title,responseCount:t.length,averageScore:s}}).filter(e=>e.responseCount>0).sort((e,t)=>t.responseCount-e.responseCount).slice(0,5),j=t.sort((e,t)=>(t._count?.responses||0)-(e._count?.responses||0)).slice(0,5).map(e=>({title:e.title,responseCount:e._count?.responses||0}));return(0,r.jsxs)("div",{className:"container mx-auto py-8 px-4",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold mb-8",children:"Analytics Dashboard"}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:(0,r.jsx)(d.default,{totalQuizzes:p,totalQuizzesTaken:h,totalResponsesReceived:f,averageScore:g,completionRate:v})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8",children:[(0,r.jsxs)(l.Zp,{children:[(0,r.jsxs)(l.aR,{children:[(0,r.jsx)(l.ZB,{children:"Quiz Performance"}),(0,r.jsx)(l.BT,{children:"Average scores and response counts for your top quizzes"})]}),(0,r.jsx)(l.Wu,{children:(0,r.jsx)(c.default,{data:b})})]}),(0,r.jsxs)(l.Zp,{children:[(0,r.jsxs)(l.aR,{children:[(0,r.jsx)(l.ZB,{children:"Top Quizzes"}),(0,r.jsx)(l.BT,{children:"Your most popular quizzes by number of responses"})]}),(0,r.jsx)(l.Wu,{children:(0,r.jsx)(u.default,{data:j})})]})]}),(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsxs)(l.Zp,{children:[(0,r.jsxs)(l.aR,{children:[(0,r.jsx)(l.ZB,{children:"Recent Activity"}),(0,r.jsx)(l.BT,{children:"Recent responses to your quizzes"})]}),(0,r.jsx)(l.Wu,{children:(0,r.jsx)(m.default,{responses:x})})]})})]})}},33405:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(62740),n=s(26996);function a({children:e}){return(0,r.jsxs)("div",{className:"flex min-h-screen flex-col",children:[(0,r.jsx)(n.default,{}),(0,r.jsx)("main",{className:"flex-1",children:e})]})}},74825:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/components/analytics/AnalyticsOverview.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/components/analytics/AnalyticsOverview.tsx","default")},73083:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/components/analytics/QuizPerformanceChart.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/components/analytics/QuizPerformanceChart.tsx","default")},98002:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/components/analytics/RecentActivityTable.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/components/analytics/RecentActivityTable.tsx","default")},23956:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/components/analytics/TopQuizzesChart.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/components/analytics/TopQuizzesChart.tsx","default")},26996:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/components/dashboard/Navbar.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/components/dashboard/Navbar.tsx","default")},99818:(e,t,s)=>{"use strict";s.d(t,{BT:()=>d,Wu:()=>c,ZB:()=>l,Zp:()=>i,aR:()=>o,wL:()=>u});var r=s(62740),n=s(76301),a=s(73300);let i=n.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,a.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));i.displayName="Card";let o=n.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,a.cn)("flex flex-col space-y-1.5 p-6",e),...t}));o.displayName="CardHeader";let l=n.forwardRef(({className:e,...t},s)=>(0,r.jsx)("h3",{ref:s,className:(0,a.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));l.displayName="CardTitle";let d=n.forwardRef(({className:e,...t},s)=>(0,r.jsx)("p",{ref:s,className:(0,a.cn)("text-sm text-muted-foreground",e),...t}));d.displayName="CardDescription";let c=n.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,a.cn)("p-6 pt-0",e),...t}));c.displayName="CardContent";let u=n.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,a.cn)("flex items-center p-6 pt-0",e),...t}));u.displayName="CardFooter"},73300:(e,t,s)=>{"use strict";s.d(t,{Yq:()=>i,cn:()=>a});var r=s(13673),n=s(47317);function a(...e){return(0,n.QP)((0,r.$)(e))}function i(e){return("string"==typeof e?new Date(e):e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})}}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[638,263,3,367,920,85,30],()=>s(52862));module.exports=r})();