(()=>{var e={};e.id=105,e.ids=[105],e.modules={96330:e=>{"use strict";e.exports=require("@prisma/client")},5486:e=>{"use strict";e.exports=require("bcrypt")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},12412:e=>{"use strict";e.exports=require("assert")},79428:e=>{"use strict";e.exports=require("buffer")},55511:e=>{"use strict";e.exports=require("crypto")},94735:e=>{"use strict";e.exports=require("events")},81630:e=>{"use strict";e.exports=require("http")},55591:e=>{"use strict";e.exports=require("https")},33873:e=>{"use strict";e.exports=require("path")},11723:e=>{"use strict";e.exports=require("querystring")},79551:e=>{"use strict";e.exports=require("url")},28354:e=>{"use strict";e.exports=require("util")},74075:e=>{"use strict";e.exports=require("zlib")},31524:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>x,tree:()=>l});var s=t(70260),n=t(28203),i=t(25155),a=t.n(i),d=t(67292),o={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>d[e]);t.d(r,o);let l=["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,57154)),"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/app/dashboard/page.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,33405)),"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/app/dashboard/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,71354)),"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["/Users/<USER>/Documents/augment-projects/hacking-quiz/src/app/dashboard/page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},66202:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,59607,23))},482:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,28531,23))},92295:(e,r,t)=>{Promise.resolve().then(t.bind(t,26996))},31615:(e,r,t)=>{Promise.resolve().then(t.bind(t,44848))},44848:(e,r,t)=>{"use strict";t.d(r,{default:()=>l});var s=t(45512),n=t(28531),i=t.n(n),a=t(79334),d=t(90993),o=t(39400);function l(){let e=(0,a.usePathname)(),{data:r}=(0,d.useSession)(),t=r=>e===r||e.startsWith(`${r}/`);return(0,s.jsx)("header",{className:"border-b",children:(0,s.jsxs)("div",{className:"container mx-auto px-4 flex h-16 items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-6",children:[(0,s.jsx)(i(),{href:"/",className:"text-xl font-bold",children:"QuizFlow"}),(0,s.jsxs)("nav",{className:"hidden md:flex gap-6",children:[(0,s.jsx)(i(),{href:"/dashboard",className:`text-sm font-medium transition-colors hover:text-primary ${!t("/dashboard")||t("/dashboard/quizzes")||t("/dashboard/activity")?"text-muted-foreground":"text-primary"}`,children:"Dashboard"}),(0,s.jsx)(i(),{href:"/dashboard/quizzes",className:`text-sm font-medium transition-colors hover:text-primary ${t("/dashboard/quizzes")?"text-primary":"text-muted-foreground"}`,children:"My Quizzes"}),(0,s.jsx)(i(),{href:"/dashboard/activity",className:`text-sm font-medium transition-colors hover:text-primary ${t("/dashboard/activity")?"text-primary":"text-muted-foreground"}`,children:"Activity"}),(0,s.jsx)(i(),{href:"/explore",className:`text-sm font-medium transition-colors hover:text-primary ${t("/explore")?"text-primary":"text-muted-foreground"}`,children:"Explore"})]})]}),(0,s.jsx)("div",{className:"flex items-center gap-4",children:r?(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsxs)("div",{className:"hidden md:block",children:[(0,s.jsx)("div",{className:"text-sm font-medium",children:r.user.name}),(0,s.jsx)("div",{className:"text-xs text-muted-foreground",children:r.user.email})]}),(0,s.jsx)(o.$,{variant:"outline",size:"sm",onClick:()=>(0,d.signOut)({callbackUrl:"/"}),children:"Sign Out"})]}):(0,s.jsx)(o.$,{asChild:!0,size:"sm",children:(0,s.jsx)(i(),{href:"/auth/login",children:"Sign In"})})})]})})}},39400:(e,r,t)=>{"use strict";t.d(r,{$:()=>l});var s=t(45512),n=t(58009),i=t(89383),a=t(21643),d=t(96720);let o=(0,a.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),l=n.forwardRef(({className:e,variant:r,size:t,asChild:n=!1,...a},l)=>{let c=n?i.DX:"button";return(0,s.jsx)(c,{className:(0,d.cn)(o({variant:r,size:t,className:e})),ref:l,...a})});l.displayName="Button"},96720:(e,r,t)=>{"use strict";t.d(r,{Yq:()=>d,cn:()=>i,lk:()=>a});var s=t(82281),n=t(94805);function i(...e){return(0,n.QP)((0,s.$)(e))}function a(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){let r=16*Math.random()|0;return("x"===e?r:3&r|8).toString(16)})}function d(e){return("string"==typeof e?new Date(e):e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})}},59607:(e,r,t)=>{let{createProxy:s}=t(73439);e.exports=s("/Users/<USER>/Documents/augment-projects/hacking-quiz/node_modules/next/dist/client/app-dir/link.js")},33405:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>i});var s=t(62740),n=t(26996);function i({children:e}){return(0,s.jsxs)("div",{className:"flex min-h-screen flex-col",children:[(0,s.jsx)(n.default,{}),(0,s.jsx)("main",{className:"flex-1",children:e})]})}},57154:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>x});var s=t(62740),n=t(31831),i=t(51825),a=t(37702),d=t(27914),o=t(99818),l=t(59607),c=t.n(l),u=t(13797);async function x(){let e=await (0,i.getServerSession)(a.N);e||(0,n.redirect)("/auth/login");let r=await d.db.quiz.findMany({where:{creatorId:e.user.id},orderBy:{updatedAt:"desc"},take:5}),t=await d.db.userResponse.findMany({where:{userId:e.user.id},include:{quiz:!0},orderBy:{completedAt:"desc"},take:5});return(0,s.jsxs)("div",{className:"container mx-auto py-8 px-4",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold mb-8",children:"Dashboard"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-8",children:[(0,s.jsxs)(o.Zp,{children:[(0,s.jsxs)(o.aR,{children:[(0,s.jsxs)(o.ZB,{children:["Welcome, ",e.user.name||"User","!"]}),(0,s.jsx)(o.BT,{children:"Manage your quizzes and view your progress"})]}),(0,s.jsx)(o.Wu,{children:(0,s.jsxs)("div",{className:"flex space-x-4",children:[(0,s.jsx)(u.$,{asChild:!0,children:(0,s.jsx)(c(),{href:"/dashboard/quizzes/create",children:"Create New Quiz"})}),(0,s.jsx)(u.$,{variant:"outline",asChild:!0,children:(0,s.jsx)(c(),{href:"/dashboard/quizzes",children:"View All Quizzes"})})]})})]}),(0,s.jsxs)(o.Zp,{children:[(0,s.jsxs)(o.aR,{children:[(0,s.jsx)(o.ZB,{children:"Your Stats"}),(0,s.jsx)(o.BT,{children:"Your quiz activity and performance"})]}),(0,s.jsx)(o.Wu,{children:(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{className:"bg-muted p-4 rounded-md",children:[(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Created Quizzes"}),(0,s.jsx)("p",{className:"text-2xl font-bold",children:r.length})]}),(0,s.jsxs)("div",{className:"bg-muted p-4 rounded-md",children:[(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Completed Quizzes"}),(0,s.jsx)("p",{className:"text-2xl font-bold",children:t.length})]})]})})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)(o.Zp,{children:[(0,s.jsxs)(o.aR,{children:[(0,s.jsx)(o.ZB,{children:"Your Quizzes"}),(0,s.jsx)(o.BT,{children:"Recently created and updated quizzes"})]}),(0,s.jsxs)(o.Wu,{children:[r.length>0?(0,s.jsx)("ul",{className:"space-y-2",children:r.map(e=>(0,s.jsx)("li",{className:"p-3 border rounded-md",children:(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium",children:e.title}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:e.isPublished?"Published":"Draft"})]}),(0,s.jsx)(u.$,{variant:"outline",size:"sm",asChild:!0,children:(0,s.jsx)(c(),{href:`/dashboard/quizzes/${e.id}`,children:"Edit"})})]})},e.id))}):(0,s.jsx)("p",{className:"text-muted-foreground",children:"You haven't created any quizzes yet."}),r.length>0&&(0,s.jsx)("div",{className:"mt-4",children:(0,s.jsx)(u.$,{variant:"link",asChild:!0,className:"px-0",children:(0,s.jsx)(c(),{href:"/dashboard/quizzes",children:"View all quizzes"})})})]})]}),(0,s.jsxs)(o.Zp,{children:[(0,s.jsxs)(o.aR,{children:[(0,s.jsx)(o.ZB,{children:"Recent Activity"}),(0,s.jsx)(o.BT,{children:"Your recent quiz attempts"})]}),(0,s.jsxs)(o.Wu,{children:[t.length>0?(0,s.jsx)("ul",{className:"space-y-2",children:t.map(e=>(0,s.jsx)("li",{className:"p-3 border rounded-md",children:(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium",children:e.quiz.title}),(0,s.jsxs)("div",{className:"flex justify-between items-center mt-1",children:[(0,s.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Score: ",e.score.toFixed(0),"%"]}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:new Date(e.completedAt||e.startedAt).toLocaleDateString()})]})]})},e.id))}):(0,s.jsx)("p",{className:"text-muted-foreground",children:"You haven't taken any quizzes yet."}),t.length>0&&(0,s.jsx)("div",{className:"mt-4",children:(0,s.jsx)(u.$,{variant:"link",asChild:!0,className:"px-0",children:(0,s.jsx)(c(),{href:"/dashboard/activity",children:"View all activity"})})})]})]})]})]})}},26996:(e,r,t)=>{"use strict";t.d(r,{default:()=>s});let s=(0,t(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/components/dashboard/Navbar.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/components/dashboard/Navbar.tsx","default")},13797:(e,r,t)=>{"use strict";t.d(r,{$:()=>p});var s=t(62740),n=t(76301);function i(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}var a=function(e){let r=function(e){let r=n.forwardRef((e,r)=>{let{children:t,...s}=e;if(n.isValidElement(t)){let e,a;let d=(e=Object.getOwnPropertyDescriptor(t.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?t.ref:(e=Object.getOwnPropertyDescriptor(t,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?t.props.ref:t.props.ref||t.ref,o=function(e,r){let t={...r};for(let s in r){let n=e[s],i=r[s];/^on[A-Z]/.test(s)?n&&i?t[s]=(...e)=>{let r=i(...e);return n(...e),r}:n&&(t[s]=n):"style"===s?t[s]={...n,...i}:"className"===s&&(t[s]=[n,i].filter(Boolean).join(" "))}return{...e,...t}}(s,t.props);return t.type!==n.Fragment&&(o.ref=r?function(...e){return r=>{let t=!1,s=e.map(e=>{let s=i(e,r);return t||"function"!=typeof s||(t=!0),s});if(t)return()=>{for(let r=0;r<s.length;r++){let t=s[r];"function"==typeof t?t():i(e[r],null)}}}}(r,d):d),n.cloneElement(t,o)}return n.Children.count(t)>1?n.Children.only(null):null});return r.displayName=`${e}.SlotClone`,r}(e),t=n.forwardRef((e,t)=>{let{children:i,...a}=e,d=n.Children.toArray(i),l=d.find(o);if(l){let e=l.props.children,i=d.map(r=>r!==l?r:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,s.jsx)(r,{...a,ref:t,children:n.isValidElement(e)?n.cloneElement(e,void 0,i):null})}return(0,s.jsx)(r,{...a,ref:t,children:i})});return t.displayName=`${e}.Slot`,t}("Slot"),d=Symbol("radix.slottable");function o(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===d}var l=t(13673);let c=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,u=l.$;var x=t(73300);let m=((e,r)=>t=>{var s;if((null==r?void 0:r.variants)==null)return u(e,null==t?void 0:t.class,null==t?void 0:t.className);let{variants:n,defaultVariants:i}=r,a=Object.keys(n).map(e=>{let r=null==t?void 0:t[e],s=null==i?void 0:i[e];if(null===r)return null;let a=c(r)||c(s);return n[e][a]}),d=t&&Object.entries(t).reduce((e,r)=>{let[t,s]=r;return void 0===s||(e[t]=s),e},{});return u(e,a,null==r?void 0:null===(s=r.compoundVariants)||void 0===s?void 0:s.reduce((e,r)=>{let{class:t,className:s,...n}=r;return Object.entries(n).every(e=>{let[r,t]=e;return Array.isArray(t)?t.includes({...i,...d}[r]):({...i,...d})[r]===t})?[...e,t,s]:e},[]),null==t?void 0:t.class,null==t?void 0:t.className)})("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),p=n.forwardRef(({className:e,variant:r,size:t,asChild:n=!1,...i},d)=>{let o=n?a:"button";return(0,s.jsx)(o,{className:(0,x.cn)(m({variant:r,size:t,className:e})),ref:d,...i})});p.displayName="Button"},99818:(e,r,t)=>{"use strict";t.d(r,{BT:()=>l,Wu:()=>c,ZB:()=>o,Zp:()=>a,aR:()=>d,wL:()=>u});var s=t(62740),n=t(76301),i=t(73300);let a=n.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...r}));a.displayName="Card";let d=n.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...r}));d.displayName="CardHeader";let o=n.forwardRef(({className:e,...r},t)=>(0,s.jsx)("h3",{ref:t,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...r}));o.displayName="CardTitle";let l=n.forwardRef(({className:e,...r},t)=>(0,s.jsx)("p",{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",e),...r}));l.displayName="CardDescription";let c=n.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("p-6 pt-0",e),...r}));c.displayName="CardContent";let u=n.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("flex items-center p-6 pt-0",e),...r}));u.displayName="CardFooter"},73300:(e,r,t)=>{"use strict";t.d(r,{Yq:()=>a,cn:()=>i});var s=t(13673),n=t(47317);function i(...e){return(0,n.QP)((0,s.$)(e))}function a(e){return("string"==typeof e?new Date(e):e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})}}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[638,263,3,367,920,85,30],()=>t(31524));module.exports=s})();