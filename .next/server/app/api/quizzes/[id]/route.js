(()=>{var e={};e.id=145,e.ids=[145],e.modules={96330:e=>{"use strict";e.exports=require("@prisma/client")},5486:e=>{"use strict";e.exports=require("bcrypt")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},12412:e=>{"use strict";e.exports=require("assert")},79428:e=>{"use strict";e.exports=require("buffer")},55511:e=>{"use strict";e.exports=require("crypto")},94735:e=>{"use strict";e.exports=require("events")},81630:e=>{"use strict";e.exports=require("http")},55591:e=>{"use strict";e.exports=require("https")},11723:e=>{"use strict";e.exports=require("querystring")},79551:e=>{"use strict";e.exports=require("url")},28354:e=>{"use strict";e.exports=require("util")},74075:e=>{"use strict";e.exports=require("zlib")},58759:(e,r,s)=>{"use strict";s.r(r),s.d(r,{patchFetch:()=>w,routeModule:()=>g,serverHooks:()=>h,workAsyncStorage:()=>q,workUnitAsyncStorage:()=>x});var t={};s.r(t),s.d(t,{DELETE:()=>m,GET:()=>p,PATCH:()=>l});var i=s(42706),n=s(28203),a=s(45994),u=s(39187),o=s(51825),d=s(37702),c=s(27914);async function p(e,{params:r}){try{let e=await (0,o.getServerSession)(d.N),s=r.id,t=await c.db.quiz.findUnique({where:{id:s},include:{questions:!0,questionPools:{include:{questions:!0}},selectionRules:!0}});if(!t)return u.NextResponse.json({message:"Quiz not found"},{status:404});if(!t.isPublished&&(!e||t.creatorId!==e.user.id))return u.NextResponse.json({message:"Unauthorized"},{status:401});return u.NextResponse.json(t)}catch(e){return console.error("Error fetching quiz:",e),u.NextResponse.json({message:"An error occurred while fetching the quiz"},{status:500})}}async function l(e,{params:r}){try{let s=await (0,o.getServerSession)(d.N);if(!s)return u.NextResponse.json({message:"Unauthorized"},{status:401});let t=r.id,i=await e.json();if(!await c.db.quiz.findUnique({where:{id:t,creatorId:s.user.id}}))return u.NextResponse.json({message:"Quiz not found or you don't have permission to edit it"},{status:404});let n=await c.db.quiz.update({where:{id:t},data:i});return u.NextResponse.json(n)}catch(e){return console.error("Error updating quiz:",e),u.NextResponse.json({message:"An error occurred while updating the quiz"},{status:500})}}async function m(e,{params:r}){try{let e=await (0,o.getServerSession)(d.N);if(!e)return u.NextResponse.json({message:"Unauthorized"},{status:401});let s=r.id;if(!await c.db.quiz.findUnique({where:{id:s,creatorId:e.user.id}}))return u.NextResponse.json({message:"Quiz not found or you don't have permission to delete it"},{status:404});return await c.db.quiz.delete({where:{id:s}}),u.NextResponse.json({message:"Quiz deleted successfully"},{status:200})}catch(e){return console.error("Error deleting quiz:",e),u.NextResponse.json({message:"An error occurred while deleting the quiz"},{status:500})}}let g=new i.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/quizzes/[id]/route",pathname:"/api/quizzes/[id]",filename:"route",bundlePath:"app/api/quizzes/[id]/route"},resolvedPagePath:"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/app/api/quizzes/[id]/route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:q,workUnitAsyncStorage:x,serverHooks:h}=g;function w(){return(0,a.patchFetch)({workAsyncStorage:q,workUnitAsyncStorage:x})}},96487:()=>{},78335:()=>{},37702:(e,r,s)=>{"use strict";s.d(r,{N:()=>c});var t=s(46814),i=s(91642),n=s(28053),a=s(7553),u=s(5486),o=s.n(u),d=s(27914);let c={adapter:(0,t.y)(d.db),session:{strategy:"jwt"},pages:{signIn:"/auth/login",signOut:"/auth/logout",error:"/auth/error"},providers:[(0,n.A)({clientId:process.env.GOOGLE_CLIENT_ID||"",clientSecret:process.env.GOOGLE_CLIENT_SECRET||""}),(0,a.A)({clientId:process.env.GITHUB_CLIENT_ID||"",clientSecret:process.env.GITHUB_CLIENT_SECRET||""}),(0,i.A)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)return null;let r=await d.db.user.findUnique({where:{email:e.email}});return r&&r.password&&await o().compare(e.password,r.password)?{id:r.id,name:r.name,email:r.email,image:r.image,role:r.role}:null}})],callbacks:{session:async({session:e,token:r})=>(r&&(e.user.id=r.id,e.user.name=r.name,e.user.email=r.email,e.user.image=r.picture,e.user.role=r.role),e),async jwt({token:e,user:r}){let s=await d.db.user.findFirst({where:{email:e.email}});return s?{id:s.id,name:s.name,email:s.email,picture:s.image,role:s.role}:(r&&(e.id=r.id),e)}},secret:process.env.NEXTAUTH_SECRET}},27914:(e,r,s)=>{"use strict";s.d(r,{db:()=>i});var t=s(96330);let i=globalThis.prisma||new t.PrismaClient}};var r=require("../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[638,263,452],()=>s(58759));module.exports=t})();