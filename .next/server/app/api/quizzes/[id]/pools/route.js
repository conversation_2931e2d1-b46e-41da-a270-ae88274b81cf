(()=>{var e={};e.id=49,e.ids=[49],e.modules={96330:e=>{"use strict";e.exports=require("@prisma/client")},5486:e=>{"use strict";e.exports=require("bcrypt")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},12412:e=>{"use strict";e.exports=require("assert")},79428:e=>{"use strict";e.exports=require("buffer")},55511:e=>{"use strict";e.exports=require("crypto")},94735:e=>{"use strict";e.exports=require("events")},81630:e=>{"use strict";e.exports=require("http")},55591:e=>{"use strict";e.exports=require("https")},11723:e=>{"use strict";e.exports=require("querystring")},79551:e=>{"use strict";e.exports=require("url")},28354:e=>{"use strict";e.exports=require("util")},74075:e=>{"use strict";e.exports=require("zlib")},8683:(e,r,s)=>{"use strict";s.r(r),s.d(r,{patchFetch:()=>q,routeModule:()=>m,serverHooks:()=>w,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>g});var t={};s.r(t),s.d(t,{GET:()=>c,POST:()=>l});var i=s(42706),a=s(28203),o=s(45994),n=s(39187),u=s(51825),p=s(37702),d=s(27914);async function c(e,{params:r}){try{let e=await (0,u.getServerSession)(p.N),s=await r.id,t=await d.db.quiz.findUnique({where:{id:s}});if(!t)return n.NextResponse.json({message:"Quiz not found"},{status:404});if(!t.isPublished&&(!e||t.creatorId!==e.user.id))return n.NextResponse.json({message:"Unauthorized"},{status:401});let i=await d.db.questionPool.findMany({where:{quizId:s},include:{questions:!0},orderBy:{createdAt:"asc"}});return n.NextResponse.json(i)}catch(e){return console.error("Error fetching pools:",e),n.NextResponse.json({message:"An error occurred while fetching pools"},{status:500})}}async function l(e,{params:r}){try{let s=await (0,u.getServerSession)(p.N);if(!s)return n.NextResponse.json({message:"Unauthorized"},{status:401});let t=await r.id,i=await e.json();if(!await d.db.quiz.findUnique({where:{id:t,creatorId:s.user.id}}))return n.NextResponse.json({message:"Quiz not found or you don't have permission to edit it"},{status:404});let a=await d.db.questionPool.create({data:{...i,quizId:t}});return n.NextResponse.json(a,{status:201})}catch(e){return console.error("Error creating pool:",e),n.NextResponse.json({message:"An error occurred while creating the pool"},{status:500})}}let m=new i.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/quizzes/[id]/pools/route",pathname:"/api/quizzes/[id]/pools",filename:"route",bundlePath:"app/api/quizzes/[id]/pools/route"},resolvedPagePath:"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/app/api/quizzes/[id]/pools/route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:x,workUnitAsyncStorage:g,serverHooks:w}=m;function q(){return(0,o.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:g})}},96487:()=>{},78335:()=>{},37702:(e,r,s)=>{"use strict";s.d(r,{N:()=>d});var t=s(46814),i=s(91642),a=s(28053),o=s(7553),n=s(5486),u=s.n(n),p=s(27914);let d={adapter:(0,t.y)(p.db),session:{strategy:"jwt"},pages:{signIn:"/auth/login",signOut:"/auth/logout",error:"/auth/error"},providers:[(0,a.A)({clientId:process.env.GOOGLE_CLIENT_ID||"",clientSecret:process.env.GOOGLE_CLIENT_SECRET||""}),(0,o.A)({clientId:process.env.GITHUB_CLIENT_ID||"",clientSecret:process.env.GITHUB_CLIENT_SECRET||""}),(0,i.A)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)return null;let r=await p.db.user.findUnique({where:{email:e.email}});return r&&r.password&&await u().compare(e.password,r.password)?{id:r.id,name:r.name,email:r.email,image:r.image,role:r.role}:null}})],callbacks:{session:async({session:e,token:r})=>(r&&(e.user.id=r.id,e.user.name=r.name,e.user.email=r.email,e.user.image=r.picture,e.user.role=r.role),e),async jwt({token:e,user:r}){let s=await p.db.user.findFirst({where:{email:e.email}});return s?{id:s.id,name:s.name,email:s.email,picture:s.image,role:s.role}:(r&&(e.id=r.id),e)}},secret:process.env.NEXTAUTH_SECRET}},27914:(e,r,s)=>{"use strict";s.d(r,{db:()=>i});var t=s(96330);let i=globalThis.prisma||new t.PrismaClient}};var r=require("../../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[638,263,452],()=>s(8683));module.exports=t})();