(()=>{var e={};e.id=563,e.ids=[563],e.modules={96330:e=>{"use strict";e.exports=require("@prisma/client")},5486:e=>{"use strict";e.exports=require("bcrypt")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},12412:e=>{"use strict";e.exports=require("assert")},79428:e=>{"use strict";e.exports=require("buffer")},55511:e=>{"use strict";e.exports=require("crypto")},94735:e=>{"use strict";e.exports=require("events")},81630:e=>{"use strict";e.exports=require("http")},55591:e=>{"use strict";e.exports=require("https")},11723:e=>{"use strict";e.exports=require("querystring")},79551:e=>{"use strict";e.exports=require("url")},28354:e=>{"use strict";e.exports=require("util")},74075:e=>{"use strict";e.exports=require("zlib")},89970:(e,s,r)=>{"use strict";r.r(s),r.d(s,{patchFetch:()=>h,routeModule:()=>w,serverHooks:()=>x,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>q});var t={};r.r(t),r.d(t,{DELETE:()=>m,GET:()=>p,PATCH:()=>c});var i=r(42706),o=r(28203),n=r(45994),a=r(39187),u=r(51825),d=r(37702),l=r(27914);async function p(e,{params:s}){try{let e=await (0,u.getServerSession)(d.N),{id:r,poolId:t}=s,i=await l.db.questionPool.findUnique({where:{id:t,quizId:r},include:{questions:!0}});if(!i)return a.NextResponse.json({message:"Pool not found"},{status:404});let o=await l.db.quiz.findUnique({where:{id:r}});if(!o)return a.NextResponse.json({message:"Quiz not found"},{status:404});if(!o.isPublished&&(!e||o.creatorId!==e.user.id))return a.NextResponse.json({message:"Unauthorized"},{status:401});return a.NextResponse.json(i)}catch(e){return console.error("Error fetching pool:",e),a.NextResponse.json({message:"An error occurred while fetching the pool"},{status:500})}}async function c(e,{params:s}){try{let r=await (0,u.getServerSession)(d.N);if(!r)return a.NextResponse.json({message:"Unauthorized"},{status:401});let{id:t,poolId:i}=s,o=await e.json();if(!await l.db.quiz.findUnique({where:{id:t,creatorId:r.user.id}}))return a.NextResponse.json({message:"Quiz not found or you don't have permission to edit it"},{status:404});if(!await l.db.questionPool.findUnique({where:{id:i,quizId:t}}))return a.NextResponse.json({message:"Pool not found"},{status:404});let n=await l.db.questionPool.update({where:{id:i},data:o});return a.NextResponse.json(n)}catch(e){return console.error("Error updating pool:",e),a.NextResponse.json({message:"An error occurred while updating the pool"},{status:500})}}async function m(e,{params:s}){try{let e=await (0,u.getServerSession)(d.N);if(!e)return a.NextResponse.json({message:"Unauthorized"},{status:401});let{id:r,poolId:t}=s;if(!await l.db.quiz.findUnique({where:{id:r,creatorId:e.user.id}}))return a.NextResponse.json({message:"Quiz not found or you don't have permission to edit it"},{status:404});let i=await l.db.questionPool.findUnique({where:{id:t,quizId:r}});if(!i)return a.NextResponse.json({message:"Pool not found"},{status:404});return await l.db.selectionRule.deleteMany({where:{quizId:r,poolId:i.poolId}}),await l.db.questionPool.delete({where:{id:t}}),a.NextResponse.json({message:"Pool deleted successfully"},{status:200})}catch(e){return console.error("Error deleting pool:",e),a.NextResponse.json({message:"An error occurred while deleting the pool"},{status:500})}}let w=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/quizzes/[id]/pools/[poolId]/route",pathname:"/api/quizzes/[id]/pools/[poolId]",filename:"route",bundlePath:"app/api/quizzes/[id]/pools/[poolId]/route"},resolvedPagePath:"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/app/api/quizzes/[id]/pools/[poolId]/route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:g,workUnitAsyncStorage:q,serverHooks:x}=w;function h(){return(0,n.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:q})}},96487:()=>{},78335:()=>{},37702:(e,s,r)=>{"use strict";r.d(s,{N:()=>l});var t=r(46814),i=r(91642),o=r(28053),n=r(7553),a=r(5486),u=r.n(a),d=r(27914);let l={adapter:(0,t.y)(d.db),session:{strategy:"jwt"},pages:{signIn:"/auth/login",signOut:"/auth/logout",error:"/auth/error"},providers:[(0,o.A)({clientId:process.env.GOOGLE_CLIENT_ID||"",clientSecret:process.env.GOOGLE_CLIENT_SECRET||""}),(0,n.A)({clientId:process.env.GITHUB_CLIENT_ID||"",clientSecret:process.env.GITHUB_CLIENT_SECRET||""}),(0,i.A)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)return null;let s=await d.db.user.findUnique({where:{email:e.email}});return s&&s.password&&await u().compare(e.password,s.password)?{id:s.id,name:s.name,email:s.email,image:s.image,role:s.role}:null}})],callbacks:{session:async({session:e,token:s})=>(s&&(e.user.id=s.id,e.user.name=s.name,e.user.email=s.email,e.user.image=s.picture,e.user.role=s.role),e),async jwt({token:e,user:s}){let r=await d.db.user.findFirst({where:{email:e.email}});return r?{id:r.id,name:r.name,email:r.email,picture:r.image,role:r.role}:(s&&(e.id=s.id),e)}},secret:process.env.NEXTAUTH_SECRET}},27914:(e,s,r)=>{"use strict";r.d(s,{db:()=>i});var t=r(96330);let i=globalThis.prisma||new t.PrismaClient}};var s=require("../../../../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[638,263,452],()=>r(89970));module.exports=t})();