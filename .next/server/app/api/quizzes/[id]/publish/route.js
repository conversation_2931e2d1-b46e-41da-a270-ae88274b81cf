(()=>{var e={};e.id=593,e.ids=[593],e.modules={96330:e=>{"use strict";e.exports=require("@prisma/client")},5486:e=>{"use strict";e.exports=require("bcrypt")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},12412:e=>{"use strict";e.exports=require("assert")},79428:e=>{"use strict";e.exports=require("buffer")},55511:e=>{"use strict";e.exports=require("crypto")},94735:e=>{"use strict";e.exports=require("events")},81630:e=>{"use strict";e.exports=require("http")},55591:e=>{"use strict";e.exports=require("https")},11723:e=>{"use strict";e.exports=require("querystring")},79551:e=>{"use strict";e.exports=require("url")},28354:e=>{"use strict";e.exports=require("util")},74075:e=>{"use strict";e.exports=require("zlib")},64824:(e,r,s)=>{"use strict";s.r(r),s.d(r,{patchFetch:()=>g,routeModule:()=>c,serverHooks:()=>h,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>x});var t={};s.r(t),s.d(t,{POST:()=>l});var i=s(42706),a=s(28203),u=s(45994),n=s(39187),o=s(51825),p=s(37702),d=s(27914);async function l(e,{params:r}){try{let s=await (0,o.getServerSession)(p.N);if(!s)return n.NextResponse.json({message:"Unauthorized"},{status:401});let t=r.id,{isPublished:i}=await e.json();if(!await d.db.quiz.findUnique({where:{id:t,creatorId:s.user.id}}))return n.NextResponse.json({message:"Quiz not found or you don't have permission to edit it"},{status:404});let a=await d.db.quiz.update({where:{id:t},data:{isPublished:i}});return n.NextResponse.json(a)}catch(e){return console.error("Error updating publish status:",e),n.NextResponse.json({message:"An error occurred while updating the publish status"},{status:500})}}let c=new i.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/quizzes/[id]/publish/route",pathname:"/api/quizzes/[id]/publish",filename:"route",bundlePath:"app/api/quizzes/[id]/publish/route"},resolvedPagePath:"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/app/api/quizzes/[id]/publish/route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:m,workUnitAsyncStorage:x,serverHooks:h}=c;function g(){return(0,u.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:x})}},96487:()=>{},78335:()=>{},37702:(e,r,s)=>{"use strict";s.d(r,{N:()=>d});var t=s(46814),i=s(91642),a=s(28053),u=s(7553),n=s(5486),o=s.n(n),p=s(27914);let d={adapter:(0,t.y)(p.db),session:{strategy:"jwt"},pages:{signIn:"/auth/login",signOut:"/auth/logout",error:"/auth/error"},providers:[(0,a.A)({clientId:process.env.GOOGLE_CLIENT_ID||"",clientSecret:process.env.GOOGLE_CLIENT_SECRET||""}),(0,u.A)({clientId:process.env.GITHUB_CLIENT_ID||"",clientSecret:process.env.GITHUB_CLIENT_SECRET||""}),(0,i.A)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)return null;let r=await p.db.user.findUnique({where:{email:e.email}});return r&&r.password&&await o().compare(e.password,r.password)?{id:r.id,name:r.name,email:r.email,image:r.image,role:r.role}:null}})],callbacks:{session:async({session:e,token:r})=>(r&&(e.user.id=r.id,e.user.name=r.name,e.user.email=r.email,e.user.image=r.picture,e.user.role=r.role),e),async jwt({token:e,user:r}){let s=await p.db.user.findFirst({where:{email:e.email}});return s?{id:s.id,name:s.name,email:s.email,picture:s.image,role:s.role}:(r&&(e.id=r.id),e)}},secret:process.env.NEXTAUTH_SECRET}},27914:(e,r,s)=>{"use strict";s.d(r,{db:()=>i});var t=s(96330);let i=globalThis.prisma||new t.PrismaClient}};var r=require("../../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[638,263,452],()=>s(64824));module.exports=t})();