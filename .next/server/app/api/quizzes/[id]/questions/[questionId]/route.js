(()=>{var e={};e.id=71,e.ids=[71],e.modules={96330:e=>{"use strict";e.exports=require("@prisma/client")},5486:e=>{"use strict";e.exports=require("bcrypt")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},12412:e=>{"use strict";e.exports=require("assert")},79428:e=>{"use strict";e.exports=require("buffer")},55511:e=>{"use strict";e.exports=require("crypto")},94735:e=>{"use strict";e.exports=require("events")},81630:e=>{"use strict";e.exports=require("http")},55591:e=>{"use strict";e.exports=require("https")},11723:e=>{"use strict";e.exports=require("querystring")},79551:e=>{"use strict";e.exports=require("url")},28354:e=>{"use strict";e.exports=require("util")},74075:e=>{"use strict";e.exports=require("zlib")},59077:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>f,routeModule:()=>q,serverHooks:()=>w,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>x});var r={};s.r(r),s.d(r,{DELETE:()=>m,GET:()=>c,PATCH:()=>l});var i=s(42706),n=s(28203),o=s(45994),u=s(39187),a=s(51825),d=s(37702),p=s(27914);async function c(e,{params:t}){try{let e=await (0,a.getServerSession)(d.N),{id:s,questionId:r}=t,i=await p.db.question.findUnique({where:{id:r,quizId:s}});if(!i)return u.NextResponse.json({message:"Question not found"},{status:404});let n=await p.db.quiz.findUnique({where:{id:s}});if(!n)return u.NextResponse.json({message:"Quiz not found"},{status:404});if(!n.isPublished&&(!e||n.creatorId!==e.user.id))return u.NextResponse.json({message:"Unauthorized"},{status:401});return u.NextResponse.json(i)}catch(e){return console.error("Error fetching question:",e),u.NextResponse.json({message:"An error occurred while fetching the question"},{status:500})}}async function l(e,{params:t}){try{let s=await (0,a.getServerSession)(d.N);if(!s)return u.NextResponse.json({message:"Unauthorized"},{status:401});let{id:r,questionId:i}=t,n=await e.json();if(!await p.db.quiz.findUnique({where:{id:r,creatorId:s.user.id}}))return u.NextResponse.json({message:"Quiz not found or you don't have permission to edit it"},{status:404});if(!await p.db.question.findUnique({where:{id:i,quizId:r}}))return u.NextResponse.json({message:"Question not found"},{status:404});let o={...n};for(let e of(o.text&&"string"!=typeof o.text&&(o.text=JSON.stringify(o.text)),["options","correctAnswer","correctAnswers","stems","correctPairs","textTemplate","blanks"]))o[e]&&"string"!=typeof o[e]&&(o[e]=JSON.stringify(o[e]));let c=await p.db.question.update({where:{id:i},data:o});return u.NextResponse.json(c)}catch(e){return console.error("Error updating question:",e),u.NextResponse.json({message:"An error occurred while updating the question"},{status:500})}}async function m(e,{params:t}){try{let e=await (0,a.getServerSession)(d.N);if(!e)return u.NextResponse.json({message:"Unauthorized"},{status:401});let{id:s,questionId:r}=t;if(!await p.db.quiz.findUnique({where:{id:s,creatorId:e.user.id}}))return u.NextResponse.json({message:"Quiz not found or you don't have permission to edit it"},{status:404});if(!await p.db.question.findUnique({where:{id:r,quizId:s}}))return u.NextResponse.json({message:"Question not found"},{status:404});return await p.db.question.delete({where:{id:r}}),u.NextResponse.json({message:"Question deleted successfully"},{status:200})}catch(e){return console.error("Error deleting question:",e),u.NextResponse.json({message:"An error occurred while deleting the question"},{status:500})}}let q=new i.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/quizzes/[id]/questions/[questionId]/route",pathname:"/api/quizzes/[id]/questions/[questionId]",filename:"route",bundlePath:"app/api/quizzes/[id]/questions/[questionId]/route"},resolvedPagePath:"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/app/api/quizzes/[id]/questions/[questionId]/route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:g,workUnitAsyncStorage:x,serverHooks:w}=q;function f(){return(0,o.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:x})}},96487:()=>{},78335:()=>{},37702:(e,t,s)=>{"use strict";s.d(t,{N:()=>p});var r=s(46814),i=s(91642),n=s(28053),o=s(7553),u=s(5486),a=s.n(u),d=s(27914);let p={adapter:(0,r.y)(d.db),session:{strategy:"jwt"},pages:{signIn:"/auth/login",signOut:"/auth/logout",error:"/auth/error"},providers:[(0,n.A)({clientId:process.env.GOOGLE_CLIENT_ID||"",clientSecret:process.env.GOOGLE_CLIENT_SECRET||""}),(0,o.A)({clientId:process.env.GITHUB_CLIENT_ID||"",clientSecret:process.env.GITHUB_CLIENT_SECRET||""}),(0,i.A)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)return null;let t=await d.db.user.findUnique({where:{email:e.email}});return t&&t.password&&await a().compare(e.password,t.password)?{id:t.id,name:t.name,email:t.email,image:t.image,role:t.role}:null}})],callbacks:{session:async({session:e,token:t})=>(t&&(e.user.id=t.id,e.user.name=t.name,e.user.email=t.email,e.user.image=t.picture,e.user.role=t.role),e),async jwt({token:e,user:t}){let s=await d.db.user.findFirst({where:{email:e.email}});return s?{id:s.id,name:s.name,email:s.email,picture:s.image,role:s.role}:(t&&(e.id=t.id),e)}},secret:process.env.NEXTAUTH_SECRET}},27914:(e,t,s)=>{"use strict";s.d(t,{db:()=>i});var r=s(96330);let i=globalThis.prisma||new r.PrismaClient}};var t=require("../../../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[638,263,452],()=>s(59077));module.exports=r})();