(()=>{var e={};e.id=944,e.ids=[944],e.modules={96330:e=>{"use strict";e.exports=require("@prisma/client")},5486:e=>{"use strict";e.exports=require("bcrypt")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},12412:e=>{"use strict";e.exports=require("assert")},79428:e=>{"use strict";e.exports=require("buffer")},55511:e=>{"use strict";e.exports=require("crypto")},94735:e=>{"use strict";e.exports=require("events")},81630:e=>{"use strict";e.exports=require("http")},55591:e=>{"use strict";e.exports=require("https")},11723:e=>{"use strict";e.exports=require("querystring")},79551:e=>{"use strict";e.exports=require("url")},28354:e=>{"use strict";e.exports=require("util")},74075:e=>{"use strict";e.exports=require("zlib")},64694:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>w,routeModule:()=>m,serverHooks:()=>g,workAsyncStorage:()=>q,workUnitAsyncStorage:()=>x});var s={};t.r(s),t.d(s,{GET:()=>p,POST:()=>l});var i=t(42706),n=t(28203),a=t(45994),o=t(39187),u=t(51825),d=t(37702),c=t(27914);async function p(e,{params:r}){try{let e=await (0,u.getServerSession)(d.N),t=r.id,s=await c.db.quiz.findUnique({where:{id:t}});if(!s)return o.NextResponse.json({message:"Quiz not found"},{status:404});if(!s.isPublished&&(!e||s.creatorId!==e.user.id))return o.NextResponse.json({message:"Unauthorized"},{status:401});let i=await c.db.question.findMany({where:{quizId:t},orderBy:{createdAt:"asc"}});return o.NextResponse.json(i)}catch(e){return console.error("Error fetching questions:",e),o.NextResponse.json({message:"An error occurred while fetching questions"},{status:500})}}async function l(e,{params:r}){try{let t=await (0,u.getServerSession)(d.N);if(!t)return o.NextResponse.json({message:"Unauthorized"},{status:401});let s=r.id,i=await e.json();if(!await c.db.quiz.findUnique({where:{id:s,creatorId:t.user.id}}))return o.NextResponse.json({message:"Quiz not found or you don't have permission to edit it"},{status:404});let n={...i};for(let e of("string"!=typeof n.text&&(n.text=JSON.stringify(n.text)),["options","correctAnswer","correctAnswers","stems","correctPairs","textTemplate","blanks"]))n[e]&&"string"!=typeof n[e]&&(n[e]=JSON.stringify(n[e]));let a=await c.db.question.create({data:{...n,quizId:s}});return o.NextResponse.json(a,{status:201})}catch(e){return console.error("Error adding question:",e),o.NextResponse.json({message:"An error occurred while adding the question"},{status:500})}}let m=new i.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/quizzes/[id]/questions/route",pathname:"/api/quizzes/[id]/questions",filename:"route",bundlePath:"app/api/quizzes/[id]/questions/route"},resolvedPagePath:"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/app/api/quizzes/[id]/questions/route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:q,workUnitAsyncStorage:x,serverHooks:g}=m;function w(){return(0,a.patchFetch)({workAsyncStorage:q,workUnitAsyncStorage:x})}},96487:()=>{},78335:()=>{},37702:(e,r,t)=>{"use strict";t.d(r,{N:()=>c});var s=t(46814),i=t(91642),n=t(28053),a=t(7553),o=t(5486),u=t.n(o),d=t(27914);let c={adapter:(0,s.y)(d.db),session:{strategy:"jwt"},pages:{signIn:"/auth/login",signOut:"/auth/logout",error:"/auth/error"},providers:[(0,n.A)({clientId:process.env.GOOGLE_CLIENT_ID||"",clientSecret:process.env.GOOGLE_CLIENT_SECRET||""}),(0,a.A)({clientId:process.env.GITHUB_CLIENT_ID||"",clientSecret:process.env.GITHUB_CLIENT_SECRET||""}),(0,i.A)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)return null;let r=await d.db.user.findUnique({where:{email:e.email}});return r&&r.password&&await u().compare(e.password,r.password)?{id:r.id,name:r.name,email:r.email,image:r.image,role:r.role}:null}})],callbacks:{session:async({session:e,token:r})=>(r&&(e.user.id=r.id,e.user.name=r.name,e.user.email=r.email,e.user.image=r.picture,e.user.role=r.role),e),async jwt({token:e,user:r}){let t=await d.db.user.findFirst({where:{email:e.email}});return t?{id:t.id,name:t.name,email:t.email,picture:t.image,role:t.role}:(r&&(e.id=r.id),e)}},secret:process.env.NEXTAUTH_SECRET}},27914:(e,r,t)=>{"use strict";t.d(r,{db:()=>i});var s=t(96330);let i=globalThis.prisma||new s.PrismaClient}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[638,263,452],()=>t(64694));module.exports=s})();