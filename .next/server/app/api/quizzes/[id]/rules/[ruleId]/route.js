(()=>{var e={};e.id=259,e.ids=[259],e.modules={96330:e=>{"use strict";e.exports=require("@prisma/client")},5486:e=>{"use strict";e.exports=require("bcrypt")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},12412:e=>{"use strict";e.exports=require("assert")},79428:e=>{"use strict";e.exports=require("buffer")},55511:e=>{"use strict";e.exports=require("crypto")},94735:e=>{"use strict";e.exports=require("events")},81630:e=>{"use strict";e.exports=require("http")},55591:e=>{"use strict";e.exports=require("https")},11723:e=>{"use strict";e.exports=require("querystring")},79551:e=>{"use strict";e.exports=require("url")},28354:e=>{"use strict";e.exports=require("util")},74075:e=>{"use strict";e.exports=require("zlib")},12618:(e,r,s)=>{"use strict";s.r(r),s.d(r,{patchFetch:()=>f,routeModule:()=>w,serverHooks:()=>h,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>x});var t={};s.r(t),s.d(t,{DELETE:()=>m,GET:()=>c,PATCH:()=>p});var i=s(42706),n=s(28203),o=s(45994),u=s(39187),a=s(51825),d=s(37702),l=s(27914);async function c(e,{params:r}){try{let e=await (0,a.getServerSession)(d.N),{id:s,ruleId:t}=await r,i=await l.db.selectionRule.findUnique({where:{id:t,quizId:s}});if(!i)return u.NextResponse.json({message:"Selection rule not found"},{status:404});let n=await l.db.quiz.findUnique({where:{id:s}});if(!n)return u.NextResponse.json({message:"Quiz not found"},{status:404});if(!n.isPublished&&(!e||n.creatorId!==e.user.id))return u.NextResponse.json({message:"Unauthorized"},{status:401});return u.NextResponse.json(i)}catch(e){return console.error("Error fetching selection rule:",e),u.NextResponse.json({message:"An error occurred while fetching the selection rule"},{status:500})}}async function p(e,{params:r}){try{let s=await (0,a.getServerSession)(d.N);if(!s)return u.NextResponse.json({message:"Unauthorized"},{status:401});let{id:t,ruleId:i}=await r,n=await e.json();if(!await l.db.quiz.findUnique({where:{id:t,creatorId:s.user.id}}))return u.NextResponse.json({message:"Quiz not found or you don't have permission to edit it"},{status:404});if(!await l.db.selectionRule.findUnique({where:{id:i,quizId:t}}))return u.NextResponse.json({message:"Selection rule not found"},{status:404});if(n.poolId){let e=await l.db.questionPool.findUnique({where:{id:n.poolId,quizId:t}});if(!e)return u.NextResponse.json({message:"Pool not found"},{status:404});n.poolId=e.poolId}let o=await l.db.selectionRule.update({where:{id:i},data:n});return u.NextResponse.json(o)}catch(e){return console.error("Error updating selection rule:",e),u.NextResponse.json({message:"An error occurred while updating the selection rule"},{status:500})}}async function m(e,{params:r}){try{let e=await (0,a.getServerSession)(d.N);if(!e)return u.NextResponse.json({message:"Unauthorized"},{status:401});let{id:s,ruleId:t}=await r;if(!await l.db.quiz.findUnique({where:{id:s,creatorId:e.user.id}}))return u.NextResponse.json({message:"Quiz not found or you don't have permission to edit it"},{status:404});if(!await l.db.selectionRule.findUnique({where:{id:t,quizId:s}}))return u.NextResponse.json({message:"Selection rule not found"},{status:404});return await l.db.selectionRule.delete({where:{id:t}}),u.NextResponse.json({message:"Selection rule deleted successfully"},{status:200})}catch(e){return console.error("Error deleting selection rule:",e),u.NextResponse.json({message:"An error occurred while deleting the selection rule"},{status:500})}}let w=new i.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/quizzes/[id]/rules/[ruleId]/route",pathname:"/api/quizzes/[id]/rules/[ruleId]",filename:"route",bundlePath:"app/api/quizzes/[id]/rules/[ruleId]/route"},resolvedPagePath:"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/app/api/quizzes/[id]/rules/[ruleId]/route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:g,workUnitAsyncStorage:x,serverHooks:h}=w;function f(){return(0,o.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:x})}},96487:()=>{},78335:()=>{},37702:(e,r,s)=>{"use strict";s.d(r,{N:()=>l});var t=s(46814),i=s(91642),n=s(28053),o=s(7553),u=s(5486),a=s.n(u),d=s(27914);let l={adapter:(0,t.y)(d.db),session:{strategy:"jwt"},pages:{signIn:"/auth/login",signOut:"/auth/logout",error:"/auth/error"},providers:[(0,n.A)({clientId:process.env.GOOGLE_CLIENT_ID||"",clientSecret:process.env.GOOGLE_CLIENT_SECRET||""}),(0,o.A)({clientId:process.env.GITHUB_CLIENT_ID||"",clientSecret:process.env.GITHUB_CLIENT_SECRET||""}),(0,i.A)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)return null;let r=await d.db.user.findUnique({where:{email:e.email}});return r&&r.password&&await a().compare(e.password,r.password)?{id:r.id,name:r.name,email:r.email,image:r.image,role:r.role}:null}})],callbacks:{session:async({session:e,token:r})=>(r&&(e.user.id=r.id,e.user.name=r.name,e.user.email=r.email,e.user.image=r.picture,e.user.role=r.role),e),async jwt({token:e,user:r}){let s=await d.db.user.findFirst({where:{email:e.email}});return s?{id:s.id,name:s.name,email:s.email,picture:s.image,role:s.role}:(r&&(e.id=r.id),e)}},secret:process.env.NEXTAUTH_SECRET}},27914:(e,r,s)=>{"use strict";s.d(r,{db:()=>i});var t=s(96330);let i=globalThis.prisma||new t.PrismaClient}};var r=require("../../../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[638,263,452],()=>s(12618));module.exports=t})();