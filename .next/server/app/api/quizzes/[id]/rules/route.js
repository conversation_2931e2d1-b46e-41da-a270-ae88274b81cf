(()=>{var e={};e.id=309,e.ids=[309],e.modules={96330:e=>{"use strict";e.exports=require("@prisma/client")},5486:e=>{"use strict";e.exports=require("bcrypt")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},12412:e=>{"use strict";e.exports=require("assert")},79428:e=>{"use strict";e.exports=require("buffer")},55511:e=>{"use strict";e.exports=require("crypto")},94735:e=>{"use strict";e.exports=require("events")},81630:e=>{"use strict";e.exports=require("http")},55591:e=>{"use strict";e.exports=require("https")},11723:e=>{"use strict";e.exports=require("querystring")},79551:e=>{"use strict";e.exports=require("url")},28354:e=>{"use strict";e.exports=require("util")},74075:e=>{"use strict";e.exports=require("zlib")},95906:(e,r,s)=>{"use strict";s.r(r),s.d(r,{patchFetch:()=>q,routeModule:()=>m,serverHooks:()=>g,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>w});var t={};s.r(t),s.d(t,{GET:()=>c,POST:()=>p});var i=s(42706),a=s(28203),n=s(45994),o=s(39187),u=s(51825),d=s(37702),l=s(27914);async function c(e,{params:r}){try{let e=await (0,u.getServerSession)(d.N),s=await r.id,t=await l.db.quiz.findUnique({where:{id:s}});if(!t)return o.NextResponse.json({message:"Quiz not found"},{status:404});if(!t.isPublished&&(!e||t.creatorId!==e.user.id))return o.NextResponse.json({message:"Unauthorized"},{status:401});let i=await l.db.selectionRule.findMany({where:{quizId:s},orderBy:{createdAt:"asc"}});return o.NextResponse.json(i)}catch(e){return console.error("Error fetching selection rules:",e),o.NextResponse.json({message:"An error occurred while fetching selection rules"},{status:500})}}async function p(e,{params:r}){try{let s=await (0,u.getServerSession)(d.N);if(!s)return o.NextResponse.json({message:"Unauthorized"},{status:401});let t=await r.id,i=await e.json();if(!await l.db.quiz.findUnique({where:{id:t,creatorId:s.user.id}}))return o.NextResponse.json({message:"Quiz not found or you don't have permission to edit it"},{status:404});let a=await l.db.questionPool.findUnique({where:{id:i.poolId,quizId:t}});if(!a)return o.NextResponse.json({message:"Pool not found"},{status:404});let n=await l.db.selectionRule.create({data:{...i,quizId:t,poolId:a.poolId}});return o.NextResponse.json(n,{status:201})}catch(e){return console.error("Error creating selection rule:",e),o.NextResponse.json({message:"An error occurred while creating the selection rule"},{status:500})}}let m=new i.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/quizzes/[id]/rules/route",pathname:"/api/quizzes/[id]/rules",filename:"route",bundlePath:"app/api/quizzes/[id]/rules/route"},resolvedPagePath:"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/app/api/quizzes/[id]/rules/route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:x,workUnitAsyncStorage:w,serverHooks:g}=m;function q(){return(0,n.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:w})}},96487:()=>{},78335:()=>{},37702:(e,r,s)=>{"use strict";s.d(r,{N:()=>l});var t=s(46814),i=s(91642),a=s(28053),n=s(7553),o=s(5486),u=s.n(o),d=s(27914);let l={adapter:(0,t.y)(d.db),session:{strategy:"jwt"},pages:{signIn:"/auth/login",signOut:"/auth/logout",error:"/auth/error"},providers:[(0,a.A)({clientId:process.env.GOOGLE_CLIENT_ID||"",clientSecret:process.env.GOOGLE_CLIENT_SECRET||""}),(0,n.A)({clientId:process.env.GITHUB_CLIENT_ID||"",clientSecret:process.env.GITHUB_CLIENT_SECRET||""}),(0,i.A)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)return null;let r=await d.db.user.findUnique({where:{email:e.email}});return r&&r.password&&await u().compare(e.password,r.password)?{id:r.id,name:r.name,email:r.email,image:r.image,role:r.role}:null}})],callbacks:{session:async({session:e,token:r})=>(r&&(e.user.id=r.id,e.user.name=r.name,e.user.email=r.email,e.user.image=r.picture,e.user.role=r.role),e),async jwt({token:e,user:r}){let s=await d.db.user.findFirst({where:{email:e.email}});return s?{id:s.id,name:s.name,email:s.email,picture:s.image,role:s.role}:(r&&(e.id=r.id),e)}},secret:process.env.NEXTAUTH_SECRET}},27914:(e,r,s)=>{"use strict";s.d(r,{db:()=>i});var t=s(96330);let i=globalThis.prisma||new t.PrismaClient}};var r=require("../../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[638,263,452],()=>s(95906));module.exports=t})();