(()=>{var e={};e.id=612,e.ids=[612],e.modules={96330:e=>{"use strict";e.exports=require("@prisma/client")},5486:e=>{"use strict";e.exports=require("bcrypt")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},41281:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>m,routeModule:()=>l,serverHooks:()=>h,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>x});var s={};t.r(s),t.d(s,{POST:()=>c});var a=t(42706),i=t(28203),n=t(45994),u=t(39187),o=t(5486),p=t.n(o),d=t(27914);async function c(e){try{let{name:r,email:t,password:s}=await e.json();if(!r||!t||!s)return u.NextResponse.json({message:"Missing required fields"},{status:400});if(await d.db.user.findUnique({where:{email:t}}))return u.NextResponse.json({message:"User with this email already exists"},{status:409});let a=await p().hash(s,10),{password:i,...n}=await d.db.user.create({data:{name:r,email:t,password:a}});return u.NextResponse.json({message:"User registered successfully",user:n},{status:201})}catch(e){return console.error("Registration error:",e),u.NextResponse.json({message:"An error occurred during registration"},{status:500})}}let l=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/auth/register/route",pathname:"/api/auth/register",filename:"route",bundlePath:"app/api/auth/register/route"},resolvedPagePath:"/Users/<USER>/Documents/augment-projects/hacking-quiz/src/app/api/auth/register/route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:g,workUnitAsyncStorage:x,serverHooks:h}=l;function m(){return(0,n.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:x})}},96487:()=>{},78335:()=>{},27914:(e,r,t)=>{"use strict";t.d(r,{db:()=>a});var s=t(96330);let a=globalThis.prisma||new s.PrismaClient}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[638,452],()=>t(41281));module.exports=s})();