// MongoDB initialization script
// This script runs when the MongoDB container starts for the first time

// Switch to the quizflow database
db = db.getSiblingDB('quizflow');

// Create a user for the quizflow database
db.createUser({
  user: 'quizflow_user',
  pwd: 'quizflow_password',
  roles: [
    {
      role: 'readWrite',
      db: 'quizflow'
    }
  ]
});

// Create initial collections (optional, <PERSON><PERSON><PERSON> will create them)
db.createCollection('User');
db.createCollection('Quiz');
db.createCollection('Question');
db.createCollection('UserResponse');

print('MongoDB initialized for QuizFlow application');
